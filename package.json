{"name": "eternia", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@reduxjs/toolkit": "^1.9.5", "apexcharts": "^3.45.1", "axios": "^1.4.0", "bootstrap": "^5.3.1", "formik": "^2.4.3", "history": "^5.3.0", "lucide-react": "^0.469.0", "moment": "^2.29.4", "radium": "^0.26.2", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-avatar": "^5.0.3", "react-countup": "^6.5.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^18.2.0", "react-email-editor": "^1.7.9", "react-icons": "^5.2.1", "react-images-viewer": "^1.7.1", "react-infinite-scroll-component": "^6.1.0", "react-intl": "^2.9.0", "react-otp-input": "^3.0.4", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-simple-image-viewer": "^1.2.2", "react-table": "^6.11.5", "react-table-hoc-fixed-columns": "^2.3.4", "reactstrap": "^9.2.0", "redux": "^4.2.1", "redux-persist": "^6.0.0", "styled-components": "^6.0.7", "styled-theme": "^0.3.3", "sweetalert2": "^11.7.27", "video.js": "^8.21.0", "yup": "^1.2.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "sass": "^1.66.1", "vite": "^4.4.5"}}