# Eternia CMS

## Getting Started

### Prerequisites

-   1.node version - **18.17.1**
-   2.vite version - **4.4.5**
-   3.react version - **18.2.0**

## Installation

Install Eternia with npm package manager:

Install Dependencies:

```bash
npm install --force
```

Run App:

```bash
npm start
```

## Support

For support, email <EMAIL>.

# React + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

-   [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
-   [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh
