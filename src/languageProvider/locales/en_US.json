{"sidebar.changepassword": "Change password ", "sidebar.pages": "Pages", "sidebar.dashboard": "Dashboard", "sidebar.staticpage": "Static Content Management", "sidebar.staticpages": "Static Pages", "sidebar.skills": "Skills", "sidebar.supervisor": "Supervisor", "sidebar.fabricator": "Fabricator", "sidebar.assignFabricator": "Assign Fabricator", "sidebar.registerUser": "Registered User", "sidebar.verifiedUser": "Verified User", "sidebar.rejectedUser": "Rejected User", "sidebar.internalTeam": "Eternia Team", "sidebar.workOrderType": "Work Order Type", "sidebar.orderManagement": "Order Management", "sidebar.orderDetails": "Order Details", "sidebar.taskDetails": "Task Details", "sidebar.handoverQuestions": "Handover Questions", "sidebar.questions": "Questions", "sidebar.questionTags": "Question Tags", "sidebar.installationSteps": "Installation", "sidebar.questionBank": "Question Bank", "sidebar.questionMapping": "Question Mapping", "sidebar.addQuestionMapping": "Add Question Mapping", "sidebar.editQuestionMapping": "Edit Question Mapping", "sidebar.videoTraining": "Video Training", "sidebar.feedback": "<PERSON><PERSON><PERSON>", "sidebar.role": "Role", "sidebar.group": "Group", "sidebar.userCreation": "User Creation", "sidebar.permissions": "Permissions", "sidebar.create": "Create Permission", "sidebar.assign": "Assign Permission", "sidebar.points": "Points", "sidebar.reports": "User Reports", "sidebar.assignPoints": "Assign Points", "sidebar.assignPointsDetails": "Assign Points Details", "sidebar.defineRate": "Define Rate", "sidebar.addressApprove": "Address Approval", "sidebar.internalApproved": "Approved", "sidebar.internalRegistered": "Registered", "sidebar.internalRejected": "Rejected", "sidebar.authentication": "Authentication", "sidebar.login": "<PERSON><PERSON>", "sidebar.forms": "Forms", "sidebar.regularforms": "Regular Forms", "sidebar.masterData": "Master Data", "sidebar.tat": "TAT", "sidebar.settings": "Settings", "sidebar.rejectReason": "Reject Reasons", "sidebar.space": "Spaces", "sidebar.scope": "<PERSON><PERSON><PERSON>", "sidebar.templates": "Templates", "sidebar.email": "Email", "sidebar.walletLogs": "Wallet Logs", "sidebar.notifications": "Notifications", "title.internalTeamManagement": "Eternia Team", "themeChanger.sidebar": "Sidebar", "themeChanger.footer": "Footer", "themeChanger.background": "Background", "themeChanger.topbar": "Topbar", "themeSetting.sidebartransparent": "Sidebar Transparent", "themeSetting.colorSchema": "Color Scheme", "themeSetting.sidebarmini": "Sidebar Mini", "header.editprofile": "Edit Profile", "header.settings": "Settings", "header.inbox": "Inbox", "header.signout": "Logout", "action.add": "Add", "action.edit": "Edit", "eternia_fabricator_agreement": "Eternia - Fabricator Agreement", "llp_certificate": "LLP Certificate", "certificate_of_incorporation": "Certificate of Incorporation", "pan_card": "Pan Card", "partnership_deed": "Partnership Deed", "identification_document": "Identification Document", "sidebar.zoneWiseHierarchy": "Zone Wise Hierarchy"}