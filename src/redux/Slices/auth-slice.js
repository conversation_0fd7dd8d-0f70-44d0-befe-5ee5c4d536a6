import { createSlice } from '@reduxjs/toolkit';

const initialState = {
	isLogin: false,
	accessToken: null,
	firstName: null,
	lastName: null,
	email: null,
	key: null,
	profile_image: null,
	modules: null,
	role: null,
	country_code: null,
	mobile: null,
};

const authSlice = createSlice({
	name: 'auth',
	initialState,
	reducers: {
		logout(state) {
			state.isLogin = false;
			state.firstName = null;
			state.lastName = null;
			state.accessToken = null;
			state.email = null;
			state.id = null;
			state.role = null;
		},
		login(state, action) {
			state.isLogin = action.payload.isLogin;
			state.accessToken = action.payload.accessToken;
			state.firstName = action.payload.firstName;
			state.lastName = action.payload.lastName;
			state.email = action.payload.email;
			state.role = action.payload.role;
			state.country_code = action.payload.country_code;
			state.mobile = action.payload.mobile;
			state.id = action.payload.id;
		},
		admin(state, action) {
			state.firstName = action.payload.firstName;
			state.lastName = action.payload.lastName;
			state.email = action.payload.email;
			state.profile_image = action.payload.profile_image;
			state.role = action.payload.role;
		}
	}
});

export const { logout, login, admin } = authSlice.actions;

export default authSlice.reducer;
