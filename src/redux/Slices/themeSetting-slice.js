import { createSlice } from '@reduxjs/toolkit';

const initialState = {
	toolbarAlignValue: 'below', // below or above
	footerAlignValue: 'below', // below or above
	toolbarDisplayValue: 'show', // show or hide
	footerDisplayValue: 'show', // show or hide
	sidebarTransParentValue: 'off', // off or on
	activeLinkStyle: 'style1', // style1, style2, style3, style4, style5
	sidebarMiniValue: 'off', // off or on
	sidebarTransParentActiveBack: 'hsla(0,0%,100%,.23)', // when you use transparentImage at that time you can put any back color for active link
	sidebarTransParentActiveColor: '#E91E63' // when you use transparentImage at that time you can put any text color for active link
};

const themeSettingSlice = createSlice({
	name: 'themeSetting',
	initialState,
	reducers: {
		toolbarAlignment(state, action) {
			state.toolbarAlign = action.payload;
		},
		footerAlignment(state, action) {
			state.footerAlign = action.payload;
		},
		sidebarDisplay(state, action) {
			state.sidebarDisplay = action.payload;
		},
		toolbarDisplay(state, action) {
			state.toolbarDisplay = action.payload;
		},
		footerDisplay(state, action) {
			state.footerDisplay = action.payload;
		},
		sidebarTransParent(state, action) {
			state.sidebarTransParent = action.payload;
		},
		triggerTransparetImage(state, action) {
			state.transparentImage = action.payload;
		},
		triggerActiveLinkStyle(state, action) {
			state.activeLinkStyle = action.payload;
		},
		sidebarMini(state, action) {
			state.sidebarMiniValue = action.payload;
		}
	}
});

export const {
	toolbarAlignment,
	footerAlignment,
	sidebarDisplay,
	toolbarDisplay,
	footerDisplay,
	sidebarTransParent,
	triggerTransparetImage,
	triggerActiveLinkStyle,
	sidebarMini
} = themeSettingSlice.actions;

export default themeSettingSlice.reducer;
