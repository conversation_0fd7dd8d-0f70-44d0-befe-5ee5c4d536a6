import { createSlice } from '@reduxjs/toolkit';
import config, { getCurrentTheme } from 'src/settings/themeConfig';

const initialState = {
	isActivated: false,
	changeThemes: getCurrentTheme(
		'changeThemes',
		config.changeThemes.defaultTheme || 'themeDefault'
	),
	topbarTheme: getCurrentTheme('topbarTheme', config.topbarTheme.defaultTheme || 'themeDefault'),
	sidebarTheme: getCurrentTheme(
		'sidebarTheme',
		config.sidebarTheme.defaultTheme || 'themeDefault'
	),
	layoutTheme: getCurrentTheme('layoutTheme', config.layoutTheme.defaultTheme || 'themeDefault'),
	footerTheme: getCurrentTheme('footerTheme', config.footerTheme.defaultTheme || 'themeDefault')
};

const authSlice = createSlice({
	name: 'themeChanger',
	initialState,
	reducers: {
		changeTheme: (state, action) => {
			const theme = getCurrentTheme(action.payload.attribute, action.payload.themeName);

			state[action.payload.attribute] = theme;
		}
	}
});

export const { logout, login, admin } = authSlice.actions;

export default authSlice.reducer;
