// SingleDropdown custom for (most dropdown's.)
import React, { useState } from 'react';
import { Dropdown, DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';

const SingleDropdown = ({ data, keyProps, onSelect, selectedData, className, disabled }) => {
	const [dropdownOpen, setDropdownOpen] = useState(false);

	const toggleDropdown = () => {
		setDropdownOpen(!dropdownOpen);
	};

	const handleItemClick = (item) => {
		if (onSelect) {
			onSelect(item);
		}
		setDropdownOpen(false);
	};

	const getDisplayLabel = (item) => {
		return keyProps.map((keyProp) => item[keyProp] || item.title).join(' - ');
	};

	return (
		<Dropdown
			className={`mt-0 bg-blue ${disabled ? 'cursor-disabled opacity-50' : ''}`}
			isOpen={dropdownOpen}
			disabled={disabled}
			toggle={toggleDropdown}>
			<DropdownToggle
				caret
				className={`roleBtn text-truncate mw-100 ${className ? className : ''}`}>
				{selectedData ? getDisplayLabel(selectedData) : 'Select'}
			</DropdownToggle>

			<DropdownMenu className="w-100">
				{data.length > 0 ? (
					data?.map((item, index) => (
						<DropdownItem
							key={index}
							onClick={() => {
								handleItemClick(item);
							}}
							className="text-truncate">
							{keyProps.map((keyProp) => item[keyProp] || item.title).join(' - ')}
						</DropdownItem>
					))
				) : (
					<DropdownItem disabled>No Data</DropdownItem>
				)}
			</DropdownMenu>
		</Dropdown>
	);
};

export default SingleDropdown;
