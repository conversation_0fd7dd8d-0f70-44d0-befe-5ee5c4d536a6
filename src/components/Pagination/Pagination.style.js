import styled from 'styled-components';

const PaginationWrapper = styled.div`
	display: flex;
	justify-content: flex-end;
	padding: 20px 10px 0px;

	.Table__pageButton {
		max-width: 100px;
		outline: none;
		border: none;
		background-color: transparent;
		cursor: pointer;
		color: #757575 !important;
		margin: 0 5px;

		img {
			width: 18px;
			z-index: -1;

			&.inverse {
				transform: rotate(180deg);
			}

			&.disabled {
				cursor: not-allowed;
			}
		}
	}

	.Table__pageButton:disabled {
		cursor: not-allowed;
		color: gray;
	}

	.Table__pageButton--active {
		font-weight: bold;
		width: 30px;
		height: 30px;
		border-radius: 6px;
	}
`;

export default PaginationWrapper;
