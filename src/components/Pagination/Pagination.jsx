import React from 'react';
import PropTypes from 'prop-types';
import <PERSON><PERSON><PERSON><PERSON> from '../common/RoyTooltip';
// STYLES
import PaginationWrapper from './Pagination.style';
// ASSETS
import BackwardIcon from 'src/assets/images/Backward.svg';
import { TABLE } from 'src/helper/constant';

const defaultButton = (props) => <button {...props}>{props.children}</button>;

export default class Pagination extends React.Component {
	constructor(props) {
		super();

		this.changePage = this.changePage.bind(this);

		this.state = {
			visiblePages: this.getVisiblePages(null, props.pages)
		};
	}

	static propTypes = {
		pages: PropTypes.number,
		page: PropTypes.number,
		PageButtonComponent: PropTypes.any,
		onPageChange: PropTypes.func,
		previousText: PropTypes.string,
		nextText: PropTypes.string
	};

	componentWillReceiveProps(nextProps) {
		if (this.props.pages !== nextProps.pages) {
			this.setState({
				visiblePages: this.getVisiblePages(null, nextProps.pages)
			});
		}

		// this.changePage(nextProps.page + 1);
	}

	filterPages = (visiblePages, totalPages) => {
		return visiblePages.filter((page) => page <= totalPages);
	};

	getVisiblePages = (page, total) => {
		if (total < 7) {
		  return this.filterPages([1, 2, 3, 4, 5, 6], total); // Start from 1
		} else {
		  if (page % 5 >= 0 && page > 4 && page + 2 < total) {
			return [1, page - 1, page, page + 1, total]; // Start from 1
		  } else if (page % 5 >= 0 && page > 4 && page + 2 >= total) {
			return [1, total - 3, total - 2, total - 1, total]; // Start from 1
		  } else {
			return [1, 2, 3, 4, 5, total]; // Start from 1
		  }
		}
	  };
	  

	changePage(page) {
		const activePage = this.props.page + 1;

		if (page === activePage) {
			return;
		}

		const visiblePages = this.getVisiblePages(page, this.props.pages);

		this.setState({
			visiblePages: this.filterPages(visiblePages, this.props.pages)
		});

		this.props.onPageChange(page - 1);
	}

	render() {
		const { PageButtonComponent = defaultButton, totalCount } = this.props;
		const { visiblePages } = this.state;
		const activePage = this.props.page + 1;

		const recordsPerPage = TABLE.LIMIT; // Change this to match your records per page
		const totalRecords = totalCount; // Use the totalCount from props

		// Calculate the range of records to be displayed
		const startRecord = (activePage - 1) * recordsPerPage + 1;
		const endRecord = Math.min(activePage * recordsPerPage, totalRecords);
	  

		return (
			<PaginationWrapper>
				<div className="total-records me-auto">
					{totalCount ? `${startRecord}-${endRecord} out of ${totalRecords} records` : null}
				</div>
				<div className="Table__prevPageWrapper">
					<RoyTooltip id={`previous`} title={'Previous'} placement="left" disabled={activePage === 1}>
						<div id={`previous`}>
							<PageButtonComponent
								className="Table__pageButton"
								onClick={() => {
									if (activePage === 1) return;
									this.changePage(activePage - 1);
								}}
								disabled={activePage === 1}>
								<img
									src={BackwardIcon}
									className={`cursor-pointer ${
										activePage === 1 ? 'disabled' : ''
									}`}
								/>
							</PageButtonComponent>
						</div>
					</RoyTooltip>
				</div>
				<div className="Table__visiblePagesWrapper">
					{visiblePages.map((page, index, array) => {
						return (
							<PageButtonComponent
								key={page}
								className={
									activePage === page
									? 'Table__pageButton Table__pageButton--active'
									: 'Table__pageButton'
								}
								onClick={this.changePage.bind(null, page)}>
								{array[index - 1] + 2 < page ? `...${page}` : page}
							</PageButtonComponent>
						);
					})}
				</div>
				<div className="Table__nextPageWrapper">
					<RoyTooltip id={`next`} title={'Next'} placement="right" disabled={activePage === this.props.pages}>
						<div id={`next`}>
							<PageButtonComponent
								className="Table__pageButton"
								onClick={() => {
									if (activePage === this.props.pages) return;
									this.changePage(activePage + 1);
								}}
								disabled={activePage === this.props.pages}>
								<img
									src={BackwardIcon}
									className={`cursor-pointer inverse ${
										activePage === this.props.pages ? 'disabled' : ''
									}`}
								/>
							</PageButtonComponent>
						</div>
					</RoyTooltip>
				</div>
			</PaginationWrapper>
		);
	}
}
