import React, { useState, useEffect } from 'react';
import { Input } from 'reactstrap';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import ReactTable from 'react-table';

import { TABLE } from '../../helper/constant';
import Pagination from '../Pagination/Pagination';
import Loader from './Loader';
import TableWrapper from './Table.style';
import Button from '../button/Button';

let debounceVar;
const Table = (props) => {
	const [searchVal, setSearchVal] = useState('');
	const [activePage, setActivePage] = useState(0);
	const [pages, setPages] = useState(0);

	useEffect(() => {
		props.rows.length && setPages(Math.ceil(props.rows.length / TABLE.LIMIT));
	}, [props.rows]);

	const handleSearch = (e) => {
		e.persist();
		setSearchVal(e.target.value);
		setActivePage(0);

		debounceVar && clearTimeout(debounceVar);

		if (props.setSearch) {
			debounceVar = setTimeout(() => props.setSearch(e.target.value), [1000]);
		}
	};

	const handlePageChange = (pageIndex) => {
		setActivePage(pageIndex);
		props.setParam({
			...props.param,
			pageNumber: pageIndex + 1
		});
	};

	const handleSortChange = (sortProperties) => {
		props.setParam({
			...props.param,
			orderBy: sortProperties[0].desc ? 'DESC' : 'ASC',
			order: sortProperties[0].id
		});

		setActivePage(0);
	};

	return (
		<TableWrapper {...props}>
			<div className="plr-15">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-header module-header justify-content-between">
						<div className="mr-10">
							<Input
								value={searchVal}
								onChange={handleSearch}
								type="text"
								placeholder="Search..."
								className="fs-14 medium-text plr-10 form-control react-form-input"
							/>
						</div>

						<div>
							<Button onClick={props.toggleAdd} className="btn form-button">
								ADD
							</Button>
						</div>
					</div>

					<div className="roe-card-body">
						<ReactTable
							style={{ border: 'none', boxShadow: 'none' }}
							data={props.rows}
							columns={props.columns}
							defaultPageSize={TABLE.LIMIT}
							minRows={TABLE.MIN_ROW}
							filterable
							manual
							defaultFilterMethod={(filter, row) => {
								const id = filter.pivotId || filter.id;
								return row[id] !== undefined
									? String(row[id].toLowerCase()).includes(
											filter.value.toLowerCase()
									  )
									: true;
							}}
							className="-striped -highlight custom-react-table-theme-class"
							pages={pages}
							page={activePage}
							PaginationComponent={Pagination}
							loading={props.loading}
							LoadingComponent={Loader}
							onPageChange={(pageIndex) => handlePageChange(pageIndex)}
							onSortedChange={(sortProperties) => handleSortChange(sortProperties)}
						/>
					</div>
				</div>
			</div>
		</TableWrapper>
	);
};

// define props validation
Table.propTypes = {
	rows: PropTypes.array.isRequired,
	columns: PropTypes.array.isRequired,
	setSearch: PropTypes.func.isRequired,
	toggleAdd: PropTypes.func.isRequired,
	param: PropTypes.object.isRequired,
	setParam: PropTypes.func.isRequired
};

const mapStateToProps = (state) => {
	return {
		...state.themeChanger
	};
};

export default connect(mapStateToProps, null)(Table);
