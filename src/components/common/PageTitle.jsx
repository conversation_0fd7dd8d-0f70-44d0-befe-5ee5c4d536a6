/* eslint-disable react/prop-types */
import { Fragment } from 'react';
import { connect } from 'react-redux';
import { Breadcrumb, BreadcrumbItem } from 'reactstrap';
import classNames from 'classnames';

import IntlMessages from '../../util/intlMessages';

import crossIcon from '../../assets/images/cross.svg';
import searchIcon from '../../assets/images/search.svg';

const PageTitle = ({
	title,
	sidebarTheme,
	className,
	breadCrumb,
	searchKey,
	search,
	setSearchKey
}) => {
	const titleStyle = {
		background: sidebarTheme.activeColor,
		color: sidebarTheme.textColorNew,
		boxShadow: `0 5px 20px 0 rgba(0,0,0,.2), 0 13px 24px -11px ${sidebarTheme.activeColor}`
	};

	const titleColor = { color: sidebarTheme.activeColor };

	return (
		<Fragment>
			<div
				className={classNames('d-flex align-items-center', {
					'justify-content-between': search
				})}>
				<div className={classNames('Page-title', 'mtb-15', className)}>
					<div className="title-icon" style={titleStyle}>
						{title.substr(title.indexOf('.') + 1).substring(0, 2)}
					</div>
					<div style={titleColor} className="title">
						<IntlMessages id={title} />
					</div>
				</div>
				{breadCrumb && (
					<div>
						<Breadcrumb className="custom-breadcumb">
							{breadCrumb &&
								breadCrumb.map((e, i) => {
									if (i === breadCrumb.length - 1) {
										return (
											<BreadcrumbItem key={i} active>
												<IntlMessages id={e.name} />
											</BreadcrumbItem>
										);
									} else {
										return (
											<BreadcrumbItem className="breadcumb-color" key={i}>
												<IntlMessages id={e.name} />
											</BreadcrumbItem>
										);
									}
								})}
						</Breadcrumb>
					</div>
				)}
				{search && (
					<div className="ps-3">
						<form
							className="w-100 position-relative search-system-users-form"
							autoComplete="off">
							{!searchKey && (
								<img
									src={searchIcon}
									className="svg-icon-2 svg-icon-gray-500 position-absolute top-50 translate-middle-y ms-3 cursor-pointer"
									style={{ right: '7px', width: '23px', height: '23px' }}></img>
							)}

							<input
								type="text"
								className="form-control ps-10 search-users-input padding-start-40 min-w-250"
								name="search"
								onChange={(e) => setSearchKey(e.target.value)} // C
								placeholder="Search"
								value={searchKey}
								autoComplete="off"
							/>

							{searchKey ? (
								<span
									className={`btn btn-flush position-absolute top-50 end-0 translate-middle-y lh-0`}
									data-kt-search-element="clear"
									onClick={() => setSearchKey('')}>
									<img
										src={crossIcon}
										style={{ right: '7px' }}
										className="svg-icon-2 svg-icon-gray-500 position-absolute top-50 translate-middle-y ms-3"></img>
								</span>
							) : (
								''
							)}
						</form>
					</div>
				)}
			</div>
		</Fragment>
	);
};

// PageTitle.propTypes = {
// 	title: PropTypes.string.isRequired
// };

const mapStateToProps = (state) => {
	return { ...state.themeChanger };
};

export default connect(mapStateToProps, null)(PageTitle);
