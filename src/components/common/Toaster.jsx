import { Component } from 'react';
// COMPONENTS
import CustomToast from '../../components/notification/CustomToast';
// REDUX
import { store } from '../../redux/store';

class Toaster extends Component {
	state = { editedData: null, toast: false, toastify: {} };

	error = (error) => {
		if (error && error.data) {
			this.setState({
				toast: true,
				toastify: { message: error.data.message, heading: 'Error', styleClass: 'c-danger' }
			});
		} else {
			this.setState({
				toast: true,
				toastify: { message: error, heading: 'Error', styleClass: 'c-danger' }
			});
		}

		setTimeout(() => {
			this.setState({ toast: false });
		}, 4000);
	};

	success = (message) => {
		this.setState({
			toast: true,
			toastify: { message: message, heading: 'Success', styleClass: 'c-info' }
		});

		setTimeout(() => {
			this.setState({ toast: false });
		}, 4000);
	};

	info = (message) => {
		this.setState({
			toast: true,
			toastify: { message: message, heading: 'Info', styleClass: 'c-secondary' }
		});

		setTimeout(() => {
			this.setState({ toast: false });
		}, 4000);
	};

	render() {
		const { toastify, toast } = { ...this.state };

		return (
			<CustomToast
				width={400}
				show={toast}
				transition={true}
				position="top-right"
				className={toastify && toastify.styleClass}
				message={toastify && toastify.message}
				heading={toastify && toastify.heading}
				onCloseCLick={() => this.setState({ toast: false })}
			/>
		);
	}
}

export default Toaster;
