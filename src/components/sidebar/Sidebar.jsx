import { Fragment } from 'react';
import SidebarWrapper from './sidebar.style';
import NavList from './NavList';
import { sidebarData } from '../../util/data/sidebar';
import { APP_NAME } from '../../helper/constant';
import { Scrollbars } from 'react-custom-scrollbars';
import IntlMessages from '../../util/intlMessages';
import favicon from 'src/assets/images/logo-solo.svg';

const Sidebar = (props) => {
	let listNameStyle;
	let sidebar;
	let sideScrollStyle;

	const { mini, drawerWidth, miniDrawerWidth, sidebarTheme, themeSetting, closeDrawer } = props;

	if (themeSetting.toolbarAlignValue === '    ' && themeSetting.footerAlignValue === 'above')
		sideScrollStyle = { height: 'calc(100vh - 190px)' };
	else if (themeSetting.toolbarAlignValue === 'above')
		sideScrollStyle = { height: 'calc(100vh - 145px)' };
	else if (themeSetting.footerAlignValue === 'above')
		sideScrollStyle = { height: 'calc(100vh - 128px)' };
	else sideScrollStyle = { height: 'calc(100vh - 75px)' };

	if (themeSetting.sidebarTransParentValue === 'on') {
		sidebar = {
			backgroundImage: `linear-gradient(0deg,rgba(0, 0, 0, 0.8),rgba(0, 0, 0, 0.9)),url(${themeSetting.transparentImage})`,
			backgroundRepeat: 'no-repeat, repeat',
			backgroundPosition: 'center',
			backgroundSize: 'cover',
			width: mini ? miniDrawerWidth : drawerWidth,

			'@media (max-width: 767.98px)': { width: mini ? 0 : drawerWidth }
		};
	} else {
		sidebar = {
			width: mini ? miniDrawerWidth : drawerWidth,
			background: sidebarTheme.backgroundColor,

			'@media (max-width: 767.98px)': { width: mini ? 0 : drawerWidth }
		};
	}

	const closeIcon = {
		'@media (maxWidth: 769px)': { display: 'block' }
	};

	if (mini) {
		listNameStyle = {
			opacity: miniDrawerWidth === drawerWidth ? 1 : 0,
			transform: miniDrawerWidth === drawerWidth ? 'translateZ(0)' : 'translate3d(-25px,0,0)'
		};
	} else {
		listNameStyle = {
			opacity: !mini ? 1 : 0,
			transform: !mini ? 'translateZ(0)' : 'translate3d(-25px,0,0)'
		};
	}

	return (
		<SidebarWrapper
			themeSetting={themeSetting}
			sidebarTheme={sidebarTheme}
			mini={mini}
			miniDrawerWidth={miniDrawerWidth}
			drawerWidth={drawerWidth}>
			<div id="sidebar" className="sidebar sideBack" style={sidebar}>
				<div className="sidebar-header">
					<div className="logo-img simple-text logo-mini">
						<img src={favicon} alt="react-logo" />
					</div>
					{!mini && <div className="logo-text simple-text">{APP_NAME}</div>}
				</div>
				<div className="close-drawer-icon" style={closeIcon} onClick={closeDrawer}>
					<i className="fas fa-times-circle" />
				</div>
				<div className="dropdown-divider"></div>
				<Scrollbars
					autoHide
					style={sideScrollStyle}
					renderThumbVertical={({ ...props }) => (
						<div {...props} className="sidebar-scrollbar-style" />
					)}
					renderThumbHorizontal={({ ...props }) => <div {...props} />}
					renderTrackVertical={({ style, ...props }) => (
						<div
							{...props}
							style={{
								...style,
								zIndex: 5,
								position: 'absolute',
								width: '6px',
								right: '2px',
								bottom: '2px',
								top: '2px',
								borderRadius: '3px'
							}}
						/>
					)}>
					<div className={`sidebar-wrapper ${mini ? 'mini' : ''}`}>
						<ul className="nav">
							{sidebarData.map((list, i) => {
								return (
									<Fragment key={i}>
										{list.type && list.type === 'heading' ? (
											(!mini || miniDrawerWidth === drawerWidth) && (
												<div className="sidelist-header-name">
													{<IntlMessages id={list.name} />}
												</div>
											)
										) : (
											<NavList
												listNameStyle={listNameStyle}
												list={list}
												mini={mini}
												miniDrawerWidth={miniDrawerWidth}
												drawerWidth={drawerWidth}
												{...props}
											/>
										)}
									</Fragment>
								);
							})}
						</ul>
					</div>
				</Scrollbars>
			</div>
		</SidebarWrapper>
	);
};

export default Sidebar;
