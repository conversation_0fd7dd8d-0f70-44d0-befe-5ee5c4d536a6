export const LOGOUT_POPUP = {
	title: 'Are you sure?',
	text: 'You have to login again!',
	icon: 'warning',
	showCancelButton: true,
	confirmButtonColor: '#1cb4e3',
	cancelButtonColor: '#d33',
	confirmButtonText: 'Yes',
	cancelButtonText: 'No',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const COMPLETED_LOGOUT = {
	title: 'Logout!',
	text: 'You have been logout successfully.',
	icon: 'success',
	confirmButtonColor: '#1cb4e3',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const CONFIRM_APPROVE_POPUP = {
	title: 'Are you sure?',
	text: 'User will be approved!',
	icon: 'warning',
	showCancelButton: true,
	confirmButtonColor: '#1cb4e3',
	cancelButtonColor: '#d33',
	confirmButtonText: 'Yes',
	cancelButtonText: 'No',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const COMPLETED_APPROVE = {
	title: 'Approved!',
	text: 'User has been approved successfully.',
	icon: 'success',
	confirmButtonColor: '#1cb4e3',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const CONFIRM_CHANGE_STATUS = {
	title: 'Are you sure?',
	text: 'Feedback status will be changed!',
	icon: 'warning',
	showCancelButton: true,
	confirmButtonColor: '#1cb4e3',
	cancelButtonColor: '#d33',
	confirmButtonText: 'Yes',
	cancelButtonText: 'No',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const CONFIRM_DELETE = {
	title: 'Are you sure?',
	text: 'This item will be deleted!',
	icon: 'warning',
	showCancelButton: true,
	confirmButtonColor: '#1cb4e3',
	cancelButtonColor: '#d33',
	confirmButtonText: 'Yes',
	cancelButtonText: 'No',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};

export const CONFIRM_ACTIVATE_DELETED_USER = {
	title: 'Are you sure?',
	text: 'Are you sure you want to activate this user as it has been deleted by someone?',
	icon: 'warning',
	showCancelButton: true,
	confirmButtonColor: '#1cb4e3',
	cancelButtonColor: '#d33',
	confirmButtonText: 'Yes',
	cancelButtonText: 'No',
	customClass: {
		title: 'eternia-title-swal',
		htmlContainer: 'eternia-html-swal'
	}
};
