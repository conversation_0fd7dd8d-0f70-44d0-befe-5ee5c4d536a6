import { useRef, useState } from 'react';
import Avatar from 'react-avatar';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Popover, PopoverBody, PopoverHeader } from 'reactstrap';
import Swal from 'sweetalert2';
import { postApi } from '../../helper/api/Api';
import { AUTH } from '../../helper/api/endPoint';
import { APP_NAME, ROUTES } from '../../helper/constant';
import { logout } from '../../redux/Slices/auth-slice';
import IntlMessages from '../../util/intlMessages';
import { COMPLETED_LOGOUT, LOGOUT_POPUP } from './constants';
import HeaderWrapper from './header.style';
import ArrowIcon from 'src/assets/images/Arrow.svg';
import Toaster from '../common/Toaster';

const Header = (props) => {
	const toaster = useRef();
	const navigate = useNavigate();
	const dispatch = useDispatch();

	const { firstName, lastName, profile_image } = useSelector((state) => state.auth);

	const [profilePopover, setProfilePopover] = useState(false);

	const userSignout = () => {
		Swal.fire(LOGOUT_POPUP).then((result) => {
			if (result.isConfirmed) {
				postApi(AUTH.LOGOUT)
					.then((response) => {
						if (response) {
							dispatch(logout());
							Swal.fire(COMPLETED_LOGOUT);
							setTimeout(() => {
								navigate(ROUTES.LOGIN);
							}, 1000);
						} else {
							toaster.current.error(error?.response?.data?.message);
						}
					})
					.catch((error) => {
						if (toaster.current) {
							toaster.current.error(error.message);
							dispatch(logout());
							setTimeout(() => {
								navigate(ROUTES.LOGIN);
							}, 1000);
						}
					});
				return;
			}
		});
	};

	const profilePopovertoggle = () => setProfilePopover(!profilePopover);

	return (
		<>
			<HeaderWrapper {...props}>
				<div className="headerBack">
					<ul className="list-inline ma-0">
						<li className="list-inline-item feed-text-area-icon">
							<div className="drawer-handle-arrow">
								{props.mini ? (
									<button
										className="top-header-icon c-btn"
										onClick={props.drawerMiniMethod}>
										<img src={ArrowIcon} className="icon rotate" alt="" />
									</button>
								) : (
									<button
										className="top-header-icon c-btn"
										onClick={props.drawerMiniMethod}>
										<img src={ArrowIcon} className="icon" alt="" />
									</button>
								)}
							</div>
							<div className="mini-drawer-menu-icon" onClick={props.drawerMiniMethod}>
								<i className="fas fa-bars" />
								<span className="app-name">{APP_NAME}</span>
							</div>
						</li>
						<li className="list-inline-item feed-text-area-icon pull-right">
							<div
								id="profilePopover"
								onClick={profilePopovertoggle}
								className="top-header-profile-class">
								{profile_image ? (
									<img
										src={profile_image}
										alt="loading"
										style={{
											height: '40px',
											width: '40px',
											borderRadius: '50%'
										}}
									/>
								) : (
									<Avatar
										name={`${firstName} ${lastName}`}
										size={36}
										round={true}
									/>
								)}
							</div>
						</li>

						<Popover
							className="language-popover-width"
							placement="bottom"
							isOpen={profilePopover}
							target="profilePopover"
							toggle={profilePopovertoggle}
							popperClassName="left-minus-10"
							trigger="hover">
							<PopoverHeader className="custom-popover-header ">
								<ul className="list-inline ma-0">
									<li className="list-inline-item notification-popover-profile">
										{profile_image ? (
											<img
												src={profile_image}
												alt="loading"
												style={{
													height: '40px',
													width: '40px',
													borderRadius: '50%'
												}}
											/>
										) : (
											<Avatar
												name={`${firstName} ${lastName}`}
												size={36}
												round={true}
											/>
										)}
									</li>
									<li className="list-inline-item notification-popover-profile">
										{`${firstName} ${lastName}`}
									</li>
								</ul>
							</PopoverHeader>
							<PopoverBody className="pa-0">
								<div className="container-fluid grid-popover pb-6 pt-1 px-0">
									<ul
										className="list-inline plr-10 ma-0 language-list-hover"
										onClick={() => {
											navigate(ROUTES.PROFILE.EDIT);
										}}>
										<li className="list-inline-item pa-5">
											<i className="fas fa-user-alt" />
										</li>
										<li className="list-inline-item pa-5">
											<IntlMessages id="header.editprofile" />
										</li>
									</ul>

									<ul
										className="list-inline plr-10 language-list-hover ma-0"
										onClick={() => {
											navigate(ROUTES.PASSWORD.CHANGE);
										}}>
										<li className="list-inline-item pa-5">
											<i className="fas fa-cog" />
										</li>
										<li className="list-inline-item pa-5">
											<div>Change Password</div>
										</li>
									</ul>

									<ul
										className="list-inline plr-10 language-list-hover ma-0"
										onClick={userSignout}>
										<li className="list-inline-item pa-5">
											<i className="fas fa-sign-out-alt" />
										</li>
										<li className="list-inline-item pa-5">
											<IntlMessages id="header.signout" />
										</li>
									</ul>
								</div>
							</PopoverBody>
						</Popover>
						{/* Profile popover */}
					</ul>
				</div>
			</HeaderWrapper>
			<Toaster ref={toaster} />
		</>
	);
};

export default Header;
