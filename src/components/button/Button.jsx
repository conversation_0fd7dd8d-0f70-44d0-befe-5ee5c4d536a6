import React, { useState, Fragment } from 'react';
import { connect } from 'react-redux';
import classNames from 'classnames';

const Button = (props) => {
	const [buttonClick, setButtonCLick] = useState(false);
	const { className, disabled, loading, dataStyle, style, type = 'button' } = props;

	const buttonClickEvent = () => {
		setButtonCLick(true);
		if (props.onClick) {
			props.onClick();
		}
	};

	const getButtonStyle = () => {
		let btnStyle = { ...style };
		if (!btnStyle.backgroundColor) {
			btnStyle.backgroundColor = props.sidebarTheme?.activeColor;
		}
		if (!btnStyle.color) {
			btnStyle.color = props.sidebarTheme?.textColorNew;
		}
		return btnStyle;
	};

	return (
		<Fragment>
			<button
				disabled={disabled}
				type={type}
				onClick={buttonClickEvent}
				// style={style && style}
				style={getButtonStyle()}
				className={classNames(
					className,
					loading && 'loading-button',
					buttonClick && loading && dataStyle,
					buttonClick && loading && 'disabled',
					disabled && 'disable-btn'
				)}>
				<span
					className={classNames(dataStyle === 'middle' && buttonClick && 'label-middle')}>
					{props.children}
				</span>
				{loading && buttonClick && dataStyle === 'expand-left' && (
					<div className="lds-ring loder-left">
						<div />
						<div />
						<div />
						<div />
					</div>
				)}
				{loading && buttonClick && dataStyle === 'expand-right' && (
					<div className="lds-ring loder-right">
						<div />
						<div />
						<div />
						<div />
					</div>
				)}
				{loading && buttonClick && dataStyle === 'expand-up' && (
					<div className="lds-ring loder-middle">
						<div />
						<div />
						<div />
						<div />
					</div>
				)}
				{loading && buttonClick && dataStyle === 'expand-down' && (
					<div className="lds-ring loder-down">
						<div />
						<div />
						<div />
						<div />
					</div>
				)}
				{loading && buttonClick && dataStyle === 'middle' && (
					<div className="lds-ring loder-middle">
						<div />
						<div />
						<div />
						<div />
					</div>
				)}
			</button>
		</Fragment>
	);
};

const mapStateToProps = (state) => {
	return {
		...state.themeChanger
	};
};

export default connect(mapStateToProps, null)(Button);
