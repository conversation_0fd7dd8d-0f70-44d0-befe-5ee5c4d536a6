import styled from 'styled-components';
import { Dropdown } from 'reactstrap';

const DropdownWrapper = styled(Dropdown)`
	.dropdown-btn {
		width: 100%;
		display: flex;
		align-items: center;
		color: #707981 !important;
		border: 1px solid #ced4da;
		justify-content: space-between;
		background-color: unset !important;

		&.filled {
			color: #000 !important;
		}

		&:focus {
			border-color: #80bdff !important;
		}

		&:focus-within {
			color: #000 !important;
			background-color: unset !important;
		}

		&:hover {
			color: #000 !important;
			background-color: unset !important;
		}

		.dropdown-text {
			overflow: hidden;
			text-wrap: nowrap;
			margin-right: 10px;
			text-overflow: ellipsis;
			color: #000;
		}
	}

	.dropdown-menu {
		width: 100%;
		max-height: 177px;
		overflow: auto;

		.dropdown-item {
			color: rgb(0 0 0 / 50%);
			font-weight: bold;
			font-size: 14px;
			position: relative;
			background-color: transparent !important;

			&.selected {
				color: #28a745;
				background-color: #e2e2e29e !important;
			}

			.selected-img {
				position: absolute;
				top: 5px;
				right: 13px;
				width: 20px;
				height: 20px;
				background-size: 100%;
				background-repeat: no-repeat;
			}
		}

		.no-data-text {
			padding: 10px;
			text-align: center;
		}
	}
`;

export default DropdownWrapper;
