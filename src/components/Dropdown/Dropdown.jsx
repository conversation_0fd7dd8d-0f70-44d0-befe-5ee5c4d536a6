import { useEffect, useState } from 'react';
import { DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';
import DropdownWrapper from './Dropdown.style';
import selectedImg from '../../assets/images/approve.svg';

const Dropdown = (props) => {
	const [data, setData] = useState([]);
	const [text, setText] = useState('');
	const [dropdownOpen, setDropdownOpen] = useState(false);

	useEffect(() => {
		setData(props.data.map((value) => ({ ...value })));
	}, [props.data]);

	useEffect(() => {
		const selectedItems = data.filter((value) => value.selected);
		const allItemIndex = data.findIndex((value) => value.name === 'All');

		if (allItemIndex !== -1) {
			// Determine whether the "All" item should be selected
			const isAllSelected = selectedItems.length === data.length - 1; // Exclude "All"
			data[allItemIndex].selected = isAllSelected;
		}

		if (selectedItems.length === data.length && data.length > 0) {
			setText('All');
		} else {
			setText(
				selectedItems.length > 0
					? selectedItems.map((value) => value.name).join(', ')
					: props.placeholder
			);
		}

		props.handleChange(selectedItems.map((value) => value.id));
	}, [data]);

	const handleChangeDropdown = () => setDropdownOpen((prev) => !prev);

	const handleClickAll = () => {
		setData((prev) => {
			const copyPrev = [...prev];
			const isAllSelected = !copyPrev.every((value) => value.selected);

			copyPrev.forEach((value) => {
				value.selected = isAllSelected;
			});

			props.handleChange(isAllSelected ? copyPrev.map((value) => value.id) : []);

			return copyPrev;
		});
	};

	const handleClickRoleDropdown = (id) => {
		setData((prev) => {
			const copyPrev = [...prev];
			const index = copyPrev.findIndex((value) => value.id === id);

			if (index !== -1) {
				copyPrev[index].selected = !copyPrev[index].selected;
			}

			return copyPrev;
		});

		handleChangeDropdown();
	};

	return (
		<DropdownWrapper
			isOpen={dropdownOpen}
			toggle={handleChangeDropdown}
			className={`${props.disabled ? 'cursor-disabled' : ''}`}>
			<DropdownToggle
				caret
				className={`dropdown-btn ${text === 'All' ? 'filled' : ''} `}
				disabled={props.disabled}
				color="primary">
				<span className="dropdown-text">
					{text !== 'All' ? (text.length ? text : props.placeholder) : 'All'}
				</span>
			</DropdownToggle>
			<DropdownMenu className="dropdown-menu">
				{data.length ? (
					<>
						<DropdownItem
							className={`dropdown-item ${data.every((value) => value.selected) ? 'selected' : ''
								}`}
							onClick={handleClickAll}>
							All
							{data.every((value) => value.selected) ? <img src={selectedImg} className='selected-img' /> : null}
						</DropdownItem>
						{data.map((role) => (
							<DropdownItem
								className={`dropdown-item ${role.selected ? 'selected' : ''}`}
								key={role.id}
								onClick={() => handleClickRoleDropdown(role.id)}>
								{role.name}
								{role.selected ? <img src={selectedImg} className='selected-img' /> : null}
							</DropdownItem>
						))}
					</>
				) : (
					<div className="no-data-text">No Data</div>
				)}
			</DropdownMenu>
		</DropdownWrapper>
	);
};

export default Dropdown;
