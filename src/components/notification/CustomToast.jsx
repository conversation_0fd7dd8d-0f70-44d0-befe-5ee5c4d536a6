import React from 'react';
import ToastWrapper from './toast.style';
import classNames from 'classnames';
import ErrorIcon from 'src/assets/images/Notify/error.svg';
import SuccessIcon from 'src/assets/images/Notify/success.svg';
import InfoIcon from 'src/assets/images/Notify/info.svg';
import NotificationIcon from 'src/assets/images/Notify/notification.svg';
import CloseIcon from 'src/assets/images/Notify/close-notify.svg';

const CustomToast = (props) => {
	const {
		message,
		heading,
		className,
		style,
		width,
		position = '',
		show,
		transition = false
	} = props;

	let icon;
	switch ((heading || '').toLowerCase()) {
		case 'success':
			icon = SuccessIcon;
			break;
		case 'error':
			icon = ErrorIcon;
			break;
		case 'info':
			icon = InfoIcon;
			break;
		default:
			icon = NotificationIcon;
			break;
	}

	return (
		<ToastWrapper width={width}>
			{show && (
				<div
					className={classNames(
						className,
						'custom-toast',
						transition && position.substring(0, 3) === 'top' && 'animated',
						transition && position.substring(0, 3) === 'top' && 'fadeInDown',
						transition && position.substring(0, 6) === 'bottom' && 'animated',
						transition && position.substring(0, 6) === 'bottom' && 'fadeInUp',
						position
					)}
					style={style}>
					<div className="d-flex">
						<img src={icon} alt="notification-icon" width={23} className="mr-10" />
						<div className="d-flex flex-column">
							<strong className="mr-auto">{heading}</strong>
							<div className="font-14">{message}</div>
							{/* <img
								src={CloseIcon}
								width={19}
								alt="close-icon"
								className="far fa-times-circle close-icon"
								onClick={() => props.onCloseCLick && props.onCloseCLick()}
							/> */}
						</div>
					</div>
				</div>
			)}
		</ToastWrapper>
	);
};

export default CustomToast;
