import styled from 'styled-components';

const ToastWrapper = styled.div`
	.custom-toast {
		border: 0;
		border-radius: 5px;
		top: 25px;
		right: 20px;
		position: relative;
		width: ${(props) => props.width}px;
		padding: 10px;
		box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
		transition: all 1s;

		.close-icon {
			position: absolute;
			right: 10px;
			top: 13px;
			cursor: pointer;
		}
	}

	.top-left {
		position: fixed;
		z-index: 999;
		z-index: 5;
		@media only screen and (max-width: 575.98px) {
			left: 10%;
			width: 291px;
		}
	}

	.top-right {
		position: fixed;
		z-index: 9999;
		@media only screen and (max-width: 575.98px) {
			right: 10%;
			width: 291px;
		}
	}

	.top-middle {
		position: fixed;
		z-index: 999;
		@media only screen and (min-width: 768px) and (max-width: 991.98px) {
			right: 27%;
		}
		@media only screen and (min-width: 576px) and (max-width: 767.98px) {
			right: 23%;
		}
		@media only screen and (max-width: 575.98px) {
			right: 10%;
			width: 291px;
		}
	}

	.bottom-left {
		position: fixed;
		z-index: 5;
		@media only screen and (max-width: 575.98px) {
			left: 10%;
			width: 291px;
		}
	}

	.bottom-right {
		position: fixed;
		z-index: 5;
		@media only screen and (max-width: 575.98px) {
			right: 10%;
			width: 291px;
		}
	}

	.bottom-middle {
		position: fixed;
		z-index: 5;
		@media only screen and (min-width: 768px) and (max-width: 991.98px) {
			right: 27%;
		}
		@media only screen and (min-width: 576px) and (max-width: 767.98px) {
			right: 23%;
		}
		@media only screen and (max-width: 575.98px) {
			right: 10%;
			width: 291px;
		}
	}
`;

export default ToastWrapper;
