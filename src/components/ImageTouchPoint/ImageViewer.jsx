import React from 'react';
import TouchPointWrapper from './ImageViewer.style';

const ImageViewer = ({ imageSrc, onImageClick, touchpoints, cursor_indication }) => {
	return (
		<TouchPointWrapper>
			<div className="main">
				{imageSrc && (
					<img
						className={`img-overlay ${cursor_indication ? 'cursor-default' : ''}`}
						src={imageSrc}
						alt="Uploaded"
						onClick={onImageClick}
					/>
				)}
				{Object.keys(touchpoints).length > 0
					? Object.entries(touchpoints).map(([key, point], index) => (
							<div
								key={key}
								className="red-circle-point d-flex justify-content-center align-items-center"
								style={{
									top: point.top,
									left: point.left
								}}>
								{index + 1}
							</div>
					  ))
					: null}
			</div>
		</TouchPointWrapper>
	);
};

export default ImageViewer;
