import styled from 'styled-components';

const TouchPointWrapper = styled.div`
	.main {
		position: relative;
		max-width: 375px;
		/* max-height: 667px; */
		max-height: 450px;
		height: 100%;
		border: 1px solid #dee2e6;
		/* border-radius: 7px; */
		overflow: hidden;
	}
	.img-overlay {
		width: 100%;
		height: 100%;
		max-height: 450px;
		object-fit: cover;
		cursor: crosshair;
	}
	.red-circle-point {
		position: absolute;
		transform: translate(-50%, -50%);
		background-color: red;
		width: 20px;
		height: 20px;
		border-radius: 50px;
		color: white;
		text-align: center;
		line-height: 31px;
		font-size: 15px;
		z-index: 1;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			height: 100%;
			width: 100%;
			background: red;
			border-radius: 100%;
			animation-name: ripple;
			animation-duration: 1.7s;
			animation-delay: 0s;
			animation-iteration-count: infinite;
			animation-timing-function: cubic-bezier(0.65, 0, 0.34, 1);
			z-index: -1;
		}
	}
`;

export default TouchPointWrapper;
