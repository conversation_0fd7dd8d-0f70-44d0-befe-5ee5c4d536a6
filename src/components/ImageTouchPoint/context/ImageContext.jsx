import React, { createContext, useContext, useState, useEffect } from "react";

const ImageContext = createContext(null);

export const useImageContext = () => {
  const context = useContext(ImageContext);
  if (!context === null) {
    throw new Error("useImageContext must be used within an ImageProvider");
  }
  return context;
};

const ImageProvider = ({ children }) => {
  const [imagesData, setImagesData] = useState(
    JSON.parse(localStorage.getItem("imagesData") || "[]")
  );

  const addImage = (data) => {
    setImagesData((prev) => [...prev, data]);
  };

  const updateImageTouchpoints = (id, touchpoints) => {
    setImagesData((prev) =>
      prev.map((img) => (img.id === id ? { ...img, touchpoints } : img))
    );
  };

  useEffect(() => {
    localStorage.setItem("imagesData", JSON.stringify(imagesData));
  }, [imagesData]);

  return (
    <ImageContext.Provider
      value={{ imagesData, addImage, updateImageTouchpoints }}
    >
      {children}
    </ImageContext.Provider>
  );
};

export { ImageProvider, useImageContext };
