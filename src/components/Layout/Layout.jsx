import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Scrollbars } from 'react-custom-scrollbars';
import { Outlet, useLocation } from 'react-router-dom';
// STYLES
import CommonStyles from 'src/components/Common.style';
// HELPERS
import { DRAWER_WIDTH, MINI_DRAWER_WIDTH } from '../../helper/constant';
// COMPONENTS
import Sidebar from '../sidebar/Sidebar';
import Header from '../header/Header';
import Footer from '../footer/Footer';
import ErrorBoundary from '../common/ErrorBoundary';
// REDUX
import { sidebarMini } from 'src/redux/Slices/themeSetting-slice';

const Layout = () => {
	const scrollBar = useRef(null);
	const location = useLocation();
	const dispatch = useDispatch();

	const { themeSetting, themeChanger } = useSelector((state) => ({
		themeChanger: state.themeChanger,
		themeSetting: {
			toolbarAlignValue: state.themeSetting.toolbarAlignValue,
			footerAlignValue: state.themeSetting.footerAlignValue,
			sidebarDisplayValue: state.themeSetting.sidebarDisplayValue,
			toolbarDisplayValue: state.themeSetting.toolbarDisplayValue,
			footerDisplayValue: state.themeSetting.footerDisplayValue,
			sidebarTransParentValue: state.themeSetting.sidebarTransParentValue,
			transparentImage: state.themeSetting.transparentImage,
			activeLinkStyle: state.themeSetting.activeLinkStyle,
			sidebarMiniValue: state.themeSetting.sidebarMiniValue,
			sidebarTransParentActiveBack: state.themeSetting.sidebarTransParentActiveBack,
			sidebarTransParentActiveColor: state.themeSetting.sidebarTransParentActiveColor
		}
	}));

	let routeScrollHeight;
	let routeArrowIcon = {
		backgroundColor: themeChanger.sidebarTheme.backgroundColor,
		color: themeChanger.sidebarTheme.textColor,
		borderBottomLeftRadius: 0,
		borderTopLeftRadius: 0,
		position: 'fixed',
		zIndex: 1,
		fontSize: '18px',
		padding: '4px 5px'
	};

	const [mini, setMini] = useState(
		themeSetting.sidebarMiniValue ? themeSetting.sidebarMiniValue === 'on' : false
	);

	const mainPanelWidth = {
		width: mini ? `calc(100% - ${MINI_DRAWER_WIDTH})` : `calc(100% - ${DRAWER_WIDTH})`,
		'@media (max-width: 767.98px)': { width: `100%` }
	};

	useEffect(() => {
		if (scrollBar.current) scrollBar.current.scrollTop(0);
	}, [location.pathname]);

	useEffect(() => {
		if (mini !== themeSetting.sidebarMiniValue) setMini(themeSetting.sidebarMiniValue === 'en');
	}, [themeSetting.sidebarMiniValue]);

	const drawerMiniMethod = () => {
		setMini((prevMini) => !prevMini);
	};

	useEffect(() => {
		dispatch(sidebarMini('off'));
	}, [mini]);

	const closeDrawer = () => {
		if (mini) {
			setMini(false);
			dispatch(sidebarMini('off'));
		} else {
			setMini(true);
			dispatch(sidebarMini('on'));
		}
	};

	if (themeSetting.toolbarDisplayValue === 'hide' && themeSetting.footerDisplayValue === 'hide')
		routeScrollHeight = { height: 'calc(100vh - 0px)' };
	else if (themeSetting.toolbarDisplayValue === 'hide')
		routeScrollHeight = { height: 'calc(100vh - 51px)' };
	else if (themeSetting.footerDisplayValue === 'hide')
		routeScrollHeight = { height: 'calc(100vh - 65px)' };
	else routeScrollHeight = { height: 'calc(100vh - 98px)' };

	return (
		<ErrorBoundary>
			<CommonStyles
				layoutTheme={themeChanger.layoutTheme}
				sidebarTheme={themeChanger.sidebarTheme}
				themeSetting={themeSetting}
			/>

			<Sidebar
				mini={mini}
				closeDrawer={closeDrawer}
				drawerWidth={DRAWER_WIDTH}
				themeSetting={themeSetting}
				sidebarTheme={themeChanger.sidebarTheme}
				miniDrawerWidth={MINI_DRAWER_WIDTH}
			/>

			<div id="main-panel" className="main-panel" style={mainPanelWidth}>
				{themeSetting.toolbarDisplayValue === 'show' && (
					<Header
						mini={mini}
						drawerWidth={DRAWER_WIDTH}
						themeSetting={themeSetting}
						miniDrawerWidth={MINI_DRAWER_WIDTH}
						drawerMiniMethod={drawerMiniMethod}
						topbarTheme={themeChanger.topbarTheme}
					/>
				)}

				<div className="drawer-handle-arrow-hide-topbar">
					{themeSetting.toolbarDisplayValue !== 'show' ? (
						<button style={routeArrowIcon} className="c-btn" onClick={drawerMiniMethod}>
							<i className={`fas fa-arrow-${mini ? 'right' : 'left'}`} />
						</button>
					) : (
						''
					)}
				</div>

				{themeSetting.toolbarDisplayValue !== 'show' && (
					<button
						style={routeArrowIcon}
						onClick={drawerMiniMethod}
						className="c-btn mini-drawer-menu-icon-hide-topbar">
						<i className="fas fa-bars" />
					</button>
				)}

				<Scrollbars style={routeScrollHeight} autoHide ref={scrollBar}>
					<div
						style={{
							padding: '0 15px 15px 15px',
							minHeight: 'calc(100vh - 98px)',
							background: themeChanger.layoutTheme.backgroundColor
						}}>
						<Outlet />
					</div>
				</Scrollbars>

				{themeSetting.footerDisplayValue === 'show' && (
					<Footer
						mini={mini}
						drawerWidth={DRAWER_WIDTH}
						themeSetting={themeSetting}
						miniDrawerWidth={MINI_DRAWER_WIDTH}
						footerTheme={themeChanger.footerTheme}
					/>
				)}
			</div>
		</ErrorBoundary>
	);
};

export default Layout;
