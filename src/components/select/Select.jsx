import React, { useState, useEffect } from "react";
import Select from "react-select";
import UserServices from "../../api/UserServices";

const SingleSelect = props => {
    const [optionData, setOptionData] = useState([]);

    useEffect(() => {
        const config = {
            search: ""
        };
        UserServices.countryCode(config)
            .then(res => {
                return res.data.data;
            })
            .then(res => {
                const DropdownData = res.map(data => {
                    return {
                        value: data.country_code,
                        label: `+${data.country_code}-${data.country_name}`,
                        country_code_id: data.country_code_id
                    };
                });
                return DropdownData;
            })
            .then(res => setOptionData(res));
        // const data = {
        //   value: props && props.value.country_name,
        //   label: props && props.value.country_code
        // };
        // setcountryCode(data);
    }, []);
    const handleChange = selectedOption => {
        props.Change(selectedOption);
    };
    return (
        <Select
            value={props.value}
            onChange={handleChange}
            options={optionData}
            isDisabled={props.disabled}
        />
    );
};

export default SingleSelect;
