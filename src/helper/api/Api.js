import axios from 'axios';
import { BASE_URL, ORDER_BASE_URL } from '../constant';
import { store } from '../../redux/store';
import { logout } from 'src/redux/Slices/auth-slice';
import CODES from '../StatusCodes';

const axiosInstance = axios.create({});
const METHOD = { GET: 'get', POST: 'post', PUT: 'put', DELETE: 'delete' };
const MODULE_BASE_URLS = {
	default: BASE_URL,
	order: ORDER_BASE_URL,
	zone: ORDER_BASE_URL // You can change this to ORDER_BASE_URL if needed
	// Add more module types as needed
};

const setHeaders = (data) => {
	let headers = {
		'accept-language': 'en',
		'Content-Type': 'application/json',
		// 'ngrok-skip-browser-warning': 'true',
		platform: 'web',
		token: store.getState().auth.accessToken
	};

	if (data) {
		if (data.isMultipart) {
			headers['Content-Type'] = 'multipart/form-data';
		}
		if (data.isBinary) {
			headers['Content-Type'] = 'video/mp4';
		}

		if (data.headers) {
			for (var key in data.headers) {
				if (data.headers.hasOwnProperty(key)) {
					headers[key] = data.headers[key];
				}
			}
		}
	}
	return headers;
};

export const getApi = (urlData, params = {}, typeOfModule) => {
	const baseUrl = MODULE_BASE_URLS[typeOfModule] || BASE_URL;
	const url = `${baseUrl}${urlData}`;
	const headers = { ...setHeaders() };

	let axiosObj = { method: METHOD.GET, url, params, headers };

	return axios(axiosObj);
};

export const postApi = (urlData, params = {}, typeOfModule, multipart) => {
	const baseUrl = MODULE_BASE_URLS[typeOfModule] || BASE_URL;
	const url = `${baseUrl}${urlData}`;
	const headers = {
		...setHeaders({ isMultipart: multipart || false })
	};

	let axiosObj = { method: METHOD.POST, url: url, data: params, headers };

	return axios(axiosObj);
};

export const putApi = (urlData, params = {}, typeOfModule, multipart) => {
	const baseUrl = MODULE_BASE_URLS[typeOfModule] || BASE_URL;
	const url = `${baseUrl}${urlData}`;
	const headers = {
		...setHeaders({ isMultipart: multipart || false })
	};

	let axiosObj = { method: METHOD.PUT, url: url, data: params, headers };

	return axios(axiosObj);
};

export const putVideoApi = (urlData, params = {}) => {
	const url = urlData;
	const headers = {
		'Content-Type': 'multipart/form-data' // Set the content type to handle binary data
	};

	let axiosObj = { method: METHOD.PUT, url: url, data: params, headers };

	return axios(axiosObj);
};

export const Api = (method, urlData, data) => {
	const url = `${BASE_URL}${urlData}`;

	let headers = {};
	headers = setHeaders(data, url);
	if (store.getState().auth.accessToken) {
		headers['token'] = store.getState().auth.accessToken;
	}
	let axiosObj = {
		method: method,
		url: url,
		data: data,
		headers: headers
	};
	return axiosInstance(axiosObj);
};

axios.interceptors.response.use(
	(next) => {
		return Promise.resolve(next);
	},
	(error) => {
		if (error?.response?.status === CODES.UNAUTHORIZED) {
			const { isLogin } = store.getState().auth;
			if (isLogin) {
				store.dispatch(logout());
				// toaster.error('Session has been expired!');
			}
		}
		return Promise.reject(error);
	}
);
