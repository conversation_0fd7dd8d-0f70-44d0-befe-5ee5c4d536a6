export const AUTH = {
	LOGIN: '/auth/login',
	LOGOUT: '/auth/logout'
};

export const PASSWORD = {
	// SEND_FORGOT_LINK: "/user/v1/forgot-password",
	FORGOT_PASSWORD_OTP: '/auth/send-otp',
	OTP_VERIFY: '/auth/verify-otp',
	// FORGOT_PASSWORD: "/auth/send-forgot-password-otp",
	RESET_PASSWORD: '/auth/reset-forgotted-password',
	CHANGE_PASSWORD: '/auth/change-password'
};

export const ROLES = {
	GET: '/roles/get',
	GET_CHILD_ROLE: '/roles/get-child-roles',
	APPROVE_PROFILE: '/user/review-profile'
};

export const VIDEO_TRAINING = {
	GET: '/training-materials/get',
	GET_CATEGORIES: '/training-materials/category/get',
	ADD: '/training-materials/create',
	UPDATE: '/training-materials/update',
	UPDATE_ACTIVE_INACTIVE: '/training-materials/change-status',
	UPDATE_FILE_UPLOAD_STATUS: '/training-materials/upload-success',
	GENERATE_FILE_URL: '/training-materials/generate-signed-url'
};

export const FEEDBACK = {
	GET: '/feedback/get',
	UPDATE: '/feedback/update-status'
};

export const POINTS = {
	GET_DEFINE_RATE: '/points/rate/get',
	UPDATE_DEFINE_RATE: '/points/rate/define-per-points',
	UPDATE_ASSIGN_POINTS: '/points/define-per-wo-type',
	GET_ASSIGN_POINTS: '/points/get',
	GET_ASSIGN_POINTS_MORE: '/points/get-by-wo-type'
};

export const STATIC_PAGES = {
	GET: '/templates/get',
	UPDATE: '/templates/update'
};

export const PROFILE = {
	GET: '/user/profile',
	ADD: '',
	UPDATE: '/user/update-profile',
	UPDATE_ACTIVE_INACTIVE: '/user/active-inactive-profile'
};

export const ZONE = {
	GETALL: '/zone/get'
};

export const SKILLS = {
	GET: '/skills/get',
	ADD: '/skills/create-role-skills',
	UPDATE: '/skills/update',
	UPDATE_ACTIVE_INACTIVE: '/skills/change-status'
};

export const SUPERVISOR = {
	GET: '/user/get-supervisors'
};

export const FABRICATOR = {
	GET_USER: '/user/get-fabricators',
	// ADD: '/skills/create-role-skills',
	// UPDATE: '/skills/update',
	// UPDATE_ACTIVE_INACTIVE: '/skills/change-status'
	GETALL: '/zone/get',
	ASSIGN_FABRICATOR: '/user/assign-parent-to-child'
};

export const USER = {
	GET_USER_BY_ROLE: '/user/get-internal-team'
};

export const ADDRESS = {
	GET: '/address/get',
	APPROVE: '/address/review'
};

export const REASONS = {
	GET_REASONS: '/reason/get',
	UPDATE: '/reason/update',
	CREATE: '/reason/create'
};
export const SPACES = {
	GET_SPACES: '/spaces/get',
	ADD: '/spaces/add',
	UPDATE: '/spaces/update',
	CHANGE_STATUS: '/spaces/change-status'
};

export const TEMPLATES = {
	GET: '/templates/get',
	GET_TEMPLATES_ACTIONS: '/templates/get-template-actions',
	ADD: '/templates/create',
	UPDATE: '/templates/update'
};

export const SCOPES = {
	GET_SCOPES: '/scopes/get',
	ADD: '/scopes/add',
	UPDATE: '/scopes/update',
	CHANGE_STATUS: '/scopes/change-status'
};

export const KYC = {
	GET_KYC_FIELDS: '/kyc/type-with-fields'
};

export const WORK_ORDER_TYPE = {
	GET: '/work-order-type/get',
	ADD: '/work-order-type/add',
	UPDATE: '/work-order-type/update',
	CHANGE_STATUS: '/work-order-type/change-status'
};

export const ORDER_MANAGEMENT = {
	GET: '/orders/get',
	DETAILS: '/work-order/get',
	TASK_DETAILS: '/tasks/get',
	TASK_QUESTIONS: '/question-set/get-by-wo-type-scope',
	GET_ZOHO: '/orders/fetch-sync'
};
export const HANDOVER_QUESTION = {
	GET: '/handover-question/get',
	ADD: '/handover-question/create',
	CHANGE_STATUS: '/handover-question/update-status'
};
export const QUESTION_TAG = {
	GET: '/report-tags/get',
	ADD: '/report-tags/create',
	UPDATE: '/report-tags/edit'
};
export const TAT = {
	GET: '/master-data/get',
	UPDATE: '/master-data/update'
};
export const INSTALLATION_STEP = {
	GET: '/installation-steps/get',
	ADD: '/installation-steps/add',
	UPDATE: '/installation-steps/edit'
};
export const QUESTION = {
	GET_TYPE: '/question-type/get',
	GET: '/questions/get',
	ADD: '/questions/create',
	CHANGE_STATUS: '/questions/change-status',
	UPDATE: '/questions/update',
	REPORT_TAGS_GET: '/report-tags/get',
	REPORT_TAGS_ADD: '/report-tags/create'
};
export const QUESTION_SET = {
	ADD: '/question-set/create',
	EDIT: '/question-set/edit',
	GET: '/question-set/get',
	CHANGE_STATUS: '/question-set/change-status'
};

export const DASHBOARD = {
	GET: '/dashboard/cms'
};

export const REPORTS = {
	GET: '/reports/order'
};
export const REDEEM = {
	UPDATE: '/points/redeem-earnings',
	GET: '/points/redeemed-earnings-history'
};

export const ROLE = {
	GET: '/roles/get',
	ADD: '/roles/create',
	UPDATE: '/roles/update',
	DELETE: '/roles/delete'
};

export const GROUP = {
	GET: '/groups/get',
	ADD: '/groups/create',
	UPDATE: '/groups/update',
	DELETE: '/groups/delete'
};

export const USER_CREATION = {
	GET: '/user/get',
	ADD: '/user/create',
	TAG_GET: '/tags/get',
	TAG_ADD: '/tags/create',
	UPDATE: '/user/update',
	DELETE: '/user/delete'
};

export const PERMISSION = {
	GET: '/permissions/get',
	ADD: '/permissions/create',
	ASSIGN: '/permissions/assign-to-role',
	MODULE_GET: '/modules/get',
	UPDATE: '/permissions/update'
};

export const ZONE_BASED_AMOUNT = {
	GET: '/zone-based-amount/get',
	UPDATE: '/zone-based-amount/update',
};
