/* eslint-disable no-unused-vars */
export const APP_NAME = 'Eternia';
export const DRAWER_WIDTH = '270px';
export const MINI_DRAWER_WIDTH = '80px';
export const BASE_URL = import.meta.env.VITE_REACT_APP_BASE_URL;
export const ORDER_BASE_URL = import.meta.env.VITE_REACT_APP_ORDER_BASE_URL;
import ICON_DEMO_PATH from '../assets/images/Logo.svg';
import { INSTALLATION_STEP } from './api/endPoint';

export const ICON_DEMO = ICON_DEMO_PATH;
export const MAX_FILE_SIZE = {
	IMAGE: 2 // MB
};

export const MAX_FILE_SIZE_MULTIPLE = {
	IMAGE: 5 // MB
};

export const ALLOWED_TOTAL_FILES_TO_UPLOAD = {
	IMAGE: 5
};

export const ALLOWED_FILE_FORMATS = {
	IMAGE: ['image/jpg', 'image/jpeg', 'image/png']
};

export const TABLE = {
	LIMIT: 10,
	MIN_ROW: 2
};

export const IMAGE_RESOLUTION = {
	HEIGHT: 100,
	WIDTH: 100
};

export const API_SUCCESS_STATUS = 200;

export const USER_ROLES = {
	ADMIN: {
		ID: 1,
		NAME: 'ADMIN'
	},
	USER: {
		ID: 2,
		NAME: 'USER'
	},
	VENDOR: {
		ID: 3,
		NAME: 'VENDOR'
	}
};

export const ROUTES = {
	HOME: '/',
	LOGIN: '/login',
	PROFILE: {
		EDIT: '/profile/edit'
	},
	DASHBOARD: {
		BASE: '/reports'
	},
	PASSWORD: {
		CHANGE: '/password/change',
		// FORGOT: "/forgot-password/:token",
		RESET: '/reset-password', //replace as above after api integration
		FORGOT_PASSWORD: '/forgot-password',
		OTP_VERIFY: '/otp-verify'
	},
	STATIC_PAGES: {
		BASE: '/static-pages'
	},
	SKILLS: {
		BASE: '/skills'
	},
	SETTINGS: {
		REJECTED_REASON: '/settings/rejectedReasons',
		SPACE: '/settings/space',
		SCOPE: '/settings/scope',
		WORK_ORDER_TYPE: '/settings/work-order-type'
	},
	INTERNAL_TEAM_MANAGEMENT: {
		APPROVED: '/internal-team/approved',
		REGISTERED: '/internal-team/registered',
		REJECTED: '/internal-team/rejected'
	},
	ADDRESS_APPROVE: {
		APPROVED: '/address-approve/approved',
		REGISTERED: '/address-approve/registered',
		REJECTED: '/address-approve/rejected'
	},
	FABRICATOR: {
		APPROVED: '/fabricator/approved',
		REGISTERED: '/fabricator/registeredUser',
		REJECTED: '/fabricator/rejected'
	},
	SUPERVISOR: {
		APPROVED: '/supervisor/verifiedUser',
		REGISTERED: '/supervisor/registeredUser',
		REJECTED: '/supervisor/rejectedUser',
		ASSIGN_FABRICATOR: '/supervisor/assign-fabricator',
		WALLET_LOGS: '/supervisor/verifiedUser/wallet-logs'
	},
	ORDER_MANAGEMENT: {
		BASE: '/order-management',
		DETAILS: '/order-management/details',
		TASK_DETAILS: '/order-management/task-details'
	},
	HANDOVER_QUESTIONS: {
		BASE: '/handover-questions'
	},
	INSTALLATION_STEPS: {
		BASE: '/installation_steps'
	},
	QUESTION_BANK: {
		TAGS: '/questions/tags',
		BASE: '/questions/question-bank',
		MAPPING: '/questions/question-mapping',
		ADD: '/questions/question-mapping/add',
		EDIT: '/questions/question-mapping/edit'
	},
	VIDEO_TRAINING: {
		BASE: '/video-training'
	},
	FEEDBACK: {
		BASE: '/feedback'
	},
	ROLE: {
		BASE: '/role'
	},
	GROUP: {
		BASE: '/group'
	},
	USER_CREATION: {
		BASE: '/user-creation'
	},
	PERMISSION: {
		BASE: '/create-permission',
		ASSIGN: '/assign-permission'
	},
	TEMPLATES: {
		BASE: '/templates/email',
		NOTIFICATIONS: '/templates/notifications'
	},
	POINTS: {
		DEFINE: '/points/define',
		ASSIGN: '/points/assign',
		DETAILS: '/points/assign/details'
	},
	REPORTS: {
		BASE: '/dashboard'
	},
	MASTER_DATA: {
		TAT: '/master-data/tat',
		ZONE_WISE_HIERARCHY: '/master-data/zone-wise-hierarchy'
	}
};

export const REGEX_VALIDATION = {
	MOBILE: /^[0-9]*$/
};

export const MAX_LENGTH = {
	MOBILE: 10
};

export const MIN_LENGTH = {
	MOBILE: 10
};
export const REASON_OTHER = 'General';

export const ForgotTabs = [
	{ value: 'EMAIL', label: 'Email' },
	{ value: 'MOBILE', label: 'Mobile' }
];

export const CountryOptions = [
	{ code: '91', name: 'India' },
	{ code: '1', name: 'United States' }
	// Add more country options as needed
	// { code: "44", name: "United Kingdom" },
	// { code: "81", name: "Japan" },
];

export const QUILL_VALIDATIONS = [
	undefined,
	'',
	'<p><br></p>',
	'<h1><br></h1>',
	'<h2><br></h2>',
	'<h3><br></h3>',
	'<h4><br></h4>',
	'<blockquote><br></blockquote>',
	'<p><br></p><p><br></p>',
	'<p><br></p><p><br></p><p><br></p>'
];

export const ROLE_SCOPE = {
	APP: 'APP',
	CMS: 'CMS',
	BOTH: 'BOTH'
};

export const ROLE_TYPE = {
	PARENT: 'PARENT',
	CHILD: 'CHILD'
};

export const ROLE_SCOPE_NAME = {
	INTERNAL_TEAM: 'Eternia Team',
	BUSINESS_ASSOCIATES: 'Business Associates'
};

export const CHILD_ROLE_NAME = {
	SERVICE_HEAD: 'Service Head',
	SERVICE_ENGINEER: 'Service Engineer'
};

export const PROFILE_STATUS = {
	REJECTED: 0,
	APPROVED: 1,
	PENDING: 2
};

export const regexCharactersNumbers = /^[a-zA-Z\s]*$/;

export const allQuestionsTypes = [
	{
		id: 1,
		ques_type: 'Single Answer (MCQs)'
	},
	{ id: 2, ques_type: 'Multiple Answer (MCQs)' },
	{ id: 3, ques_type: 'Descriptive (Inputs)' }
];

export const touchPoints = {
	left: 0,
	top: 0
};

export const supportedFormats = ['image/png', 'image/jpeg', 'image/svg+xml', 'image/gif'];
export const VIDEO__SUPPORTED__FORMATS = [
	'video/x-flv',
	'video/mp4',
	'application/x-mpegURL',
	'video/MP2T',
	'video/3gpp',
	'video/quicktime',
	'video/x-msvideo',
	'video/x-ms-wmv'
];

export const API_STATUS = { ACTIVE: 1, IN_ACTIVE: 0 };

export const SCOPE_TAB = [
	{ id: 1, title: 'CMS' },
	{ id: 2, title: 'ZOHO' }
];

export const TEMPLATE_ACTIONS = {
	PAGE: 'PAGE',
	NOTIFICATION: 'NOTIFICATION',
	EMAIL: 'EMAIL',
	SMS: 'SMS'
};
