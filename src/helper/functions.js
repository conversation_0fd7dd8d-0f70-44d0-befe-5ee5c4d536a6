import { ALLOWED_FILE_FORMATS, MAX_FILE_SIZE } from './constant';
import moment from 'moment';
export const formAttributes = (props, fieldName) => ({
	id: fieldName,
	value: props?.values?.[fieldName] || '',
	onChange: props.handleChange,
	onBlur: props.handleBlur
});

export const convertBytesToMb = (bytes) => {
	let MB = 0;
	if (bytes) {
		MB = bytes / (1024 * 1024);
	}

	return MB;
};

export const validateFileSize = (value) => {
	let valid = true;
	if (value.length && value[0].size && convertBytesToMb(value[0].size) > MAX_FILE_SIZE.IMAGE) {
		valid = false;
	}
	return valid;
};

export const validateImageFormat = (value) => {
	let valid = true;
	if (value[0].type && !ALLOWED_FILE_FORMATS.IMAGE.includes(value[0].type)) {
		valid = false;
	}
	return valid;
};

export const convertTimeToLocal = (time) => {
	const localTime = moment.utc(time).local();
	return localTime.format('L');
};

export const convertDateTimeToLocal = (time) => {
	const localTime = moment.utc(time).local();
	return localTime.format('LT');
};

export const downloadFile = (fileUrl, fileName) => {
	const link = document.createElement('a');
	link.href = fileUrl;
	link.download = fileName;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
};

export const extractFileName = (url) => {
	const parts = url.split('/');
	const fileNameWithExtension = parts[parts.length - 1];
	const fileName = fileNameWithExtension.split('.')[0];
	return fileName;
};

export const groupBy = (array, key) => {
	const groupedData = array.reduce((groups, item) => {
		const category = item[key];

		if (!groups[category]) {
			groups[category] = [];
		}

		groups[category].push(item);

		return groups;
	}, {});

	const resultArray = Object.entries(groupedData).map(([key, value]) => ({
		key,
		value
	}));

	return resultArray;
};
