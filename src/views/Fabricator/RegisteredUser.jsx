/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import FabricatorWrapper from './Fabricator.style';
import PageTitle from 'src/components/common/PageTitle';
import EditIcon from 'src/assets/images/Edit.svg';
import ViewIcon from 'src/assets/images/View.svg';
import ReactTable from 'react-table';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import Pagination from '../../components/Pagination/Pagination';
import Loader from '../../components/common/Loader';
import { useDispatch } from 'react-redux';
import { getFabricatorUsers } from 'src/redux/apis/usersAPIs';
import 'bootstrap/dist/css/bootstrap.min.css';
import PageIcon from 'src/assets/images/Sidebar/Page.svg';
import ApproveIcon from '../../assets/images/approve.svg';
import RejectIcon from '../../assets/images/reject.svg';
import { convertTimeToLocal } from 'src/helper/functions';
import ViewOrEditModal from './Modals/ViewOrEditModal';
import 'react-table/react-table.css';
import Toaster from 'src/components/common/Toaster';
import { postApi } from 'src/helper/api/Api';
import { PROFILE, ROLES } from 'src/helper/api/endPoint';
import crossIcon from '../../assets/images/cross.svg';
import searchIcon from '../../assets/images/search.svg';
import useDebounce from 'src/util/hooks/useDebounce';
import Swal from 'sweetalert2';
import { CONFIRM_APPROVE_POPUP } from 'src/components/header/constants';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';

// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const RegisteredUser = () => {
	const dispatch = useDispatch();
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 400);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: ' User Type',
					width: 180,
					accessor: 'kyc_data.type.name',
					resizable: false,
					default: '-',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'City',
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Zone',
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Created On',
					resizable: false,
					maxWidth: 130,
					minWidth: 110,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					sticky: 'right',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={ViewIcon}
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeAddOrEditModal({
									open: true,
									view: true,
									data: cell.original,
									profile_status: PROFILE_STATUS.PENDING
								})}
							/>
							<img
								src={ApproveIcon}
								alt="ApproveIcon"
								title="Approve"
								className="mr-10 cursor-pointer"
								width={22}
								onClick={() => handleApprovedUser(cell.original)}
							/>

							<img
								src={RejectIcon}
								alt="RejectIcon"
								title="Reject"
								className="cursor-pointer"
								width={20}
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									reject: true
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];
	useEffect(() => {
		if (debounceSearch?.length > 2 || !debounceSearch) {
			getUsers();
		}
	}, [debounceSearch, activePage]);

	const setSearch = (e) => {
		setSearchKey('');
	};
	const getUsers = async () => {
		const obj = {
			page: activePage,
			limit: TABLE.LIMIT,
			profile_status: [PROFILE_STATUS.PENDING]
		};

		if (debounceSearch.trim()) {
			obj.page = 1;
			obj.search = searchKey;
		}
		setLoading(true);
		const response = await dispatch(getFabricatorUsers(obj));
		if (response?.type === 'getFabricatorUsers/fulfilled') {
			setData(response?.payload?.data?.fabricators || []);
			setPages(Math.ceil(response.payload.data.totalCount / TABLE.LIMIT));
			setCount(response?.payload?.data?.totalCount);
		} else {
			setData([]);
			setPages(1);
			setCount(0);
		}
		setLoading(false);
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleApprovedUser = async (datas) => {
		let params = {
			user_id: datas.user_role.user_id,
			role_id: datas.user_role.Role.id,
			role_name: datas.user_role.Role.name,
			profile_status: PROFILE_STATUS.APPROVED
		};
		Swal.fire(CONFIRM_APPROVE_POPUP).then((result) => {
			if (result.isConfirmed) {
				postApi(ROLES.APPROVE_PROFILE, { ...params })
					.then((response) => {
						if (response.data.status) {
							getUsers();
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};
	const handleRejectUser = async (datas) => {
		try {
			const response = await postApi(ROLES.APPROVE_PROFILE, { ...datas });
			if (response.data.status) {
				getUsers();
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error);
		}
	};

	return (
		<>
			<FabricatorWrapper>
				<PageTitle
					title="sidebar.registerUser"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>

				<div className="pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</FabricatorWrapper>
			<Toaster ref={toaster} />
			{addOrEditModalData.open && (
				<ViewOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
					handleRejectUser={handleRejectUser}
				/>
			)}
		</>
	);
};

export default RegisteredUser;
