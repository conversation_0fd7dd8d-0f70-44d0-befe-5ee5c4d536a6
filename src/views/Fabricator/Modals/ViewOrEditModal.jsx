/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Dropdown,
	DropdownToggle,
	DropdownMenu,
	DropdownItem
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { PROFILE_STATUS, REASON_OTHER } from 'src/helper/constant';
import { AddOrEditModalWrapper } from '../Fabricator.style';
import { KYC, REASONS } from 'src/helper/api/endPoint';
import { Api, getApi } from 'src/helper/api/Api';
import ImageViewer from 'react-simple-image-viewer';
import pdfIcon from '../../../assets/images/pdf.svg';
import { downloadFile, extractFileName, groupBy } from 'src/helper/functions';
import IntlMessages from 'src/util/intlMessages';
import CODES from 'src/helper/StatusCodes';
import { Accordion, AccordionBody, AccordionHeader, AccordionItem } from 'reactstrap';

const AddOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		reason: '',
		reasonId: ''
	});
	const [formErrors, setFormErrors] = useState({ reason: '' });
	const [fields, setFields] = useState([]);
	const [kycDocs, setKycDocs] = useState([]);
	const [kycImages, setKycImages] = useState([]);
	const handleCLoseModal = props.handleChangeAddOrEditModal({ open: false, data: null });
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [allReasons, setAllReasons] = useState([]);
	const [dropdownOpen, setDropdownOpen] = useState(false);

	const [openAccordion, setOpenAccordion] = useState('user_1');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);
	const getReasons = async () => {
		try {
			const response = await getApi(REASONS.GET_REASONS, {});
			if (response?.status === CODES.SUCCESS) {
				const data = response?.data?.data?.reasons;
				setAllReasons(data);
			}
		} catch (error) {
			setAllReasons([]);
		}
	};
	useEffect(() => {
		const userData = props.data;
		if (userData?.kyc_data?.type?.name) {
			getFieldsBasedOnUserType(userData?.kyc_data?.type?.name);
		}
		if (props.reject) {
			getReasons();
		}
	}, [props.data]);

	const handleChange = ({ target }) => {
		const newReason = target.value;
		const reasonObj = structuredClone(formValues);
		reasonObj.reason = newReason;
		setFormValues(reasonObj);

		const errorMessage = validateReason(newReason);
		setFormErrors({ reason: errorMessage });
	};

	const handleChangeReason = (value) => {
		const formValue = structuredClone(formValues);
		formValue.id = value.id;
		formValue.type = value.type;
		formValue.reason = value.type === REASON_OTHER ? '' : value.reason;
		setFormValues(formValue);
	};

	const validateReason = (reason) => {
		if (!reason.trim()) {
			return 'Reason is required!';
		}
		return ''; // No error
	};

	const handleChangeImageViewer = (open, images, imageIndex) => () => {
		setImageViewer({ open, images });
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessage = validateReason(formValues.reason);
			setFormErrors({ reason: errorMessage });

			if (!errorMessage) {
				handleCLoseModal();
				const obj = {
					user_id: props.data.user_role.user_id,
					role_id: props.data.user_role.Role.id,
					role_name: props.data.user_role.Role.name,
					profile_status: PROFILE_STATUS.REJECTED,
					reason_id: formValues.id
				};
				if (formValues.type === REASON_OTHER) {
					obj.reason = formValues.reason;
				}

				props.handleRejectUser(obj);
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const getFieldsBasedOnUserType = async (type) => {
		const obj = {
			type: type
		};

		const response = await getApi(KYC.GET_KYC_FIELDS, { ...obj });
		if (response?.data?.data?.types?.length) {
			const all = response?.data?.data?.types[0];
			setFields(all?.kyc_types);
			const docs = [];
			const images = [];
			props?.data?.kyc_docs?.map((item) => {
				if (item.file_url.includes('.pdf')) {
					docs.push(item);
				} else {
					images.push(item);
				}
			});
			setKycDocs(docs);
			const imagesData = groupBy(images, 'field');
			setKycImages(imagesData);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.view ? 'View' : 'Reject'} Fabricator User
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{props.data && props.view && (
							<>
								<Accordion open={openAccordion} toggle={toggleAccordion}>
									<AccordionItem>
										<AccordionHeader targetId="user_1">
											User Details
										</AccordionHeader>
										<AccordionBody accordionId="user_1">
											{props.data.profile_image_url && (
												<div className="mb-10 d-flex flex-column">
													<strong className="mb-10">
														Profile image:
													</strong>{' '}
													<img
														className="profile-img-wrapper cursor-pointer"
														src={props.data.profile_image_url}
														alt={props.data.profile_image_key}
														onClick={handleChangeImageViewer(
															true,
															[props.data.profile_image_url],
															0
														)}
													/>
												</div>
											)}
											{props.data.first_name && props.data.last_name && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Name:</strong>{' '}
													{props.data.first_name +
														' ' +
														props.data.last_name}
												</div>
											)}
											{props.data.email && (
												<div className="mb-10 mt-10">
													<strong>Email:</strong> {props.data.email}{' '}
													{props.data.is_email_verified &&
													props.data.is_email_verified ? (
														<span className="badge text-bg-primary fw-normal">
															Verified
														</span>
													) : (
														<span className="badge text-bg-danger fw-normal">
															Not Verified
														</span>
													)}
												</div>
											)}
											{props.data.mobile && (
												<div className="mb-10 mt-10">
													<strong>Mobile:</strong>{' '}
													{props.data.country_code} {props.data.mobile}{' '}
													{props.data.is_mobile_verified &&
													props.data.is_mobile_verified ? (
														<span className="badge text-bg-primary fw-normal">
															Verified
														</span>
													) : (
														<span className="badge text-bg-danger fw-normal">
															Not Verified
														</span>
													)}
												</div>
											)}
											{props.data.address[0]?.city && (
												<div className="mb-10 mt-10">
													<strong>City:</strong>{' '}
													{props.data.address[0].city}
												</div>
											)}
											{props.data.address[0].state && (
												<div className="mb-10 mt-10">
													<strong>State:</strong>{' '}
													{props.data.address[0].state}
												</div>
											)}
											{props.data.address?.[0]?.zone && (
												<div>
													<strong>Zone:</strong>{' '}
													{props.data.address[0].zone.name}
												</div>
											)}
											{props.data?.user_role?.Role.name && (
												<div className="mb-10 mt-10">
													<strong>User Role:</strong>{' '}
													{props.data.user_role.Role.name}
												</div>
											)}

											{props.data?.kyc_data?.type.name && (
												<div className="mb-10 mt-10">
													<strong>User Type:</strong>{' '}
													{props.data.kyc_data.type.name}
												</div>
											)}
											{props.data.user_skills.length ? (
												<div className="mb-10 mt-10">
													<strong>User Skills:</strong>{' '}
													{props.data?.user_skills.map((items) => (
														<span
															className="badge text-bg-secondary mr-5 fw-normal"
															key={items.skills_data.id}>
															{items.skills_data.title}
														</span>
													))}
												</div>
											) : null}
											{props.data?.requester_user_review_history?.[0] &&
												props?.data.profile_status !==
													PROFILE_STATUS.APPROVED && (
													<div className="mb-10 mt-10">
														<strong>
															{props.profile_status ===
																PROFILE_STATUS.PENDING &&
																'Last'}{' '}
															Reason:
														</strong>{' '}
														{props.data
															?.requester_user_review_history?.[0]
															?.reason
															? props.data
																	?.requester_user_review_history?.[0]
																	?.reason
															: props.data
																	?.requester_user_review_history?.[0]
																	?.profile_reject_reason?.reason}
													</div>
												)}
										</AccordionBody>
									</AccordionItem>
									{fields?.length || kycDocs?.length || kycImages?.length ? (
										<>
											<AccordionItem>
												<AccordionHeader targetId="user_2">
													KYC Details
												</AccordionHeader>
												<AccordionBody accordionId="user_2">
													{fields.map(
														(item) =>
															props.data.kyc_data[
																item?.kyc_field?.key
															] && (
																<div
																	key={item?.kyc_field?.title}
																	className="py-1">
																	<strong>
																		{item?.kyc_field?.title}
																		{' :'}
																	</strong>{' '}
																	{
																		props.data.kyc_data[
																			item?.kyc_field?.key
																		]
																	}
																</div>
															)
													)}
												</AccordionBody>
											</AccordionItem>
											<AccordionItem>
												<AccordionHeader targetId="user_3">
													Images & Documents Details
												</AccordionHeader>
												<AccordionBody accordionId="user_3">
													<div>
														{kycImages.map((item, index) => (
															<div key={item?.key}>
																<strong>
																	<IntlMessages id={item?.key} />
																</strong>
																<div className="kyc-doc-container">
																	{item?.value.map((kycDoc) => (
																		<img
																			className="kyc-doc cursor-pointer"
																			key={kycDoc.id}
																			src={kycDoc.file_url}
																			alt=""
																			onClick={handleChangeImageViewer(
																				true,
																				[kycDoc.file_url],
																				index
																			)}
																		/>
																	))}
																</div>
															</div>
														))}
														<hr />
													</div>
													<div className="">
														{kycDocs.map((item, i, row) => (
															<div key={item?.field}>
																<strong>
																	<IntlMessages
																		id={item?.field}
																	/>
																</strong>
																<div
																	className="d-flex py-2 gap-2 align-items-center cursor-pointer"
																	onClick={() =>
																		downloadFile(
																			item.file_url,
																			extractFileName(
																				item.file_url
																			)
																		)
																	}>
																	<img
																		src={pdfIcon}
																		className="avatar avatar-lg"
																	/>
																	<div className="font-14 m-0">
																		{extractFileName(
																			item.file_url
																		)}
																		.pdf
																	</div>
																</div>
																{i + 1 === row.length ? null : (
																	<hr />
																)}
																{/* {props.data.kyc_data[item?.kyc_field?.key]} */}
															</div>
														))}
													</div>
												</AccordionBody>
											</AccordionItem>
										</>
									) : null}
								</Accordion>
							</>
						)}

						{props.data && props.reject && (
							<>
								<FormGroup>
									<Label>Reason</Label>
									<Dropdown
										className="mt-10 mb-4"
										isOpen={dropdownOpen}
										toggle={toggleRoleDropDown}>
										<DropdownToggle caret className="roleBtn text-truncate">
											{formValues.type === REASON_OTHER
												? 'Other'
												: formValues.reason || 'Select Reason'}
										</DropdownToggle>

										<DropdownMenu className="w100" style={{ maxWidth: '50%' }}>
											{allReasons.length ? (
												allReasons.map((role, index) => {
													return (
														<DropdownItem
															key={index}
															onClick={() => {
																handleChangeReason(role);
															}}
															className="text-truncate">
															{role.reason}
														</DropdownItem>
													);
												})
											) : (
												<DropdownItem disabled> No Data </DropdownItem>
											)}
										</DropdownMenu>
									</Dropdown>
									{formValues.type === REASON_OTHER && (
										<>
											<Input
												id="reason"
												value={formValues.reason}
												onChange={handleChange}
												type="textarea"
												placeholder="Type reject reason..."
											/>
										</>
									)}
									<span className="error-msg mt-10">{formErrors.reason}</span>
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							{props.data && !props.view && (
								<Button
									loading={saveLoading}
									type="submit"
									className="btn form-button">
									Reject
								</Button>
							)}
						</div>

						<div>
							<Button
								onClick={handleCLoseModal}
								disabled={saveLoading}
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
			{imageViewer.open && (
				<ImageViewer
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default AddOrEditModal;
