/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import FabricatorWrapper from './Fabricator.style';
import PageTitle from 'src/components/common/PageTitle';
import EditIcon from 'src/assets/images/Edit.svg';
import ViewIcon from 'src/assets/images/View.svg';
import ReactTable from 'react-table';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import Pagination from '../../components/Pagination/Pagination';
import Loader from '../../components/common/Loader';
import { useDispatch } from 'react-redux';
import { getFabricatorUsers } from 'src/redux/apis/usersAPIs';
import { convertTimeToLocal } from 'src/helper/functions';
import AddOrEditModal from './Modals/ViewOrEditModal';
import useDebounce from 'src/util/hooks/useDebounce';
import crossIcon from '../../assets/images/cross.svg';
import searchIcon from '../../assets/images/search.svg';
import Toaster from 'src/components/common/Toaster';
import { useRef } from 'react';
import Switch from 'src/components/Switch/Switch';
import { PROFILE } from 'src/helper/api/endPoint';
import { postApi } from 'src/helper/api/Api';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';

const ReactTableFixedColumns = withFixedColumns(ReactTable);
const RegisteredUser = () => {
	const toaster = useRef();
	const dispatch = useDispatch();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 400);
	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				},
				{
					Header: ' User Type',
					minWidth: 170,
					accessor: 'kyc_data.type.name',
					resizable: false,
					default: '-',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'City',
					minWidth: 150,
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Zone',
					minWidth: 150,
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Orders Count',
					minWidth: 150,
					accessor: 'assigned_orders_count',
					resizable: false,
					default: 0,
					headerClassName: 'text-left pa-20',
					className: 'pa-20 d-flex align-items-center justify-content-center',
					Cell: ({ value }) => (value ? value : 0)
				},

				{
					Header: 'Created On',
					resizable: false,
					minWidth: 150,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				},
				{
					Header: 'Active',
					minWidth: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					// minWidth: 150,
					resizable: false,
					Cell: (cell) => (
						<>
							<div className="">
								<img
									src={ViewIcon}
									title="View"
									width={23}
									className="mr-10 cursor-pointer"
									onClick={handleChangeAddOrEditModal({
										open: true,
										view: true,
										data: cell.original,
										profile_status: PROFILE_STATUS.APPROVED
									})}
								/>
							</div>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];

	useEffect(() => {
		if (debounceSearch?.length > 2 || !debounceSearch) {
			getUsers();
		}
	}, [debounceSearch, activePage]);
	const getUsers = async () => {
		const obj = {
			page: activePage,
			limit: TABLE.LIMIT,
			profile_status: [PROFILE_STATUS.APPROVED]
		};
		if (debounceSearch.trim()) {
			obj.page = 1;
			obj.search = searchKey;
		}
		setLoading(true);
		const response = await dispatch(getFabricatorUsers(obj));
		if (response?.type === 'getFabricatorUsers/fulfilled') {
			setData(response?.payload?.data?.fabricators || []);
			setPages(Math.ceil(response.payload?.data.totalCount / TABLE.LIMIT));
			setCount(response?.payload?.data?.totalCount);
		} else {
			setData([]);
			setPages(1);
			setCount(0);
		}
		setLoading(false);
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(PROFILE.UPDATE_ACTIVE_INACTIVE, {
					user_id: id,
					status: is_active ? 1 : 0
				});

				if (response.status === 200) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);
	return (
		<>
			<FabricatorWrapper>
				<PageTitle
					title="sidebar.verifiedUser"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>

				<div className="pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</FabricatorWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
				/>
			)}
		</>
	);
};

export default RegisteredUser;
