import { withF<PERSON><PERSON> } from "formik";
import * as Yup from "yup";

const ResetPasswordValidator = withFormik({
    validationSchema: Yup.object().shape({
        password: Yup
            .string()
            .trim()
            .required("Passsword is required."),
        confirmPassword: Yup
            .string()
            .required("Confirm password is required")
    }),
    validate: values => {
        const errors = {};
        const pattern = new RegExp(".*[@#$%^&+=]");
        const pattern2 = new RegExp("(?=.*[A-Z])");
        const pattern3 = new RegExp("(?=.*[0-9])");

        if (values.password) {
            if (values.password.length < 8) {
                errors.password = "password must have 8 characters";
            } else if (!pattern.test(values.password)) {
                errors.password = `password must have at-least one special character `;
            } else if (!pattern2.test(values.password)) {
                errors.password = `password must have at-least one Capital Letter `;
            } else if (!pattern3.test(values.password)) {
                errors.newPassword = `password must have at-least one Number `;
            }
        }

        if (
            values.confirmPassword &&
            values.password !== values.confirmPassword
        ) {
            errors.confirmPassword = "password not matched";
        }
        return errors;
    },
    mapPropsToValues: props => ({
        password: props.values?.password || "",
        confirmPassword: ""
    }),
    handleSubmit: values => { },
    displayName: "ForgotPasswordValidator",
    enableReinitialize: true
});

export default ResetPasswordValidator;
