/* eslint-disable no-unused-vars */
import React, { useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Input, InputGroup, InputGroupText, Label } from 'reactstrap';

import { ROUTES } from '../../helper/constant';
import { LoginStyles } from '../login/login.style';
import Toaster from '../../components/common/Toaster';
import Button from '../../components/button/Button';
import personImage from '../../assets/images/personimage.jpg';
import { postApi } from '../../helper/api/Api';
import Error from '../../components/common/Error';
import { PASSWORD } from '../../helper/api/endPoint';

import ICON_DEMO from '../../assets/images/Logo.svg';
import FORGOT_ICON from '../../assets/images/forgotpassword.svg';
import EYE_ICON from '../../assets/images/eye-svgrepo-com.svg';
import EYE_DISABLE_ICON from '../../assets/images/eye-slash-svgrepo-com.svg';
import * as Yup from 'yup';
import { useFormik } from 'formik';
const ResetPassword = (props) => {
	const toaster = useRef();
	const navigateTo = useNavigate();
	const location = useLocation();
	const [loading, setLoading] = useState(false);
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmedPassword, setShowConfirmedPassword] = useState(false);

	const {
		handleChange,
		handleSubmit,
		values,
		isValid,
		errors,
		handleBlur,
		touched,
		setValues,
		setFieldValue
	} = useFormik({
		validationSchema: Yup.object().shape({
			password: Yup.string()
				.trim()
				.matches(/.*[@#$%^&+=]/, 'Password must have at least one special character')
				.matches(/.*[A-Z]/, 'Password must have at least one capital letter')
				.matches(/.*[0-9]/, 'Password must have at least one number')
				.required('Passsword is required.'),
			confirmPassword: Yup.string()
				.oneOf([Yup.ref('password'), null], 'Passwords must match')
				.required('Confirm password is required')
		}),
		initialValues: {
			password: '',
			confirmPassword: ''
		},
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			submitHandler();
		}
	});

	const submitHandler = (e) => {
		if (!values.password & !values.confirmPassword) {
			return;
		}
		if (!isValid) {
			return;
		}
		if (isValid) {
			let params = {};

			if (location?.state?.activeTab === 'EMAIL') {
				params = {
					...params,
					email: location.state?.email,
					type: 'EMAIL',
					password: values.password,
					password_confirmation: values.confirmPassword
				};
			} else {
				params = {
					...params,
					type: 'MOBILE',
					password: values.password,
					password_confirmation: values.confirmPassword,
					country_code: '+91',
					mobile: location?.state?.mobile
				};
			}
			setLoading(true);

			postApi(PASSWORD.RESET_PASSWORD, params)
				.then((response) => {
					if (response) {
						toaster.current.success(response.data.message);
						setLoading(false);
						setTimeout(() => {
							// props.history.push(ROUTES.LOGIN);
							navigateTo(ROUTES.LOGIN);
						}, 1000);
					} else {
						setLoading(false);
						toaster.current.error(error?.response?.data?.message);
					}
				})
				.catch((error) => {
					toaster.current.error(error.response.data.message);
				})
				.finally(() => {
					setLoading(false);
				});
		}
	};

	const togglePasswordVisibility = (type) => {
		if (type === 'password') {
			setShowPassword((prevShowPassword) => !prevShowPassword);
		} else {
			setShowConfirmedPassword((prevShowConfirmedPassword) => !prevShowConfirmedPassword);
		}
	};
	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	return (
		<>
			<LoginStyles {...props}>
				<div className="loginContainer">
					<div className="img_div">
						<img src={personImage} alt="person" className="img"></img>
					</div>
					<div className="form_container">
						<div className="form">
							<div className="login-icon">
								<div className="loginIcon">
									<img src={ICON_DEMO} alt="icon" height="120px" width="240px" />
								</div>
							</div>

							<img src={FORGOT_ICON} alt="icon" height="60px" />
							<div className="Title">Reset Password</div>

							<form className="pa-24">
								<div className="form-group">
									<Label>Password</Label>
									<InputGroup>
										<Input
											type={showPassword ? 'text' : 'password'}
											placeholder="Enter password"
											onPaste={(e) => {
												e.preventDefault();
												return false;
											}}
											className={`form-control form-control-lg react-form-input password`}
											{...formAttributes('password')}
										/>
										<InputGroupText
											onClick={() => togglePasswordVisibility('password')}>
											{showPassword ? (
												<img
													src={EYE_ICON}
													alt="eye-icon"
													className="show-hide-icon"
												/>
											) : (
												<img
													src={EYE_DISABLE_ICON}
													alt="eye-icon"
													className="show-hide-icon"
												/>
											)}
										</InputGroupText>
									</InputGroup>

									<Error errors={errors} touched={touched} fieldName="password" />
								</div>

								<div className="form-group mt-10">
									<Label>Confirm Password</Label>
									<InputGroup>
										<Input
											type={showConfirmedPassword ? 'text' : 'password'}
											placeholder="Confirm password"
											onPaste={(e) => {
												e.preventDefault();
												return false;
											}}
											className={`form-control form-control-lg react-form-input password`}
											{...formAttributes('confirmPassword')}
										/>
										<InputGroupText
											onClick={() =>
												togglePasswordVisibility('confirmPassword')
											}>
											{showConfirmedPassword ? (
												<img
													src={EYE_ICON}
													alt="eye-icon"
													className="show-hide-icon"
												/>
											) : (
												<img
													src={EYE_DISABLE_ICON}
													alt="eye-icon"
													className="show-hide-icon"
												/>
											)}
										</InputGroupText>
									</InputGroup>
									<Error
										errors={errors}
										touched={touched}
										fieldName="confirmPassword"
									/>
								</div>

								<div className="center">
									<Button
										loading={loading}
										className="btnText primaryFormButton"
										dataStyle="expand-right"
										style={{ position: 'relative' }}
										onClick={handleSubmit}>
										reset
									</Button>
								</div>
							</form>

							<Toaster ref={toaster} />
						</div>
					</div>
				</div>
			</LoginStyles>
		</>
	);
};

export default ResetPassword;
