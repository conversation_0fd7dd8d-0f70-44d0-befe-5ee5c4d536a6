/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Button from '../../../components/button/Button';
import SingleDropdown from '../../../components/SingleDropdown/SingleDropdown';
import { AddOrEditModalWrapper } from '../Question.style';

const type = [
	{ id: 'number', name: 'Number' },
	{ id: 'string', name: 'String' }
];

const AddOrEditModalTAT = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [selectedType, setSelectedType] = useState(null);

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const validationSchema = Yup.object().shape({
		title: Yup.string().required('Tag name is required!'),
		value: Yup.mixed().when('type', {
			is: 'number',
			then: (schema) =>
				Yup.number()
					.positive('Value must be greater than 0!')
					.integer('Value must be integer!')
					.required('Value is required!')
					.typeError('Value must be a valid number!'),
			otherwise: (schema) => Yup.string().required('Value is required!')
		})
	});
	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			let submissionData = {
				value: formik?.values?.value,
				type: formik?.values?.type,
				id: props?.data?.id
			};
			props?.editTag(submissionData);
			setSaveLoading(false);
			handleCLoseModal();
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const handleChangeTypeDropdown = (selected) => {
		setSelectedType(selected);
		formik.setFieldValue('type', selected.id);
	};

	const formik = useFormik({
		initialValues: {
			title: '',
			value: '',
			type: 'number'
		},
		validationSchema,
		validateOnChange: true,
		onSubmit: onSubmitHandler
	});

	useEffect(() => {
		if (props?.data) {
			formik?.setValues({
				title: props?.data?.title,
				type: props?.data?.type,
				value: props?.data?.value
			});
			// Set selected type for dropdown
			const typeData = type.find(t => t.id === props?.data?.type);
			setSelectedType(typeData);
		}
	}, [props?.data]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}>
				<ModalHeader toggle={handleCLoseModal}>Edit TAT</ModalHeader>
				<Form onSubmit={formik?.handleSubmit}>
					<ModalBody>
						<FormGroup className="mb-10">
							<Label for="title">Title </Label>
							<Input
								id="title"
								name="title"
								value={formik?.values?.title}
								onChange={formik?.handleChange}
								onBlur={formik?.handleBlur}
								type="text"
								placeholder="Title"
								disabled
							/>
							{formik?.touched?.title && formik?.errors?.title && (
								<span className="error-msg mt-0">{formik.errors.title}</span>
							)}
						</FormGroup>
						<FormGroup className="mt-10">
							<Label for="type">Type</Label>
							<SingleDropdown
								data={type}
								keyProps={['name']}
								onSelect={handleChangeTypeDropdown}
								selectedData={selectedType}
								className={'w-100 text-left'}
							/>
							{formik.touched.type && formik.errors.type && (
								<span className="error-msg mt-0">
									{formik.errors.type}
								</span>
							)}
						</FormGroup>
						<FormGroup className="mt-10">
							<Label for="value">Value</Label>
							<Input
								id="value"
								name="value"
								value={formik?.values?.value}
								onChange={formik?.handleChange}
								onBlur={formik?.handleBlur}
								type="value"
								placeholder="Value"
							/>
							{formik?.touched?.value && formik?.errors?.value && (
								<span className="error-msg mt-0">{formik.errors.value}</span>
							)}
						</FormGroup>
					</ModalBody>
					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalTAT;
