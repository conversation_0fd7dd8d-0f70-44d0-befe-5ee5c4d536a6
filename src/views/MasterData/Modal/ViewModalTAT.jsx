import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';

const ViewModalTAT = (props) => {
	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View TAT</ModalHeader>

				<ModalBody>
					<div>
						<div className="mb-10 mt-10 text-capitalize">
							<strong>Title :</strong> {props?.data?.title ? props?.data?.title : '-'}
						</div>
						<div className="mb-10 mt-10 text-capitalize">
							<strong>Value :</strong> {props?.data?.value}
						</div>
						<div className="mb-10 mt-10 text-capitalize">
							<strong>Type of value :</strong> {props?.data?.type}
						</div>
					</div>
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalTAT;
