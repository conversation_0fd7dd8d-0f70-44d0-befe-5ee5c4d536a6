/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import ViewIcon from '../../assets/images/View.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { TAT } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import Toaster from 'src/components/common/Toaster';
import QuestionWrapper from './Question.style';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import EditIcon from 'src/assets/images/Edit.svg';
import ViewModalQuesTags from './Modal/ViewModalTAT';
import AddOrEditModalTAT from './Modal/AddOrEditModalTAT';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

let timeoutVar;

const TAT_page = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const [debounceSearchKey, setDebounceSearchKey] = useState('');
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true,
		edit: false
	});
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			const { value, type } = data[index];
			try {
				setLoading(true);
				const response = await postApi(
					TAT.UPDATE,
					{
						id: id,
						is_active: is_active ? true : false,
						value,
						type
					},
					'order'
				);

				if (response?.status === CODES.SUCCESS) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Title',
					resizable: false,
					Cell: (row) => `${row?.original?.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				},
				{
					Header: 'Value',
					resizable: false,
					Cell: (row) => `${row?.original?.value}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => {
						return (
							<Switch
								checked={cell?.original?.is_active}
								onChange={() =>
									handleChangeActiveInactive(
										cell?.original?.id,
										!cell?.original?.is_active
									)
								}
							/>
						);
					},
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={EditIcon}
								alt="EditIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: false,
									edit: true,
									data: cell?.original
								})}
							/>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal({
									open: true,
									data: cell?.original,
									view: true
								})}
							/>
						</>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);
	const handleChangeViewModal = (params) => () => setViewModalData(params);

	useEffect(() => {
		getTATs();
	}, [activePage, debounceSearchKey]);

	const handleSearchInputChange = (value) => {
		setSearchKey(value);
		if (timeoutVar) {
			clearTimeout(timeoutVar);
		}
		timeoutVar = setTimeout(() => {
			setActivePage(1);
			setDebounceSearchKey(value);
		}, 300);
	};

	const getTATs = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};

			if (debounceSearchKey?.trim()) {
				dataToSend.search = debounceSearchKey;
			}
			const response = await getApi(
				TAT.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				if (response?.data?.data?.data) {
					setData(response?.data?.data?.data);
				 	setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				 	setCount(response?.data?.data?.totalCount);
				 }
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const editTag = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(TAT.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getTATs();
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<QuestionWrapper>
				<PageTitle
					title="sidebar.tat"
					search={true}
					searchKey={searchKey}
					setSearchKey={handleSearchInputChange}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</QuestionWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModalTAT
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					editTag={editTag}
				/>
			)}
			{viewModalData.open && (
				<ViewModalQuesTags
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}
		</>
	);
};

export default TAT_page;
