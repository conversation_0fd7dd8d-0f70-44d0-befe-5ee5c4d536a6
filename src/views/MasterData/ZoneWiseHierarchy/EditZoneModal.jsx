import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody, ModalFooter, Button, Form, FormGroup, Label, Input } from 'reactstrap';

const EditZoneModal = ({ open, zone, values, onSave, onClose }) => {
  const [form, setForm] = useState({ ...values });
  const [loading, setLoading] = useState(false);

  // Only update form when the zone changes (not on every value change)
  useEffect(() => {
    setForm({ ...values });
  }, [zone]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    // Always store as string, allow empty string
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSave(zone, {
        se: form.se === '' ? null : Number(form.se),
        zh_from: form.zh_from === '' ? null : Number(form.zh_from),
        zh_to: form.zh_to === '' ? null : Number(form.zh_to),
        sh: form.sh === '' ? null : Number(form.sh),
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={open} toggle={onClose} centered size="md">
      <ModalHeader toggle={onClose}>Edit {zone} Zone</ModalHeader>
      <Form onSubmit={handleSubmit} autoComplete="off">
        <ModalBody>
          <FormGroup>
            <Label>Service Engineer approval - Less than</Label>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Input
                type="number"
                name="se"
                value={form.se}
                onChange={handleChange}
                min={0}
                style={{ width: '90%' }}
                placeholder="Enter amount"
                autoComplete="off"
              />
              <span>Lakhs</span>
            </div>
          </FormGroup>
          <FormGroup>
            <Label>Zonal Head approval -</Label>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Input
                type="number"
                name="zh_from"
                value={form.zh_from}
                onChange={handleChange}
                min={0}
                style={{ flex: 1 }}
                placeholder="From"
                autoComplete="off"
              />
              <span>to</span>
              <Input
                type="number"
                name="zh_to"
                value={form.zh_to}
                onChange={handleChange}
                min={0}
                style={{ flex: 1 }}
                placeholder="To"
                autoComplete="off"
              />
              <span>Lakhs</span>
            </div>
          </FormGroup>
          <FormGroup>
            <Label>Service Head approval - greater than</Label>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Input
                type="number"
                name="sh"
                value={form.sh}
                onChange={handleChange}
                min={0}
                style={{ width: '90%' }}
                placeholder="Enter amount"
                autoComplete="off"
              />
              <span>Lakhs</span>
            </div>
          </FormGroup>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" type="submit" disabled={loading}>
            {loading ? 'Saving...' : 'Save'}
          </Button>
          <Button color="secondary" onClick={onClose} type="button" disabled={loading}>
            Cancel
          </Button>
        </ModalFooter>
      </Form>
    </Modal>
  );
};

export default EditZoneModal; 