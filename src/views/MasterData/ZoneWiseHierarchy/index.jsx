import React, { useState, useRef } from 'react';
import EditZoneModal from './EditZoneModal';
import EditIcon from 'src/assets/images/Edit.svg';
import PageTitle from 'src/components/common/PageTitle';
import Loader from 'src/components/common/Loader';
import Toaster from 'src/components/common/Toaster';
import NorthZoneIcon from 'src/assets/images/Sidebar/north-zone.svg';
import SouthZoneIcon from 'src/assets/images/Sidebar/south-zone.svg';
import EastZoneIcon from 'src/assets/images/Sidebar/east-zone.svg';
import WestZoneIcon from 'src/assets/images/Sidebar/west-zone.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { ZONE_BASED_AMOUNT } from 'src/helper/api/endPoint';

const ZONE_DATA = [
  { key: 'North', name: 'North Zone', icon: NorthZoneIcon, color: '#0ea5e9', bg: '#f0f9ff' },
  { key: 'South', name: 'South Zone', icon: SouthZoneIcon, color: '#16a34a', bg: '#f0fdf4' },
  { key: 'East', name: 'East Zone', icon: EastZoneIcon, color: '#ea580c', bg: '#fff7ed' },
  { key: 'West', name: 'West Zone', icon: WestZoneIcon, color: '#c026d3', bg: '#fdf4ff' },
];

const DEFAULTS = {
  se: '',
  zh_from: '',
  zh_to: '',
  sh: ''
};

const ZoneWiseHierarchy = () => {
  const toaster = useRef();
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState(null);
  const [zoneValues, setZoneValues] = useState(DEFAULTS);
  const [zoneIds, setZoneIds] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleEdit = (zone) => {
    setLoading(true);
    setError(null);
    getApi(ZONE_BASED_AMOUNT.GET, { page: 1, limit: 12, zone }, 'zone')
      .then((res) => {
        const arr = res?.data?.data?.zone_based_amounts || [];
        let values = { se: '', zh_from: '', zh_to: '', sh: '' };
        let ids = {};
        arr.forEach((item) => {
          if (item.level === 1) {
            values.se = item.min_amount;
            ids.se = item.id;
          } else if (item.level === 2) {
            values.zh_from = item.min_amount;
            values.zh_to = item.max_amount;
            ids.zh = item.id;
          } else if (item.level === 3) {
            values.sh = item.min_amount;
            ids.sh = item.id;
          }
        });
        setZoneValues(values);
        setZoneIds(ids);
        setSelectedZone(zone);
        setModalOpen(true);
      })
      .catch((err) => {
        setError('Failed to load zone data');
        toaster.current?.error('Failed to load zone data');
      })
      .finally(() => setLoading(false));
  };

  const handleSave = (zone, values) => {
    setLoading(true);
    setError(null);
    // Prepare API payload in the same format as GET response
    const payload = { zone_based_amounts: [] };
    const ids = zoneIds || {};
    if (values.se !== undefined)
      payload.zone_based_amounts.push({ id: ids.se, zone, level: 1, min_amount: String(values.se) });
    if (values.zh_from !== undefined && values.zh_to !== undefined)
      payload.zone_based_amounts.push({ id: ids.zh, zone, level: 2, min_amount: String(values.zh_from), max_amount: String(values.zh_to) });
    if (values.sh !== undefined)
      payload.zone_based_amounts.push({ id: ids.sh, zone, level: 3, min_amount: String(values.sh) });
    postApi(ZONE_BASED_AMOUNT.UPDATE, payload, 'zone')
      .then((response) => {
        setModalOpen(false);
        toaster.current?.success('Zone data updated successfully');
      })
      .catch((err) => {
        setError('Failed to update zone data');
        toaster.current?.error(err?.response?.data?.message || 'Failed to update zone data');
      })
      .finally(() => setLoading(false));
  };

  return (
    <div className="zone-wise-hierarchy-page" style={{ padding: 24, background: '#f6f8fa', minHeight: '100vh' }}>
      <PageTitle title="Zone Wise Hierarchy" />
      <Loader loading={loading} />
      {error && <div style={{ color: 'red', margin: 16 }}>{error}</div>}
      <div style={{ display: 'flex', flexDirection: 'column', gap: 24, marginTop: 24 }}>
        {ZONE_DATA.map((zone) => (
          <div
            key={zone.key}
            style={{
              background: zone.bg,
              borderRadius: 16,
              boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
              padding: 24,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
              <div style={{
                width: 56, height: 56, borderRadius: 12, background: zone.color, display: 'flex', alignItems: 'center', justifyContent: 'center',
              }}>
                <img src={zone.icon} alt={zone.name} style={{ width: 36, height: 36 }} />
              </div>
              <div>
                <div style={{ fontWeight: 700, fontSize: 20 }}>{zone.name}</div>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
              <img
                src={EditIcon}
                alt="Edit"
                style={{ cursor: 'pointer', width: 22, opacity: 0.7 }}
                onClick={() => handleEdit(zone.key)}
              />
            </div>
          </div>
        ))}
      </div>
      {modalOpen && (
        <EditZoneModal
          key={selectedZone}
          open={modalOpen}
          zone={selectedZone}
          values={zoneValues}
          onSave={handleSave}
          onClose={() => setModalOpen(false)}
        />
      )}
      <Toaster ref={toaster} />
    </div>
  );
};

export default ZoneWiseHierarchy; 