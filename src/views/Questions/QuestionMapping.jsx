/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { ROUTES, TABLE, allQuestionsTypes } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import ViewIcon from '../../assets/images/View.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { QUESTION_SET, SCOPES, WORK_ORDER_TYPE } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import Toaster from 'src/components/common/Toaster';
import { useNavigate } from 'react-router-dom';
import QuestionWrapper from './Question.style';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import ViewModalQuesMapping from './Modal/ViewModalQuesMapping';
import Switch from 'src/components/Switch/Switch';
import EditIcon from 'src/assets/images/Edit.svg';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const QuestionMapping = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const isFirstSearch = useRef(true);

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Work Order Type',
					resizable: false,
					Cell: (row) => `${row.original.work_order_type_data.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				},
				{
					Header: 'Scope Name',
					resizable: false,
					Cell: (row) => `${row.original.scope_data.name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				},
				{
					Header: 'Scope Model',
					resizable: false,
					Cell: (row) => `${row.original.scope_data.model}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={EditIcon}
								alt="EditIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={() => navigate(ROUTES.QUESTION_BANK.EDIT, {
									state: cell.original
								})}
							/>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal({
									open: true,
									data: cell.original,
									view: true
								})}
							/>
						</>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(
					QUESTION_SET.CHANGE_STATUS,
					{
						id,
						status: is_active ? 1 : 0
					},
					'order'
				);

				if (response.status === CODES.SUCCESS) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	const handleChangeViewModal = (params) => () => setViewModalData(params);

	useEffect(() => {
		if (!debounceSearch.trim()) {
			setActivePage(1);
			isFirstSearch.current = true;
			getQuestionMapping();
		} else if (debounceSearch.trim() && isFirstSearch.current) {
			setActivePage(1);
			isFirstSearch.current = false;
			getQuestionMapping();
		} else {
			getQuestionMapping();
		}
	}, [debounceSearch]);

	useEffect(() => {
		if (!debounceSearch.trim() || !isFirstSearch.current) {
			getQuestionMapping();
		}
	}, [activePage]);

	const getQuestionMapping = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};

			if (debounceSearch.trim()) {
				dataToSend.search = debounceSearch;
			}
			const response = await getApi(
				QUESTION_SET.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				if (response?.data?.data?.question_sets) {
					setData(response?.data?.data?.question_sets);
					setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
					setCount(response?.data?.data?.totalCount);
				}
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<QuestionWrapper>
				<PageTitle
					title="sidebar.questionMapping"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={() => navigate(ROUTES.QUESTION_BANK.ADD)}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
											filter.value.toLowerCase()
										)
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</QuestionWrapper>
			{viewModalData.open && (
				<ViewModalQuesMapping
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}
		</>
	);
};

export default QuestionMapping;
