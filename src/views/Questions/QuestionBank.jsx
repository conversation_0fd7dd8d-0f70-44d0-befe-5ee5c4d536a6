/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import QuestionWrapper from './Question.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE, allQuestionsTypes } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import ViewIcon from 'src/assets/images/View.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { QUESTION, QUESTION_TAG, SCOPES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';
import AddOrEditModalQuestionBank from './Modal/AddOrEditModalQuesBank';
import ViewModalQuesBank from './Modal/ViewModalQuesBank';
import { MdEditDocument } from 'react-icons/md';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const QuestionBank = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [questionType, setQuestionType] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [quesTypeId, setQuesTypeId] = useState(undefined);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [activeTab, setActiveTab] = useState(1);
	const [tags, setTags] = useState([]);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true,
		edit: false
	});
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const toggle = (tab) => {
		if (activeTab !== tab) {
			getQuestionBank(tab);
			setActivePage(1);
			setActiveTab(tab);
		}
	};

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Question',
					resizable: false,
					Cell: (row) => `${row.original.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 100
				},
				{
					Header: 'Edit',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<MdEditDocument
								onClick={() =>
									setAddOrEditModalData({
										open: true,
										view: false,
										edit: true,
										data: cell.original
									})
								}
								className="mr-10 cursor-pointer"
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 100
				}
			]
		}
	];

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(
					QUESTION.CHANGE_STATUS,
					{
						id: id,
						status: is_active ? 1 : 0
					},
					'order'
				);

				if (response.status === 200) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	const getTags = async () => {
		getApi(QUESTION_TAG.GET, {}, 'order')
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.report_tags;
					if (apiData?.length) {
						setTags(apiData);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const addTags = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(QUESTION.REPORT_TAGS_ADD, data, 'order', false);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getTags();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		getQuestionType();
		getTags();
	}, []);

	useEffect(() => {
		if (quesTypeId) {
			getQuestionBank(quesTypeId);
		}
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);
	const handleChangeViewModal = (params) => () => setViewModalData(params);

	const getQuestionType = async () => {
		const dataToSend = {
			page: 1,
			limit: TABLE.LIMIT
		};
		getApi(
			QUESTION.GET_TYPE,
			{
				...dataToSend
			},
			'order'
		)
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.question_types;
					if (apiData?.length) {
						const ascOrder = [...apiData].sort((a, b) => a.id - b.id);
						setQuestionType(ascOrder);
						setActiveTab(ascOrder?.[0]?.id);
						getQuestionBank(ascOrder?.[0]?.id);
					}
				}
			})
			.catch((error) => {
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const getQuestionBank = async (que_type_id) => {
		try {
			setLoading(true);
			setQuesTypeId(que_type_id);
			const dataToSend = {
				que_type_id,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(
				QUESTION.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.questions);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addQuestions = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(QUESTION.ADD, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getQuestionBank(quesTypeId);
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
		} finally {
			setLoading(false);
		}
	};
	const editQuestions = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(QUESTION.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getQuestionBank();
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<QuestionWrapper>
				<PageTitle
					title="sidebar.questionBank"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={handleChangeViewOrEditModal({
								open: true,
								view: false,
								edit: false,
								data: null
							})}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mt-8 mb-15">
						<div className="roe-card-body">
							<Loader loading={!questionType.length > 0} />
							<Nav tabs className="mb-10">
								{questionType.map((role) => (
									<NavItem key={role.id} className="cursor-pointer">
										<NavLink
											className={`${activeTab === role.id ? 'active' : ''}`}
											onClick={() => toggle(role.id)}>
											{role.title}
										</NavLink>
									</NavItem>
								))}
							</Nav>
							<TabContent activeTab={activeTab}>
								{allQuestionsTypes.map((role) => (
									<TabPane key={role.id} tabId={role.id}>
										<ReactTableFixedColumns
											manual
											data={data}
											pages={pages}
											sortable={false}
											columns={columns}
											page={activePage - 1}
											onPageChange={handleChangePage}
											totalCount={count}
											loading={loading}
											pageSize={TABLE.LIMIT}
											minRows={TABLE.MIN_ROW}
											LoadingComponent={Loader}
											PaginationComponent={Pagination}
											style={{ border: 'none', boxShadow: 'none' }}
											className="-striped -highlight custom-react-table-theme-class"
											defaultFilterMethod={(filter, row) => {
												const id = filter.pivotId || filter.id;
												return row[id] !== undefined
													? String(row[id].toLowerCase()).includes(
															filter.value.toLowerCase()
													  )
													: true;
											}}
										/>
									</TabPane>
								))}
							</TabContent>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</QuestionWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModalQuestionBank
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					questionType={questionType}
					editQuestions={editQuestions}
					addQuestions={addQuestions}
					addTags={addTags}
					tags={tags}
				/>
			)}
			{viewModalData.open && (
				<ViewModalQuesBank
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}
		</>
	);
};

export default QuestionBank;
