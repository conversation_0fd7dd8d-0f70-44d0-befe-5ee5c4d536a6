/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import QuestionWrapper from '../Question.style';
import PageTitle from 'src/components/common/PageTitle';
import { API_STATUS, ROUTES, TABLE } from 'src/helper/constant';
import { getApi, postApi } from 'src/helper/api/Api';
import { QUESTION, QUESTION_SET, SCOPES, WORK_ORDER_TYPE } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import Toaster from 'src/components/common/Toaster';
import { useLocation, useNavigate } from 'react-router-dom';
import {
	Accordion,
	AccordionBody,
	AccordionHeader,
	AccordionItem,
	Label,
	FormGroup,
	Input,
	Form
} from 'reactstrap';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Loader from 'src/components/common/Loader';
import InfiniteScroll from 'react-infinite-scroll-component';
import ScrollLoader from 'src/components/ScrollLoader/ScrollLoader';

const EditQuestionMapping = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const location = useLocation();
	const dataOfEdit = location.state;
	const [hasMoreQues, setHasMoreQues] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [data, setData] = useState([]);
	const [workOrderType, setWorkOrderType] = useState([]);
	const [scope, setScope] = useState([]);
	const [touchPoint, setTouchPoint] = useState([]);
	const [loading, setLoading] = useState(false);
	const [openAccordion, setOpenAccordion] = useState('quesMapping_1');
	const [selectedDataWork, setSelectedDataWork] = useState(null);
	const [selectedDataScope, setSelectedDataScope] = useState(null);
	const [accordionCheckboxes, setAccordionCheckboxes] = useState({});
	const [touchPointValidation, setTouchPointValidation] = useState({});

	const initialValues = {
		workOrderType: '',
		scope: '',
		selectedCheckboxes: []
	};

	const validationSchema = Yup.object().shape({
		workOrderType: Yup.string().required('Work Order Type is required'),
		scope: Yup.string().required('Scope is required')
	});

	const { values, errors, touched, handleChange, handleSubmit, isValid, setFieldValue } =
		useFormik({
			initialValues,
			validationSchema,
			validateOnChange: true,
			onSubmit: (values, { resetForm }) => {
				handleQuesMapping();
			}
		});

	const handleCheckboxChange = (touchPoint, questionId, checked) => {
		setAccordionCheckboxes((prevState) => ({
			...prevState,
			[touchPoint]: {
				...prevState[touchPoint],
				[questionId]: checked
			}
		}));

		setTouchPointValidation((prevValidation) => ({
			...prevValidation,
			[touchPoint]: true
		}));
	};

	const mapAccordionCheckboxesToFormat = () => {
		const mappedData = [];
		for (const touchPoint in accordionCheckboxes) {
			const question_ids = [];
			for (const questionId in accordionCheckboxes[touchPoint]) {
				if (accordionCheckboxes[touchPoint][questionId]) {
					question_ids.push(Number(questionId));
				}
			}
			if (question_ids.length > 0) {
				mappedData.push({
					touchpoint_id: Number(touchPoint),
					new_question_ids: question_ids,
					delete_question_ids: []
				});
			}
		}
		return removeExistingKeysForNew(mappedData);
	};

	const removeExistingKeysForNew = (newData) => {
		// const existingQuestionIds = dataOfEdit.question_set.flatMap(data =>
		// 	data.questions.map(question => question.question_data.id)
		// );

		// const updatedQuestionSet = {
		// 	"question_set": newData.map(set => ({
		// 		...set,
		// 		new_question_ids: set.new_question_ids.filter(
		// 			id => !existingQuestionIds.includes(id)
		// 		)
		// 	}))
		// };

		const existingQuestionIdsByTouchpoint = dataOfEdit.question_set.reduce((acc, data) => {
			acc[data.touch_point.question_mapping_touch_point_id] = data.questions.map(
				question => question.question_data.id
			);
			return acc;
		}, {});

		const updatedQuestionSet = {
			"question_set": newData.map(set => {
				const existingIds = existingQuestionIdsByTouchpoint[set.touchpoint_id] || [];
				const idsToDelete = existingIds.filter(id => !set.new_question_ids.includes(id));
				return {
					...set,
					new_question_ids: set.new_question_ids.filter(
						id => !existingIds.includes(id)
					),
					delete_question_ids: [...set.delete_question_ids, ...idsToDelete]
				};
			})
		};
		return updatedQuestionSet.question_set;
	}

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleSelectWork = (item) => {
		setSelectedDataWork(item);
		setFieldValue('workOrderType', item.id);
	};

	const handleSelectScope = (item) => {
		setSelectedDataScope(item);
		setFieldValue('scope', item.id);
	};

	useEffect(() => {
		getWorkOrderType();
		getScope();
		getQuestionBank();

		const initialValidationState = {};
		touchPoint.forEach((item) => {
			initialValidationState[item.touch_point] = true;
		});
		setTouchPointValidation(initialValidationState);
	}, []);

	useEffect(() => {
		if (dataOfEdit?.question_set?.length > 0) {
			const uniqueTouchPoints = new Set();
			const uniqueData = new Map();

			dataOfEdit.question_set.forEach((item) => {
				if (!uniqueTouchPoints.has(item.touch_point)) {
					uniqueTouchPoints.add(item.touch_point);
					setTouchPoint((prev) => [...prev, item.touch_point]);
				}

				// Add question_data only if its ID is unique
				item.questions.forEach((question) => {
					const questionId = question.question_data.id;
					if (!uniqueData.has(questionId)) {
						uniqueData.set(questionId, question.question_data);
						setData((prevData) => [...prevData, question.question_data]);
					}
				});
			});
		}
	}, [dataOfEdit]);

	useEffect(() => {
		if (dataOfEdit?.question_set?.length > 0 && data.length < 20) {
			dataOfEdit.question_set.forEach((item) => {
				item.questions.forEach((question) => {
					setTimeout(() => {
						handleCheckboxChange(item.touch_point.question_mapping_touch_point_id, question.question_id, true);
					}, 1000);
				});
			});
		}
	}, [data]);

	const getWorkOrderType = async () => {
		const dataToSend = {
			status: API_STATUS.ACTIVE,
			page: 1,
			limit: 500
		};
		setLoading(true);
		getApi(
			WORK_ORDER_TYPE.GET,
			{
				...dataToSend
			},
			'order'
		)
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.work_order_types;
					if (apiData?.length) {
						setWorkOrderType(apiData);
						handleSelectWork(dataOfEdit.work_order_type_data);
					}
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const getScope = async () => {
		const dataToSend = {
			source: 'CMS',
			status: API_STATUS.ACTIVE
		};
		setLoading(true);
		getApi(
			SCOPES.GET_SCOPES,
			{
				...dataToSend
			},
			'order'
		)
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.scopes;
					if (apiData?.length) {
						setScope(apiData);
						handleSelectScope(dataOfEdit.scope_data);
					}
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const getQuestionBank = async () => {
		try {
			const dataToSend = {
				status: API_STATUS.ACTIVE,
				page: activePage,
				limit: TABLE.LIMIT
			};
			const response = await getApi(
				QUESTION.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				response?.data?.data?.questions.length === 0
					? setHasMoreQues(false)
					: setHasMoreQues(true);


				setData((prevData) => {
					const newQuestions = response?.data?.data?.questions.filter(
						(question) => !prevData.some((prevQuestion) => prevQuestion.id === question.id)
					);

					return [...prevData, ...newQuestions];
				});

				setActivePage(activePage + 1);
			}
		} catch (error) {
			console.log(error);
			setHasMoreQues(false);
		}
	};

	const handleQuesMapping = async () => {
		try {
			const mappedData = mapAccordionCheckboxesToFormat();

			const touchPointValidations = {}; // Store touchpoint validation results

			for (const touchPointItem of touchPoint) {
				const touchPointId = touchPointItem.question_mapping_touch_point_id;
				const selectedIds = Object.values(accordionCheckboxes[touchPointId] || {});
				const touchPointIsValid = selectedIds.includes(true);
				touchPointValidations[touchPointId] = touchPointIsValid;
			}

			const touchPointAllValid = Object.values(touchPointValidations).every(
				(isValid) => isValid
			);


			if (isValid && touchPointAllValid) {
				setLoading(true);
				const data = {
					question_set: mappedData
				};

				const response = await postApi(QUESTION_SET.EDIT, data, 'order');
				if (response?.status === CODES.SUCCESS) {
					toaster.current.success(response.data.message);
					setTimeout(() => {
						navigate(ROUTES.QUESTION_BANK.MAPPING);
					}, 1000);
				}
			} else {
				setTouchPointValidation(touchPointValidations);
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		setAccordionCheckboxes([]);
		setTouchPointValidation([]);
	}, [selectedDataWork, selectedDataScope]);

	return (
		<>
			<QuestionWrapper>
				<PageTitle title="sidebar.editQuestionMapping" />
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<Form>
								<div className="row g-3">
									<div className="col-sm-12 col-md-12 col-lg-6">
										<div className="card h-100">
											<div className="card-body d-flex align-items-center">
												<div className="card-content w-100">
													<p className="card-title text-light-gray mb-10">
														<strong> Work Order Type</strong>
													</p>
													<div>
														<SingleDropdown
															data={workOrderType}
															keyProps={['title']}
															onSelect={handleSelectWork}
															selectedData={selectedDataWork}
															disabled={true}
														/>
														{touched.workOrderType &&
															errors.workOrderType && (
																<span className="error-msg my-2">
																	{errors.workOrderType}
																</span>
															)}
													</div>
												</div>
											</div>
										</div>
									</div>
									<div className="col-sm-12 col-md-12 col-lg-6">
										<div className="card h-100">
											<div className="card-body d-flex align-items-center">
												<div className="card-content w-100">
													<p className="card-title text-light-gray mb-10">
														<strong>Scope</strong>
													</p>
													<div>
														<SingleDropdown
															data={scope}
															keyProps={['name', 'model']}
															onSelect={handleSelectScope}
															selectedData={selectedDataScope}
															disabled={true}
														/>
														{touched.scope && errors.scope && (
															<span className="error-msg my-2">
																{errors.scope}
															</span>
														)}
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								{selectedDataScope && selectedDataWork && (
									<p className="mb-5 mt-15">
										<strong>Touchpoints:</strong>
									</p>
								)}
								<Accordion open={openAccordion} toggle={toggleAccordion}>
									{selectedDataScope &&
										selectedDataWork &&
										(dataOfEdit.question_set.length > 0 ? (
											dataOfEdit.question_set.map((items, index) => (
												<AccordionItem key={items.touch_point.question_mapping_touch_point_id}>
													<AccordionHeader
														targetId={
															'quesMapping_' + items.touch_point.question_mapping_touch_point_id
														}>
														<span>
															{items.touch_point.title}{' '}
															<strong>(#{index + 1})</strong>
														</span>

														{touchPointValidation[items.touch_point.question_mapping_touch_point_id] ===
															false && (
																<span className="badge text-bg-danger fw-normal ml-10 font-12">
																	Please select at least one question.
																</span>
															)}
													</AccordionHeader>
													<AccordionBody
														accordionId={
															'quesMapping_' + items.touch_point.question_mapping_touch_point_id
														}>
														<div
															className="question-overflow"
															id="quesOverflow">
															<InfiniteScroll
																dataLength={data.length}
																next={getQuestionBank}
																hasMore={hasMoreQues}
																height={250}
																loader={
																	<ScrollLoader loaderText="Fetching more questions..." />
																}
																scrollableTarget="quesOverflow">
																{data.length > 0
																	? data?.map((item, index) => {
																		return (
																			<FormGroup
																				className="my-2 ml-10"
																				check
																				key={index}>
																				<Label check>
																					<Input
																						id={
																							item.id
																						}
																						name={
																							item.id
																						}
																						checked={
																							accordionCheckboxes[items.touch_point.question_mapping_touch_point_id] &&
																							accordionCheckboxes[items.touch_point.question_mapping_touch_point_id][item.id]
																						}
																						type="checkbox"
																						onChange={(
																							event
																						) =>
																							handleCheckboxChange(
																								items.touch_point.question_mapping_touch_point_id,
																								item.id,
																								event
																									.target
																									.checked
																							)
																						}
																					/>
																					{item.title}
																				</Label>
																			</FormGroup>
																		);
																	})
																	: null}
															</InfiniteScroll>
														</div>
													</AccordionBody>
												</AccordionItem>
											))
										) : (
											<div className="card h-100">
												<div className="p-4 card-body">
													<p className="text-center">
														No Touchpoints available.
													</p>
												</div>
											</div>
										))
									}
								</Accordion>
							</Form>
						</div>

						<div className="d-flex justify-content-end mt-10 gap-2">
							<Button
								onClick={handleSubmit}
								loading={loading}
								type="submit"
								className="btn form-button w-auto">
								Save
							</Button>
							<Button
								onClick={() => navigate(ROUTES.QUESTION_BANK.MAPPING)}
								type="button"
								className="btn form-button w-auto c-secondary">
								Cancel
							</Button>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
				<Loader loading={loading} />
			</QuestionWrapper>
		</>
	);
};

export default EditQuestionMapping;
