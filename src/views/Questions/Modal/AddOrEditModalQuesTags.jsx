/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';

const AddOrEditModalQuesTags = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const validationSchema = Yup.object().shape({
		name: Yup.string().required('Tag name is required!'),
		number: Yup.number().when('is_mandatory', {
			is: true,
			then: () =>
				Yup.number()
					.positive('Number of Images must be greater than 0!')
					.integer('Number of Images must be integer!')
					.required('Number of Images required!')
					.typeError('Number of Images must be a valid number!'),
			otherwise: () => Yup.number().notRequired()
		})
	});

	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			let submissionData = {
				name: formik?.values?.name,
				is_mandatory: formik?.values?.is_mandatory
			};
			if (formik?.values?.is_mandatory) {
				submissionData.number = formik?.values?.number;
			}
			if (props?.edit) {
				submissionData.id = props?.data?.id;
				props?.editTag(submissionData);
			} else {
				props?.addTag(submissionData);
			}
			setSaveLoading(false);
			handleCLoseModal();
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			name: '',
			is_mandatory: false,
			number: null
		},
		validationSchema,
		validateOnChange: true,
		onSubmit: onSubmitHandler
	});

	useEffect(() => {
		if (props?.data) {
			formik?.setValues({
				name: props?.data?.name,
				is_mandatory: props?.data?.is_mandatory == 1 ? true : false,
				number: props?.data?.number
			});
		}
	}, [props?.data]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Question Tag
				</ModalHeader>
				<Form onSubmit={formik?.handleSubmit}>
					<ModalBody>
						<FormGroup className="mb-10">
							<Label for="name">Tag Name</Label>
							<Input
								id="name"
								name="name"
								value={formik?.values?.name}
								onChange={formik?.handleChange}
								onBlur={formik?.handleBlur}
								type="text"
								placeholder="Your tag..."
							/>
							{formik?.touched?.name && formik?.errors?.name && (
								<span className="error-msg mt-0">{formik.errors.name}</span>
							)}
						</FormGroup>
						<FormGroup check>
							<Label for="is_mandatory" check>
								<Input
									id="is_mandatory"
									name="is_mandatory"
									checked={formik?.values?.is_mandatory}
									onChange={() =>
										formik.setFieldValue(
											'is_mandatory',
											!formik?.values?.is_mandatory
										)
									}
									type="checkbox"
								/>{' '}
								Is Image mandatory?
							</Label>
						</FormGroup>
						{formik?.values?.is_mandatory && (
							<FormGroup className="mt-10">
								<Label for="number">Number of Images</Label>
								<Input
									id="number"
									name="number"
									value={formik?.values?.number}
									onChange={formik?.handleChange}
									onBlur={formik?.handleBlur}
									type="number"
									placeholder="Number of Images..."
								/>
								{formik?.touched?.number && formik?.errors?.number && (
									<span className="error-msg mt-0">{formik.errors.number}</span>
								)}
							</FormGroup>
						)}
					</ModalBody>
					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								{props?.data ? 'Save' : 'Add'}
							</Button>
						</div>
						<div>
							{!props.data && (
								<Button
									onClick={handleCLoseModal}
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalQuesTags;
