import React, { useState } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ModalBody,
	Modal<PERSON>ooter,
	FormGroup,
	Label,
	Input,
	Card,
	Button
} from 'reactstrap';
import AlertModal from './AlertModal';

const CONDITION_TYPES = ['Always', 'Equals', 'Not equals', 'Contains', 'Greater than', 'Less than'];

const ConditionsModal = ({ isOpen, toggle, type, conditions, setConditions, title }) => {
	const [showAlertModal, setShowAlertModal] = useState(false);
	const [alertConfig, setAlertConfig] = useState({
		index: null
	});

	const addCondition = () => {
		setConditions((prev) => ({
			...prev,
			conditions: [
				...prev.conditions,
				{ type: 'Equals', value: '' } // Default to Equals for additional conditions
			]
		}));
	};

	const handleAlertConfirm = () => {
		setConditions((prev) => ({
			...prev,
			conditions: [{ type: 'Always', value: '' }] // Keep only the Always condition
		}));
		setShowAlertModal(false);
	};

	const updateCondition = (index, field, value) => {
		if (field === 'type' && value === 'Always') {
			setAlertConfig({ index });
			setShowAlertModal(true);
			return;
		}

		setConditions((prev) => {
			const newConditions = [...prev.conditions];
			newConditions[index] = { ...newConditions[index], [field]: value };
			return { ...prev, conditions: newConditions };
		});
	};

	const removeCondition = (index) => {
		setConditions((prev) => ({
			...prev,
			conditions: prev.conditions.filter((_, i) => i !== index)
		}));
	};

	return (
		<>
			<Modal isOpen={isOpen} toggle={toggle} className="modal-dialog-centered">
				<ModalHeader toggle={toggle}>Set Conditions for {title}</ModalHeader>
				<ModalBody>
					<FormGroup>
						<Label>Logical Operator between conditions</Label>
						<Input
							type="select"
							value={conditions.logicalOperator}
							onChange={(e) =>
								setConditions((prev) => ({
									...prev,
									logicalOperator: e.target.value
								}))
							}>
							<option value="AND">AND</option>
							<option value="OR">OR</option>
						</Input>
					</FormGroup>

					{conditions.conditions.map((condition, index) => (
						<Card key={index} className="mb-3 p-3">
							<div className="d-flex justify-content-between">
								<FormGroup className="flex-grow-1 mr-2">
									<Label>Type</Label>
									<Input
										type="select"
										value={condition.type}
										onChange={(e) =>
											updateCondition(index, 'type', e.target.value)
										}>
										{/* Only show Always option for first condition */}
										{<option value="Always">Always</option>}
										<option value="Equals">Equals</option>
										<option value="Not equals">Not equals</option>
										{type !== 'comment' && (
											<>
												<option value="Contains">Contains</option>
												<option value="Greater than">Greater than</option>
												<option value="Less than">Less than</option>
											</>
										)}
									</Input>
								</FormGroup>
								{condition.type !== 'Always' && (
									<FormGroup className="flex-grow-1 ml-2">
										<Label>Value</Label>
										<Input
											type={
												['Greater than', 'Less than'].includes(
													condition.type
												)
													? 'number'
													: 'text'
											}
											value={condition.value}
											onChange={(e) =>
												updateCondition(index, 'value', e.target.value)
											}
											placeholder="Enter value"
										/>
									</FormGroup>
								)}
								{/* Show remove button if there's more than one condition OR if it's not an Always condition */}
								{(conditions.conditions.length > 1 ||
									condition.type !== 'Always') && (
									<Button
										className="btn-danger ml-2 align-self-end"
										onClick={() => removeCondition(index)}>
										Remove
									</Button>
								)}
							</div>
						</Card>
					))}

					{/* Only show Add Condition button if first condition is not Always */}
					{conditions.conditions[0]?.type !== 'Always' && (
						<Button className="btn btn-primary mt-2" onClick={addCondition}>
							Add Condition
						</Button>
					)}
				</ModalBody>
				<ModalFooter>
					<Button onClick={toggle}>Done</Button>
				</ModalFooter>
			</Modal>

			<AlertModal
				isOpen={showAlertModal}
				toggle={() => setShowAlertModal(false)}
				onConfirm={handleAlertConfirm}
				title="Remove Other Conditions"
				message="Setting this condition to 'Always' will remove all other conditions. Do you want to continue?"
			/>
		</>
	);
};

export default ConditionsModal;
