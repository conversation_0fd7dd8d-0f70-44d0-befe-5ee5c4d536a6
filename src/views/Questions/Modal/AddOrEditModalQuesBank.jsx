/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Dropdown,
	Card,
	CardBody,
	Badge,
	Modal
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import RejectIcon from '../../../assets/images/reject.svg';
import { getApi } from 'src/helper/api/Api';
import { QUESTION } from 'src/helper/api/endPoint';
import ConditionsModal from './ConditionsModal';
import AlertModal from './AlertModal';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';

const AddOrEditModalQuestionBank = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [selectedQuesType, setSelectedQuesType] = useState('');
	const [mcqFields, setMCQFields] = useState([1, 2]);
	// New state variables for dependent questions
	const [searchQuery, setSearchQuery] = useState('');
	const [searchResults, setSearchResults] = useState([]);
	const [selectedDependencies, setSelectedDependencies] = useState([]);
	const [activeDependent, setActiveDependent] = useState(null);
	const [dependencyConditions, setDependencyConditions] = useState({});
	const [showDependentSearch, setShowDependentSearch] = useState(false);
	const [showAttachmentConditions, setShowAttachmentConditions] = useState(false);
	const [showVideoConditions, setShowVideoConditions] = useState(false);
	const [showCommentConditions, setShowCommentConditions] = useState(false);
	const [showAlertModal, setShowAlertModal] = useState(false);
	const [alertConfig, setAlertConfig] = useState({
		questionId: null,
		conditionIndex: null
	});
	const [attachmentConditions, setAttachmentConditions] = useState({
		logicalOperator: 'AND',
		conditions: [
			{
				type: 'Always',
				value: ''
			}
		]
	});
	const [videoConditions, setVideoConditions] = useState({
		logicalOperator: 'AND',
		conditions: [
			{
				type: 'Always',
				value: ''
			}
		]
	});
	const [commentConditions, setCommentConditions] = useState({
		logicalOperator: 'AND',
		conditions: [
			{
				type: 'Always',
				value: ''
			}
		]
	});
	const [tags, setTags] = useState([]);
	const [selectedTags, setSelectedTags] = useState(null);

	const CONDITION_TYPES = [
		'Always',
		'Equals',
		'Not equals',
		'Contains',
		'Greater than',
		'Less than'
	];

	const maxCharacterCountQues = 200;
	const countMaxMCQValue = 4;

	const dynamicMCQInitialValues = mcqFields.reduce((acc, field) => {
		acc[`mcq_${field}`] = '';
		return acc;
	}, {});

	const mcqObject = Object.fromEntries(
		Array.from({ length: countMaxMCQValue }, (_, i) => [`mcq_${i + 1}`, ''])
	);

	const initialValues = {
		ques_name: '',
		...mcqObject,
		// is_numeric: false,
		attach_img: true,
		is_video_required: false,
		is_comment_required: false
	};

	const initialCondition = {
		operator: 'EQUALS', // Changed from 'ALWAYS' to 'EQUALS'
		option_ids: [],
		condition_type: 'AND',
		condition_value: ''
	};

	const commonValidationSchema = Yup.object().shape({
		ques_name: Yup.string()
			.required('Question name is required')
			.max(maxCharacterCountQues, 'Question name is too long'),
		attach_img: Yup.boolean(),
		is_video_required: Yup.boolean(),
		// is_numeric: Yup.boolean()
		is_comment_required: Yup.boolean()
	});

	const validationSchema = Yup.lazy((values) => {
		if (selectedQuesType === 'Single' || selectedQuesType === 'Multiple') {
			const fieldValidations = {
				...commonValidationSchema.fields
			};

			Object.keys(dynamicMCQInitialValues).forEach((field) => {
				fieldValidations[field] = Yup.string().required(
					`MCQ ${field.split('_')[1]} is required`
				);
			});
			const dynamicMCQObj = Yup.object().shape(fieldValidations);

			return Yup.object().shape({
				...dynamicMCQObj.fields,
				...commonValidationSchema.fields
			});
		}
		return commonValidationSchema;
	});

	const handleSearch = async (query) => {
		setSearchQuery(query);
		const dataToSend = {
			page: 1,
			limit: 30,
			search: query
		};
		// Assuming props.questions contains all available questions
		const response = await getApi(
			QUESTION.GET,
			{
				...dataToSend
			},
			'order'
		);
		const searchRes = response?.data?.data?.questions || [];
		setSearchResults(searchRes);
	};

	const formik = useFormik({
		initialValues,
		validationSchema,
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleQuesBank();
		}
	});

	const addMCQField = () => {
		const nextField = mcqFields.length + 1;
		setMCQFields([...mcqFields, nextField]);
	};

	const removeMCQField = (field) => {
		setMCQFields(mcqFields.slice(0, -1));
		formik.setFieldValue(`mcq_${field}`, '');
	};

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const characterCount = formik.values.ques_name.length;

	const handleChangeTagsDropdown = (item) => {
		setSelectedTags(item);
	};

	useEffect(() => {
		console.log('Question data:-', props.data);
		if (props.data) {
			const formattedData = {
				ques_name: props.data.title,
				attach_img: props.data.attachment_required,
				is_video_required: props.data.is_video_required,
				is_comment_required: props.data.comment_required?.required ? true : false
			};

			if (props.data.question_type.title !== 'Descriptive') {
				// Set mcqFields based on the number of options
				const optionsCount = props.data.question_options.length;
				setMCQFields(Array.from({ length: optionsCount }, (_, i) => i + 1));

				// Set the values for each MCQ field
				props.data.question_options.forEach((option, index) => {
					formattedData[`mcq_${index + 1}`] = option?.title || '';
				});
			}

			formik.setValues(formattedData);
			setSelectedQuesType(props.data.question_type.title);

			if (props.data.report_tag) {
				setSelectedTags(props.data.report_tag);
			}

			if (props.data.is_attachment_required?.required) {
				setAttachmentConditions({
					logicalOperator: props.data.is_attachment_required.logicalOperator,
					conditions: props.data.is_attachment_required.conditions
				});
			}

			// Load video conditions if they exist
			if (props.data.video_required?.required) {
				setVideoConditions({
					logicalOperator: props.data.video_required.logicalOperator,
					conditions: props.data.video_required.conditions
				});
			}

			if (props.data.comment_required?.required) {
				setCommentConditions({
					logicalOperator: props.data.comment_required?.logicalOperator,
					conditions: props.data.comment_required?.conditions
				});
			}

			const questions_data = {};
			const selected_dependencies = [];
			props.data?.dependencies?.childQuestions?.forEach((question_data) => {
				questions_data[question_data.question.id] = {
					id: question_data.id,
					logicalOperator: question_data.logicalOperator,
					childQuestionId: question_data.question.id,
					triggerConditions: question_data.triggerConditions
				};
				selected_dependencies.push(question_data?.question);
			});
			setDependencyConditions(questions_data);
			setSelectedDependencies(selected_dependencies);
		}
	}, [props.data]);
	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const handleQuesBank = () => {
		try {
			formik.validateForm(formik.values).then((errors) => {
				if (Object.keys(errors).length === 0) {
					setSaveLoading(true);
					const findQuesType = props.questionType.find(
						(items) => items.title === selectedQuesType
					);

					let submissionData = {
						title: formik.values.ques_name,
						attachment_required: formik.values.attach_img ? 1 : 0,
						is_video_required: formik.values.is_video_required ? 1 : 0,
						// Structure attachment conditions according to schema
						is_attachment_required: {
							required: formik.values.attach_img,
							...(formik.values.attach_img
								? {
										logicalOperator: attachmentConditions.logicalOperator,
										conditions: attachmentConditions.conditions
								  }
								: {})
						},
						// Structure video conditions according to schema
						video_required: {
							required: formik.values.is_video_required,
							...(formik.values.is_video_required
								? {
										logicalOperator: videoConditions.logicalOperator,
										conditions: videoConditions.conditions
								  }
								: {})
						},
						comment_required: {
							required: formik.values.is_comment_required,
							...(formik.values.is_comment_required
								? {
										logicalOperator: commentConditions.logicalOperator,
										conditions: commentConditions.conditions
								  }
								: {})
						}
					};
					console.log(submissionData);

					if (selectedTags) {
						submissionData.report_tag_id = selectedTags.id;
					}

					// Add question type specific data
					// if (findQuesType.id === 3) {
					// 	submissionData.is_numeric = formik.values.is_numeric ? 1 : 0;
					// }

					if (findQuesType.id !== 3) {
						const mcqValuesArray = Object.keys(formik.values)
							.filter((key) => key.startsWith('mcq_') && formik.values[key])
							.map((key) => formik.values[key]);
						submissionData.options = mcqValuesArray;
					}

					// Add dependencies if they exist
					if (Object.keys(dependencyConditions).length > 0) {
						submissionData.dependencies = Object.values(dependencyConditions);
					}

					if (props.edit) {
						submissionData.id = props.data.id;
						props.editQuestions(submissionData);
					} else {
						submissionData.que_type_id = findQuesType.id;
						props.addQuestions(submissionData);
					}

					handleCLoseModal();
				}
			});
		} catch (error) {
			console.error('Error in handleQuesBank:', error);
			setSaveLoading(false);
		}
	};

	const handleChangeQuestionsType = (value) => {
		setSelectedQuesType(value.title);
	};

	// Function to add dependent question
	const handleAddDependentQuestion = (question) => {
		setSelectedDependencies([...selectedDependencies, question]);
		setDependencyConditions({
			...dependencyConditions,
			[question.id]: {
				childQuestionId: question.id,
				triggerConditions: [
					{
						...initialCondition,
						operator: 'ALWAYS' // Only the first condition is ALWAYS
					}
				],
				logicalOperator: 'AND'
			}
		});
		setShowDependentSearch(false);
		setSearchQuery('');
		setSearchResults([]);
	};

	// Function to remove dependent question
	const handleRemoveDependentQuestion = (questionId) => {
		setSelectedDependencies(selectedDependencies.filter((dep) => dep.id !== questionId));
		const newConditions = { ...dependencyConditions };
		delete newConditions[questionId];
		setDependencyConditions(newConditions);
	};

	// Function to handle condition removal
	const handleRemoveCondition = (questionId, index) => {
		const newConditions = [...dependencyConditions[questionId].triggerConditions];
		newConditions.splice(index, 1);
		handleUpdateConditions(questionId, {
			...dependencyConditions[questionId],
			triggerConditions: newConditions
		});
	};

	// Function to update conditions
	const handleUpdateConditions = (questionId, updatedConditions) => {
		setDependencyConditions({
			...dependencyConditions,
			[questionId]: {
				...dependencyConditions[questionId],
				...updatedConditions
			}
		});
	};
	// console.log('New conditions', dependencyConditions);
	// Handle adding option to condition
	const handleAddOptionToCondition = (questionId, conditionIndex, optionId) => {
		const conditions = { ...dependencyConditions[questionId] };
		const optionIds = conditions.triggerConditions[conditionIndex].option_ids || [];

		if (!optionIds.includes(optionId)) {
			conditions.triggerConditions[conditionIndex].option_ids = [...optionIds, optionId];
			handleUpdateConditions(questionId, conditions);
		}
	};

	// Handle removing option from condition
	const handleRemoveOptionFromCondition = (questionId, conditionIndex, optionId) => {
		const conditions = { ...dependencyConditions[questionId] };
		conditions.triggerConditions[conditionIndex].option_ids = conditions.triggerConditions[
			conditionIndex
		].option_ids.filter((id) => id !== optionId);
		handleUpdateConditions(questionId, conditions);
	};

	// Add function to handle "Always" condition logic
	const handleOperatorChange = (questionId, index, newOperator) => {
		if (newOperator === 'ALWAYS') {
			setAlertConfig({
				questionId,
				conditionIndex: index
			});
			setShowAlertModal(true);
		} else {
			const newConditions = [...dependencyConditions[questionId].triggerConditions];
			newConditions[index] = {
				...newConditions[index],
				operator: newOperator,
				option_ids: [],
				condition_value: ''
			};
			handleUpdateConditions(questionId, {
				...dependencyConditions[questionId],
				triggerConditions: newConditions
			});
		}
	};

	// Add function to handle alert confirmation
	const handleAlertConfirm = () => {
		const { questionId, conditionIndex } = alertConfig;
		const newConditions = [
			{
				...initialCondition,
				operator: 'ALWAYS'
			}
		];
		handleUpdateConditions(questionId, {
			...dependencyConditions[questionId],
			triggerConditions: newConditions
		});
		setShowAlertModal(false);
	};

	const renderConditionModals = () => (
		<>
			{showAttachmentConditions && (
				<ConditionsModal
					isOpen={showAttachmentConditions}
					toggle={() => setShowAttachmentConditions(false)}
					type="attachment"
					conditions={attachmentConditions}
					setConditions={setAttachmentConditions}
					title="Attachment"
				/>
			)}
			{showVideoConditions && (
				<ConditionsModal
					isOpen={showVideoConditions}
					toggle={() => setShowVideoConditions(false)}
					type="video"
					conditions={videoConditions}
					setConditions={setVideoConditions}
					title="Video"
				/>
			)}
			{(selectedQuesType === 'Single' || selectedQuesType === 'Multiple') &&
				showCommentConditions && (
					<ConditionsModal
						isOpen={showCommentConditions}
						toggle={() => setShowCommentConditions(false)}
						type="comment"
						conditions={commentConditions}
						setConditions={setCommentConditions}
						title="Comment"
					/>
				)}
		</>
	);
	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Question
				</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							{!props.data && (
								<FormGroup>
									<Label>Select Question types</Label>
									<Dropdown
										className={`mt-0 mb-10 ${
											!props.data ? 'bg-blue' : 'cursor-disabled'
										}`}
										isOpen={dropdownOpen}
										disabled={props.data}
										toggle={toggleRoleDropDown}>
										<DropdownToggle caret className="roleBtn text-truncate">
											{selectedQuesType || 'Select'}
										</DropdownToggle>

										<DropdownMenu className="w-100" style={{ maxWidth: '50%' }}>
											{props.questionType.length ? (
												props.questionType.map((item, index) => {
													return (
														<DropdownItem
															key={index}
															onClick={() => {
																handleChangeQuestionsType(item);
															}}
															className="text-truncate">
															{item.title}
														</DropdownItem>
													);
												})
											) : (
												<DropdownItem disabled> No Data </DropdownItem>
											)}
										</DropdownMenu>
									</Dropdown>
								</FormGroup>
							)}
							<Form>
								{selectedQuesType && (
									<FormGroup>
										<Label>Question Name</Label>
										<>
											<Input
												id="ques_name"
												name="ques_name"
												value={formik.values.ques_name}
												onChange={formik.handleChange}
												type="textarea"
												placeholder="Your Question..."
											/>
										</>
										<div
											className={`font-14 mt-5 ${
												characterCount > maxCharacterCountQues
													? 'text-danger'
													: ''
											}`}>
											Total characters you can add {characterCount}/
											{maxCharacterCountQues}
										</div>
										{formik.touched.ques_name && formik.errors.ques_name && (
											<span className="error-msg my-2">
												{formik.errors.ques_name}
											</span>
										)}
									</FormGroup>
								)}
								{selectedQuesType.length > 0 &&
								selectedQuesType !== 'Descriptive' ? (
									<>
										<div className="d-flex justify-content-between align-items-center mb-10">
											<p>
												<strong>MCQs</strong>
											</p>
											<Button
												className="btn form-button w-auto"
												disabled={mcqFields.length > 3}
												type="button"
												onClick={addMCQField}>
												Add MCQ
											</Button>
										</div>
										{mcqFields.map((field, index) => (
											<div key={field}>
												<FormGroup>
													<div className="d-flex align-items-center w-100">
														<Label className="mr-30 ml-10 mt-7">
															{String.fromCharCode(64 + field)}
														</Label>
														<div className="d-flex flex-row align-items-center w-100">
															<Input
																id={`mcq_${field}`}
																name={`mcq_${field}`}
																value={
																	formik.values[`mcq_${field}`]
																}
																onChange={formik.handleChange}
																type="text"
																placeholder={`MCQ Options ${field}`}
																className="mt-10 mb-10"
															/>
															{index >= 2 && (
																<img
																	src={RejectIcon}
																	onClick={() =>
																		removeMCQField(field)
																	}
																	alt="cross-icon"
																	title="Delete MCQ type"
																	className="ml-10 cursor-pointer"
																	width="20px"
																/>
															)}
														</div>
													</div>
													{formik.touched[`mcq_${field}`] &&
														formik.errors[`mcq_${field}`] && (
															<span className="error-msg my-0 ml-55">
																{formik.errors[`mcq_${field}`]}
															</span>
														)}
												</FormGroup>
											</div>
										))}
									</>
								) : null}
								{selectedQuesType && (
									<FormGroup>
										<Label>Question Tag</Label>
										<SingleDropdown
											data={props.tags}
											keyProps={['name']}
											onSelect={handleChangeTagsDropdown}
											selectedData={selectedTags}
											className={'w-100 text-left'}
										/>
									</FormGroup>
								)}
								{selectedQuesType && (
									<>
										{/* {selectedQuesType === 'Descriptive' ? (
											<FormGroup className="mt-10" check>
												<Label for="is_numeric" check>
													<Input
														id="is_numeric"
														name="is_numeric"
														checked={formik.values.is_numeric}
														onChange={() =>
															formik.setFieldValue(
																'is_numeric',
																!formik.values.is_numeric
															)
														}
														type="checkbox"
													/>{' '}
													Is Numeric
												</Label>
											</FormGroup>
										) : null} */}
										<FormGroup
											className="mt-10 d-flex align-items-center justify-content-between"
											check>
											<Label for="attach_img" check>
												<Input
													id="attach_img"
													name="attach_img"
													checked={formik.values.attach_img}
													onChange={() =>
														formik.setFieldValue(
															'attach_img',
															!formik.values.attach_img
														)
													}
													type="checkbox"
												/>{' '}
												Attach Image
											</Label>
											{formik.values.attach_img && (
												<Button
													className="btn btn-sm btn-outline-primary"
													onClick={() =>
														setShowAttachmentConditions(true)
													}>
													Set Conditions
												</Button>
											)}
										</FormGroup>

										<FormGroup
											className="mt-10 d-flex align-items-center justify-content-between"
											check>
											<Label for="is_video_required" check>
												<Input
													id="is_video_required"
													name="is_video_required"
													checked={formik.values.is_video_required}
													onChange={() =>
														formik.setFieldValue(
															'is_video_required',
															!formik.values.is_video_required
														)
													}
													type="checkbox"
												/>{' '}
												Attach Video
											</Label>
											{formik.values.is_video_required && (
												<Button
													className="btn btn-sm btn-outline-primary"
													onClick={() => setShowVideoConditions(true)}>
													Set Conditions
												</Button>
											)}
										</FormGroup>
									</>
								)}
								{(selectedQuesType === 'Single' ||
									selectedQuesType === 'Multiple') && (
									<FormGroup
										className="mt-10 d-flex align-items-center justify-content-between"
										check>
										<Label for="is_comment_required" check>
											<Input
												id="is_comment_required"
												name="is_comment_required"
												checked={formik.values.is_comment_required}
												onChange={() =>
													formik.setFieldValue(
														'is_comment_required',
														!formik.values.is_comment_required
													)
												}
												type="checkbox"
											/>{' '}
											Is comment required
										</Label>
										{formik.values.is_comment_required && (
											<Button
												className="btn btn-sm btn-outline-primary"
												onClick={() => setShowCommentConditions(true)}>
												Set Conditions
											</Button>
										)}
									</FormGroup>
								)}
							</Form>
							{/* Dependent Questions Section */}
							{selectedQuesType && (
								<div className="mt-4">
									<div className="d-flex justify-content-between align-items-center mb-3">
										<h5>Dependent Questions</h5>
										<Button
											className="btn form-button w-auto"
											type="button"
											onClick={() => setShowDependentSearch(true)}>
											Add Dependent Question
										</Button>
									</div>

									{/* Search Modal */}
									<Modal
										isOpen={showDependentSearch}
										toggle={() => setShowDependentSearch(false)}
										className="modal-dialog-centered">
										<ModalHeader toggle={() => setShowDependentSearch(false)}>
											Search Questions
										</ModalHeader>
										<ModalBody>
											<Input
												type="text"
												placeholder="Search questions..."
												value={searchQuery}
												onChange={(e) => handleSearch(e.target.value)}
												className="mb-3"
											/>
											<div
												className="search-results"
												style={{ maxHeight: '400px', overflow: 'auto' }}>
												{searchResults.map((question) => (
													<Card
														key={question.id}
														className="mb-2 cursor-pointer hover:bg-gray-100"
														onClick={() =>
															handleAddDependentQuestion(question)
														}>
														<CardBody>
															<div className="d-flex flex-wrap gap-2">
																<div className="flex-grow-1">
																	{question.title}
																</div>
																<Badge color="primary">
																	{question.question_type.title}
																</Badge>
																{question.question_options?.map(
																	(option) => (
																		<Badge
																			key={option.id}
																			color="secondary">
																			{option.title}
																		</Badge>
																	)
																)}
															</div>
														</CardBody>
													</Card>
												))}
												{searchResults.length === 0 && searchQuery && (
													<div className="text-center py-3">
														No questions found
													</div>
												)}
											</div>
										</ModalBody>
									</Modal>

									{/* Selected Dependencies */}
									{selectedDependencies.map((question) => (
										<Card key={question.id} className="mb-3">
											<CardBody>
												<div className="d-flex justify-content-between align-items-start">
													<div>
														<h6>{question.title}</h6>
														<div className="d-flex flex-wrap gap-2 mt-2">
															<Badge color="primary">
																{question.question_type.title}
															</Badge>
															{question.question_options?.map(
																(option) => (
																	<Badge
																		key={option.id}
																		color="secondary">
																		{option.title}
																	</Badge>
																)
															)}
														</div>
													</div>
													<div className="d-flex gap-2">
														<Button
															className="btn btn-sm btn-outline-primary"
															onClick={() =>
																setActiveDependent(question)
															}>
															Set Conditions
														</Button>
														<Button
															className="btn btn-sm btn-outline-danger"
															onClick={() =>
																handleRemoveDependentQuestion(
																	question.id
																)
															}>
															Remove
														</Button>
													</div>
												</div>

												{/* Display current conditions */}
												{dependencyConditions[
													question.id
												]?.triggerConditions?.map((condition, index) => (
													<div
														key={index}
														className="mt-2 p-2 bg-light rounded">
														<div className="d-flex justify-content-between align-items-center">
															<small>
																<strong>Operator:</strong>{' '}
																{condition.operator}
																{condition.option_ids?.length >
																	0 && (
																	<>
																		<br />
																		<strong>
																			Selected Options:
																		</strong>{' '}
																		{question.question_options
																			.filter((opt) =>
																				condition.option_ids.includes(
																					opt.id
																				)
																			)
																			.map((opt) => opt.title)
																			.join(', ')}
																	</>
																)}
																{condition.condition_value && (
																	<>
																		<br />
																		<strong>Value:</strong>{' '}
																		{condition.condition_value}
																	</>
																)}
															</small>
														</div>
													</div>
												))}
											</CardBody>
										</Card>
									))}
								</div>
							)}
							{/* Condition Setting Modal */}
							<Modal
								isOpen={activeDependent !== null}
								toggle={() => setActiveDependent(null)}
								className="modal-dialog-centered">
								<ModalHeader toggle={() => setActiveDependent(null)}>
									Set Conditions for {activeDependent?.title}
								</ModalHeader>
								<ModalBody>
									{activeDependent && (
										<>
											<FormGroup>
												<Label>Logical Operator between conditions</Label>
												<Input
													type="select"
													value={
														dependencyConditions[activeDependent.id]
															?.logicalOperator
													}
													onChange={(e) =>
														handleUpdateConditions(activeDependent.id, {
															...dependencyConditions[
																activeDependent.id
															],
															logicalOperator: e.target.value
														})
													}>
													<option value="AND">AND</option>
													<option value="OR">OR</option>
												</Input>
											</FormGroup>

											{dependencyConditions[
												activeDependent.id
											]?.triggerConditions.map((condition, index) => (
												<Card key={index} className="mb-3 p-3">
													<div className="d-flex justify-content-between align-items-start">
														<FormGroup className="w-100">
															<Label>Operator</Label>
															<Input
																type="select"
																value={condition.operator}
																onChange={(e) =>
																	handleOperatorChange(
																		activeDependent.id,
																		index,
																		e.target.value
																	)
																}>
																<option value="ALWAYS">
																	Always
																</option>
																<option value="EQUALS">
																	Equals
																</option>
																<option value="GREATER_THAN">
																	Greater Than
																</option>
																<option value="LESS_THAN">
																	Less Than
																</option>
																<option value="NOT_EQUALS">
																	Not Equals
																</option>
																<option value="CONTAINS">
																	Contains
																</option>
															</Input>
														</FormGroup>

														{/* Add the Remove Condition button here */}
														{condition.operator !== 'ALWAYS' && (
															<Button
																className="btn btn-sm btn-outline-danger ms-2"
																onClick={() =>
																	handleRemoveCondition(
																		activeDependent.id,
																		index
																	)
																}>
																Remove
															</Button>
														)}
													</div>

													{condition.operator === 'OPTION_SELECTED' && (
														<FormGroup>
															<Label>Select Options</Label>
															{/* Rest of the options selection code */}
														</FormGroup>
													)}

													{condition.operator !== 'ALWAYS' && (
														<FormGroup>
															<Label>Value</Label>
															<Input
																type={
																	[
																		'GREATER_THAN',
																		'LESS_THAN'
																	].includes(condition.operator)
																		? 'number'
																		: 'text'
																}
																value={condition.condition_value}
																onChange={(e) => {
																	const newConditions = [
																		...dependencyConditions[
																			activeDependent.id
																		].triggerConditions
																	];
																	newConditions[index] = {
																		...condition,
																		condition_value:
																			e.target.value
																	};
																	handleUpdateConditions(
																		activeDependent.id,
																		{
																			...dependencyConditions[
																				activeDependent.id
																			],
																			triggerConditions:
																				newConditions
																		}
																	);
																}}
																placeholder="Enter value"
															/>
														</FormGroup>
													)}
												</Card>
											))}

											{!(
												dependencyConditions[activeDependent.id]
													?.triggerConditions.length === 1 &&
												dependencyConditions[activeDependent.id]
													?.triggerConditions[0].operator === 'ALWAYS'
											) && (
												<Button
													className="btn btn-sm btn-primary mt-2"
													onClick={() => {
														const newConditions = [
															...dependencyConditions[
																activeDependent.id
															].triggerConditions,
															{ ...initialCondition } // New conditions start with EQUALS
														];
														handleUpdateConditions(activeDependent.id, {
															...dependencyConditions[
																activeDependent.id
															],
															triggerConditions: newConditions
														});
													}}>
													Add Condition
												</Button>
											)}
										</>
									)}
								</ModalBody>
								<ModalFooter>
									<Button onClick={() => setActiveDependent(null)}>Done</Button>
								</ModalFooter>
							</Modal>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="submit"
							className="btn form-button"
							onClick={formik.handleSubmit}
							loading={saveLoading}
							disabled={formik.isSubmitting || selectedQuesType.length === 0}>
							Save
						</Button>
					</div>
					<div>
						{!props.data && (
							<Button
								onClick={handleCLoseModal}
								loading={formik.isSubmitting}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						)}
					</div>
				</ModalFooter>
				{selectedQuesType && <>{renderConditionModals()}</>}
				<AlertModal
					isOpen={showAlertModal}
					toggle={() => setShowAlertModal(false)}
					onConfirm={handleAlertConfirm}
					title="Remove Other Conditions"
					message="Setting this condition to 'Always' will remove all other conditions. Do you want to continue?"
				/>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalQuestionBank;
