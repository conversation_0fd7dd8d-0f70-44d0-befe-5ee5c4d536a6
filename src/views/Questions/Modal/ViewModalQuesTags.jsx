import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';

const ViewModalQuesTags = (props) => {
	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Question Tag</ModalHeader>

				<ModalBody>
					<div>
						<div className="mb-10 mt-10 text-capitalize">
							<strong>Tag Name:</strong> {props?.data?.name ? props?.data?.name : '-'}
						</div>
						<div className="mb-10 mt-10 text-capitalize">
							<strong>Number of Images:</strong> {props?.data?.number}
						</div>
					</div>
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalQuesTags;
