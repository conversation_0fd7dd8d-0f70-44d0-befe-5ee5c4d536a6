import React from 'react';
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Accordion,
	AccordionItem,
	AccordionHeader,
	AccordionBody
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';
import ImageViewer from 'src/components/ImageTouchPoint/ImageViewer';

const ViewModalQuesMapping = (props) => {
	const [touchpoints, setTouchpoints] = useState({});
	const [openAccordion, setOpenAccordion] = useState('viewQuesMapping_1');

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleImageClick = () => {};

	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	const titleFields = Object.entries(touchpoints).map(([key, point]) => (
		<React.Fragment key={key}>
			<tr>
				<td>{point.title + ' (#' + point.touch_point + ')'}</td>
				<td>{point.x_coordinate}</td>
				<td>{point.y_coordinate}</td>
			</tr>
		</React.Fragment>
	));

	useEffect(() => {
		const data = props?.data?.scope_data?.scope_image?.touch_points;
		if (data) {
			const renamedData = data.map((item) => ({
				...item,
				left: item.x_coordinate,
				top: item.y_coordinate
			}));
			setTouchpoints(renamedData);
		}
	}, [props?.data?.scope_data?.scope_image?.touch_points]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Question Sets</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							<Accordion open={openAccordion} toggle={toggleAccordion}>
								<AccordionItem>
									<AccordionHeader targetId="viewQuesMapping_1">
										Type Details
									</AccordionHeader>
									<AccordionBody accordionId="viewQuesMapping_1">
										<div>
											{props.data.work_order_type_data.title && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Work Order Type:</strong>{' '}
													{props.data.work_order_type_data.title}
												</div>
											)}
											{props.data.scope_data.model && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Scope Model:</strong>{' '}
													{props.data.scope_data.model}
												</div>
											)}
											{props.data.scope_data.name && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Scope Name:</strong>{' '}
													{props.data.scope_data.name}
												</div>
											)}
										</div>
									</AccordionBody>
								</AccordionItem>
								<AccordionItem>
									<AccordionHeader targetId="viewQuesMapping_2">
										Image Details
									</AccordionHeader>
									<AccordionBody accordionId="viewQuesMapping_2">
										<div>
											<div className="d-flex justify-content-center mb-10">
												<ImageViewer
													imageSrc={
														props?.data?.scope_data?.scope_image
															?.image_url
													}
													onImageClick={handleImageClick}
													touchpoints={touchpoints}
													cursor_indication={true}
												/>
											</div>
											<table className="table table-bordered mb-0">
												<thead>
													<tr>
														<th className="width-number">Title.</th>
														<th className="width-common">
															Left Coordinate
														</th>
														<th className="width-common">
															Top Coordinate
														</th>
													</tr>
												</thead>
												<tbody>{titleFields}</tbody>
											</table>
										</div>
									</AccordionBody>
								</AccordionItem>
								<AccordionItem>
									<AccordionHeader targetId="viewQuesMapping_3">
										Question Details
									</AccordionHeader>
									<AccordionBody accordionId="viewQuesMapping_3">
										{props?.data?.question_set.map((items, index, row) => (
											<React.Fragment key={index}>
												<p
													className={`text-capitalize ${
														index + 1 !== row.length && 'mb-5'
													}`}>
													<strong>
														{items.touch_point.title} (#
														{items.touch_point.touch_point})
													</strong>
												</p>
												<ol className="m-0 ques-list">
													{items.questions.map((ques, index) => (
														<React.Fragment key={index}>
															<li>
																<div>
																	{ques.question_data.title}
																	{ques.question_data
																		.question_type.title !==
																		'Descriptive' && (
																		<span className="badge text-bg-info text-white fw-normal ml-5">
																			{
																				ques.question_data
																					.question_type
																					.title
																			}
																		</span>
																	)}
																	{ques.question_data
																		.question_options &&
																	ques.question_data
																		.question_options.length >
																		0 ? (
																		<div className="d-flex justify-content-start flex-wrap align-items-center gap-2 mt-10 mb-10">
																			{ques.question_data.question_options.map(
																				(
																					options,
																					index
																				) => (
																					<React.Fragment
																						key={index}>
																						<div className="card w-49">
																							<div className="card-body">
																								<p>
																									<strong>
																										{String.fromCharCode(
																											65 +
																												index
																										) +
																											'. '}
																									</strong>
																									{
																										options.title
																									}
																								</p>
																							</div>
																						</div>
																					</React.Fragment>
																				)
																			)}
																		</div>
																	) : null}
																</div>
															</li>
														</React.Fragment>
													))}
												</ol>
												{index + 1 !== row.length && <hr />}
											</React.Fragment>
										))}
									</AccordionBody>
								</AccordionItem>
							</Accordion>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalQuesMapping;
