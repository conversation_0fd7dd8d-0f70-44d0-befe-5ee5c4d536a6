import { useEffect, useState } from 'react';
import {
	Input,
	Modal<PERSON>ody,
	Mo<PERSON><PERSON>ooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Dropdown,
	Card,
	CardBody,
	Badge,
	Modal
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import RejectIcon from '../../../assets/images/reject.svg';

const AddOrEditModalQuestionBank = (props) => {
	// Original state variables
	const [saveLoading, setSaveLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [selectedQuesType, setSelectedQuesType] = useState('');
	const [mcqFields, setMCQFields] = useState([1, 2]);

	// New state variables for dependent questions
	const [searchQuery, setSearchQuery] = useState('');
	const [searchResults, setSearchResults] = useState([]);
	const [selectedDependencies, setSelectedDependencies] = useState([]);
	const [showConditionModal, setShowConditionModal] = useState(false);
	const [activeDependent, setActiveDependent] = useState(null);
	const [dependencyConditions, setDependencyConditions] = useState({});
	const [showDependentSearch, setShowDependentSearch] = useState(false);

	const maxCharacterCountQues = 200;
	const countMaxMCQValue = 4;

	const initialCondition = {
		operator: 'OPTION_SELECTED',
		option_ids: [],
		condition_type: 'AND',
		condition_value: ''
	};

	const dynamicMCQInitialValues = mcqFields.reduce((acc, field) => {
		acc[`mcq_${field}`] = '';
		return acc;
	}, {});

	const mcqObject = Object.fromEntries(
		Array.from({ length: countMaxMCQValue }, (_, i) => [`mcq_${i + 1}`, ''])
	);

	const initialValues = {
		ques_name: '',
		...mcqObject,
		is_numeric: false,
		attach_img: true,
		is_video_required: false
	};

	const commonValidationSchema = Yup.object().shape({
		ques_name: Yup.string()
			.required('Question name is required')
			.max(maxCharacterCountQues, 'Question name is too long'),
		attach_img: Yup.boolean(),
		is_video_required: Yup.boolean(),
		is_numeric: Yup.boolean()
	});

	const validationSchema = Yup.lazy((values) => {
		if (selectedQuesType === 'Single' || selectedQuesType === 'Multiple') {
			const fieldValidations = {
				...commonValidationSchema.fields
			};

			Object.keys(dynamicMCQInitialValues).forEach((field) => {
				fieldValidations[field] = Yup.string().required(
					`MCQ ${field.split('_')[1]} is required`
				);
			});
			const dynamicMCQObj = Yup.object().shape(fieldValidations);

			return Yup.object().shape({
				...dynamicMCQObj.fields,
				...commonValidationSchema.fields
			});
		}
		return commonValidationSchema;
	});

	const formik = useFormik({
		initialValues,
		validationSchema,
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleQuesBank();
		}
	});

	// Function to handle search
	const handleSearch = (query) => {
		setSearchQuery(query);
		// Filter questions excluding current question and already selected dependencies
		const filtered = props.questions?.filter(
			(q) =>
				q.title.toLowerCase().includes(query.toLowerCase()) &&
				q.id !== props.data?.id &&
				!selectedDependencies.find((dep) => dep.id === q.id)
		);
		setSearchResults(filtered || []);
	};

	// Function to add dependent question
	const handleAddDependentQuestion = (question) => {
		setSelectedDependencies([...selectedDependencies, question]);
		setDependencyConditions({
			...dependencyConditions,
			[question.id]: {
				parentQuestionId: props.data?.id,
				childQuestionId: question.id,
				triggerConditions: [{ ...initialCondition }],
				logicalOperator: 'AND'
			}
		});
		setShowDependentSearch(false);
		setSearchQuery('');
		setSearchResults([]);
	};

	// Function to remove dependent question
	const handleRemoveDependentQuestion = (questionId) => {
		setSelectedDependencies(selectedDependencies.filter((dep) => dep.id !== questionId));
		const newConditions = { ...dependencyConditions };
		delete newConditions[questionId];
		setDependencyConditions(newConditions);
	};

	// Function to update conditions
	const handleUpdateConditions = (questionId, updatedConditions) => {
		setDependencyConditions({
			...dependencyConditions,
			[questionId]: {
				...dependencyConditions[questionId],
				...updatedConditions
			}
		});
	};

	// Handle adding option to condition
	const handleAddOptionToCondition = (questionId, conditionIndex, optionId) => {
		const conditions = { ...dependencyConditions[questionId] };
		const optionIds = conditions.triggerConditions[conditionIndex].option_ids || [];

		if (!optionIds.includes(optionId)) {
			conditions.triggerConditions[conditionIndex].option_ids = [...optionIds, optionId];
			handleUpdateConditions(questionId, conditions);
		}
	};

	// Handle removing option from condition
	const handleRemoveOptionFromCondition = (questionId, conditionIndex, optionId) => {
		const conditions = { ...dependencyConditions[questionId] };
		conditions.triggerConditions[conditionIndex].option_ids = conditions.triggerConditions[
			conditionIndex
		].option_ids.filter((id) => id !== optionId);
		handleUpdateConditions(questionId, conditions);
	};

	// Original functions
	const addMCQField = () => {
		const nextField = mcqFields.length + 1;
		setMCQFields([...mcqFields, nextField]);
	};

	const removeMCQField = (field) => {
		setMCQFields(mcqFields.slice(0, -1));
		formik.setFieldValue(`mcq_${field}`, '');
	};

	const handleCLoseModal = () => props.handleChangeViewOrEditModal({ open: false, data: null });

	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const handleChangeQuestionsType = (value) => {
		setSelectedQuesType(value.title);
	};

	// Function to handle saving question with dependencies
	const handleQuesBank = () => {
		try {
			formik.validateForm(formik.values).then((errors) => {
				if (Object.keys(errors).length === 0) {
					setSaveLoading(true);
					const findQuesType = props.questionType.find(
						(items) => items.title === selectedQuesType
					);

					let submissionData = {
						que_type_id: findQuesType.id,
						title: formik.values.ques_name,
						attachment_required: formik.values.attach_img ? 1 : 0,
						is_video_required: formik.values.is_video_required ? 1 : 0,
						dependencies: Object.values(dependencyConditions)
					};

					if (findQuesType.id === 3) {
						submissionData.is_numeric = formik.values.is_numeric ? 1 : 0;
					}

					if (findQuesType.id !== 3) {
						const mcqValuesArray = Object.keys(formik.values)
							.filter(
								(key) =>
									key.startsWith('mcq_') &&
									formik.values[key] !== '' &&
									formik.values[key] !== null
							)
							.map((key) => formik.values[key]);
						submissionData.options = mcqValuesArray;
					}

					if (props.edit) {
						props.editQuestions({
							id: props.data.id,
							...submissionData
						});
					} else {
						props.addQuestions(submissionData);
					}

					handleCLoseModal();
				}
			});
		} catch (error) {
			setSaveLoading(false);
		}
	};

	useEffect(() => {
		if (props.data) {
			const formattedData = {
				ques_name: props.data.title,
				attach_img: props.data.attachment_required,
				is_video_required: props.data.is_video_required
			};
			if (props.data.question_type.title !== 'Descriptive') {
				props.data?.question_options.forEach((option, index) => {
					formattedData[`mcq_${index + 1}`] = option?.title || '';
				});
			}
			formik.setValues(formattedData);
			setSelectedQuesType(props.data.question_type.title);

			// Load existing dependencies if any
			if (props.data.dependencies) {
				setSelectedDependencies(props.data.dependencies.map((dep) => dep.childQuestion));
				const conditions = {};
				props.data.dependencies.forEach((dep) => {
					conditions[dep.childQuestionId] = {
						parentQuestionId: dep.parentQuestionId,
						childQuestionId: dep.childQuestionId,
						triggerConditions: dep.triggerConditions,
						logicalOperator: dep.logicalOperator
					};
				});
				setDependencyConditions(conditions);
			}
		}
	}, [props.data]);

	return (
		<AddOrEditModalWrapper
			isOpen={props.open}
			centered
			toggle={handleCLoseModal}
			style={{ width: '100%', maxWidth: '650px' }}
			backdrop={'static'}>
			<ModalHeader toggle={handleCLoseModal}>
				{props.edit ? 'Edit' : 'Add'} Question
			</ModalHeader>

			<ModalBody>
				{/* Original form content */}
				{(props.data || !props?.edit) && (
					<>
						{!props.data && (
							<FormGroup>
								<Label>Select Question types</Label>
								<Dropdown
									className={`mt-0 mb-10 ${
										!props.data ? 'bg-blue' : 'cursor-disabled'
									}`}
									isOpen={dropdownOpen}
									disabled={props.data}
									toggle={toggleRoleDropDown}>
									<DropdownToggle caret className="roleBtn text-truncate">
										{selectedQuesType || 'Select'}
									</DropdownToggle>

									<DropdownMenu className="w-100" style={{ maxWidth: '50%' }}>
										{props.questionType?.length ? (
											props.questionType.map((item, index) => (
												<DropdownItem
													key={index}
													onClick={() => handleChangeQuestionsType(item)}
													className="text-truncate">
													{item.title}
												</DropdownItem>
											))
										) : (
											<DropdownItem disabled>No Data</DropdownItem>
										)}
									</DropdownMenu>
								</Dropdown>
							</FormGroup>
						)}

						{/* Question Form */}
						<Form>
							{selectedQuesType && (
								<FormGroup>
									<Label>Question Name</Label>
									<Input
										id="ques_name"
										name="ques_name"
										value={formik.values.ques_name}
										onChange={formik.handleChange}
										type="textarea"
										placeholder="Your Question..."
									/>
									<div
										className={`font-14 mt-5 ${
											characterCount > maxCharacterCountQues
												? 'text-danger'
												: ''
										}`}>
										Total characters you can add{' '}
										{formik.values.ques_name.length}/{maxCharacterCountQues}
									</div>
									{formik.touched.ques_name && formik.errors.ques_name && (
										<span className="error-msg my-2">
											{formik.errors.ques_name}
										</span>
									)}
								</FormGroup>
							)}

							{/* MCQ Fields */}
							{selectedQuesType && selectedQuesType !== 'Descriptive' && (
								<>
									<div className="d-flex justify-content-between align-items-center mb-10">
										<p>
											<strong>MCQs</strong>
										</p>
										<Button
											className="btn form-button w-auto"
											disabled={mcqFields.length > 3}
											type="button"
											onClick={addMCQField}>
											Add MCQ
										</Button>
									</div>
									{mcqFields.map((field, index) => (
										<div key={field}>
											<FormGroup>
												<div className="d-flex align-items-center w-100">
													<Label className="mr-30 ml-10 mt-7">
														{String.fromCharCode(64 + field)}
													</Label>
													<div className="d-flex flex-row align-items-center w-100">
														<Input
															id={`mcq_${field}`}
															name={`mcq_${field}`}
															value={formik.values[`mcq_${field}`]}
															onChange={formik.handleChange}
															type="text"
															placeholder={`MCQ Options ${field}`}
															className="mt-10 mb-10"
														/>
														{index >= 2 && (
															<img
																src={RejectIcon}
																onClick={() =>
																	removeMCQField(field)
																}
																alt="cross-icon"
																title="Delete MCQ type"
																className="ml-10 cursor-pointer"
																width="20px"
															/>
														)}
													</div>
												</div>
												{formik.touched[`mcq_${field}`] &&
													formik.errors[`mcq_${field}`] && (
														<span className="error-msg my-0 ml-55">
															{formik.errors[`mcq_${field}`]}
														</span>
													)}
											</FormGroup>
										</div>
									))}
								</>
							)}

							{/* Question Type Specific Fields */}
							{selectedQuesType && (
								<>
									{selectedQuesType === 'Descriptive' && (
										<FormGroup className="mt-10" check>
											<Label for="is_numeric" check>
												<Input
													id="is_numeric"
													name="is_numeric"
													checked={formik.values.is_numeric}
													onChange={() =>
														formik.setFieldValue(
															'is_numeric',
															!formik.values.is_numeric
														)
													}
													type="checkbox"
												/>{' '}
												Is Numeric
											</Label>
										</FormGroup>
									)}
									<FormGroup className="mt-10" check>
										<Label for="attach_img" check>
											<Input
												id="attach_img"
												name="attach_img"
												checked={formik.values.attach_img}
												onChange={() =>
													formik.setFieldValue(
														'attach_img',
														!formik.values.attach_img
													)
												}
												type="checkbox"
											/>{' '}
											Attach Image
										</Label>
									</FormGroup>
									<FormGroup className="mt-10" check>
										<Label for="is_video_required" check>
											<Input
												id="is_video_required"
												name="is_video_required"
												checked={formik.values.is_video_required}
												onChange={() =>
													formik.setFieldValue(
														'is_video_required',
														!formik.values.is_video_required
													)
												}
												type="checkbox"
											/>{' '}
											Attach Video
										</Label>
									</FormGroup>
								</>
							)}
						</Form>

						{/* Dependent Questions Section */}
						{selectedQuesType && props.data && (
							<div className="mt-4">
								<div className="d-flex justify-content-between align-items-center mb-3">
									<h5>Dependent Questions</h5>
									<Button
										className="btn form-button w-auto"
										type="button"
										onClick={() => setShowDependentSearch(true)}>
										Add Dependent Question
									</Button>
								</div>

								{/* Search Modal */}
								<Modal
									isOpen={showDependentSearch}
									toggle={() => setShowDependentSearch(false)}
									className="modal-dialog-centered">
									<ModalHeader toggle={() => setShowDependentSearch(false)}>
										Search Questions
									</ModalHeader>
									<ModalBody>
										<Input
											type="text"
											placeholder="Search questions..."
											value={searchQuery}
											onChange={(e) => handleSearch(e.target.value)}
											className="mb-3"
										/>
										<div
											className="search-results"
											style={{ maxHeight: '400px', overflow: 'auto' }}>
											{searchResults.map((question) => (
												<Card
													key={question.id}
													className="mb-2 cursor-pointer hover:bg-gray-100"
													onClick={() =>
														handleAddDependentQuestion(question)
													}>
													<CardBody>
														<div className="d-flex flex-wrap gap-2">
															<div className="flex-grow-1">
																{question.title}
															</div>
															<Badge color="primary">
																{question.question_type.title}
															</Badge>
															{question.question_options?.map(
																(option) => (
																	<Badge
																		key={option.id}
																		color="secondary">
																		{option.title}
																	</Badge>
																)
															)}
														</div>
													</CardBody>
												</Card>
											))}
											{searchResults.length === 0 && searchQuery && (
												<div className="text-center py-3">
													No questions found
												</div>
											)}
										</div>
									</ModalBody>
								</Modal>

								{/* Selected Dependencies */}
								{selectedDependencies.map((question) => (
									<Card key={question.id} className="mb-3">
										<CardBody>
											<div className="d-flex justify-content-between align-items-start">
												<div>
													<h6>{question.title}</h6>
													<div className="d-flex flex-wrap gap-2 mt-2">
														<Badge color="primary">
															{question.question_type.title}
														</Badge>
														{question.question_options?.map(
															(option) => (
																<Badge
																	key={option.id}
																	color="secondary">
																	{option.title}
																</Badge>
															)
														)}
													</div>
												</div>
												<div className="d-flex gap-2">
													<Button
														className="btn btn-sm btn-outline-primary"
														onClick={() =>
															setActiveDependent(question)
														}>
														Set Conditions
													</Button>
													<Button
														className="btn btn-sm btn-outline-danger"
														onClick={() =>
															handleRemoveDependentQuestion(
																question.id
															)
														}>
														Remove
													</Button>
												</div>
											</div>

											{/* Display current conditions */}
											{dependencyConditions[
												question.id
											]?.triggerConditions?.map((condition, index) => (
												<div
													key={index}
													className="mt-2 p-2 bg-light rounded">
													<div className="d-flex justify-content-between align-items-center">
														<small>
															<strong>Operator:</strong>{' '}
															{condition.operator}
															{condition.option_ids?.length > 0 && (
																<>
																	<br />
																	<strong>
																		Selected Options:
																	</strong>{' '}
																	{question.question_options
																		.filter((opt) =>
																			condition.option_ids.includes(
																				opt.id
																			)
																		)
																		.map((opt) => opt.title)
																		.join(', ')}
																</>
															)}
															{condition.condition_value && (
																<>
																	<br />
																	<strong>Value:</strong>{' '}
																	{condition.condition_value}
																</>
															)}
														</small>
													</div>
												</div>
											))}
										</CardBody>
									</Card>
								))}
							</div>
						)}

						{/* Condition Setting Modal */}
						<Modal
							isOpen={activeDependent !== null}
							toggle={() => setActiveDependent(null)}
							className="modal-dialog-centered">
							<ModalHeader toggle={() => setActiveDependent(null)}>
								Set Conditions for {activeDependent?.title}
							</ModalHeader>
							<ModalBody>
								{activeDependent && (
									<>
										<FormGroup>
											<Label>Logical Operator between conditions</Label>
											<Input
												type="select"
												value={
													dependencyConditions[activeDependent.id]
														?.logicalOperator
												}
												onChange={(e) =>
													handleUpdateConditions(activeDependent.id, {
														...dependencyConditions[activeDependent.id],
														logicalOperator: e.target.value
													})
												}>
												<option value="AND">AND</option>
												<option value="OR">OR</option>
											</Input>
										</FormGroup>

										{dependencyConditions[
											activeDependent.id
										]?.triggerConditions.map((condition, index) => (
											<Card key={index} className="mb-3 p-3">
												<FormGroup>
													<Label>Operator</Label>
													<Input
														type="select"
														value={condition.operator}
														onChange={(e) => {
															const newConditions = [
																...dependencyConditions[
																	activeDependent.id
																].triggerConditions
															];
															newConditions[index] = {
																...condition,
																operator: e.target.value,
																option_ids: [],
																condition_value: ''
															};
															handleUpdateConditions(
																activeDependent.id,
																{
																	...dependencyConditions[
																		activeDependent.id
																	],
																	triggerConditions: newConditions
																}
															);
														}}>
														<option value="OPTION_SELECTED">
															Option Selected
														</option>
														<option value="EQUALS">Equals</option>
														<option value="GREATER_THAN">
															Greater Than
														</option>
														<option value="LESS_THAN">Less Than</option>
														<option value="CONTAINS">Contains</option>
													</Input>
												</FormGroup>

												{condition.operator === 'OPTION_SELECTED' && (
													<FormGroup>
														<Label>Select Options</Label>
														<div className="d-flex flex-wrap gap-2">
															{activeDependent.question_options?.map(
																(option) => (
																	<Badge
																		key={option.id}
																		color={
																			condition.option_ids?.includes(
																				option.id
																			)
																				? 'primary'
																				: 'secondary'
																		}
																		className="cursor-pointer p-2"
																		onClick={() => {
																			if (
																				condition.option_ids?.includes(
																					option.id
																				)
																			) {
																				handleRemoveOptionFromCondition(
																					activeDependent.id,
																					index,
																					option.id
																				);
																			} else {
																				handleAddOptionToCondition(
																					activeDependent.id,
																					index,
																					option.id
																				);
																			}
																		}}>
																		{option.title}
																	</Badge>
																)
															)}
														</div>
													</FormGroup>
												)}

												{condition.operator !== 'OPTION_SELECTED' && (
													<FormGroup>
														<Label>Value</Label>
														<Input
															type={
																[
																	'GREATER_THAN',
																	'LESS_THAN'
																].includes(condition.operator)
																	? 'number'
																	: 'text'
															}
															value={condition.condition_value}
															onChange={(e) => {
																const newConditions = [
																	...dependencyConditions[
																		activeDependent.id
																	].triggerConditions
																];
																newConditions[index] = {
																	...condition,
																	condition_value: e.target.value
																};
																handleUpdateConditions(
																	activeDependent.id,
																	{
																		...dependencyConditions[
																			activeDependent.id
																		],
																		triggerConditions:
																			newConditions
																	}
																);
															}}
															placeholder="Enter value"
														/>
													</FormGroup>
												)}
											</Card>
										))}

										<Button
											className="btn btn-sm btn-primary mt-2"
											onClick={() => {
												const newConditions = [
													...dependencyConditions[activeDependent.id]
														.triggerConditions,
													{ ...initialCondition }
												];
												handleUpdateConditions(activeDependent.id, {
													...dependencyConditions[activeDependent.id],
													triggerConditions: newConditions
												});
											}}>
											Add Condition
										</Button>
									</>
								)}
							</ModalBody>
							<ModalFooter>
								<Button onClick={() => setActiveDependent(null)}>Done</Button>
							</ModalFooter>
						</Modal>
					</>
				)}
			</ModalBody>

			<ModalFooter>
				<div>
					<Button
						type="submit"
						className="btn form-button"
						onClick={formik.handleSubmit}
						loading={saveLoading}
						disabled={formik.isSubmitting || selectedQuesType.length === 0}>
						Save
					</Button>
				</div>
				<div>
					{!props.data && (
						<Button
							onClick={handleCLoseModal}
							loading={formik.isSubmitting}
							type="submit"
							className="btn form-button c-secondary">
							Cancel
						</Button>
					)}
				</div>
			</ModalFooter>
		</AddOrEditModalWrapper>
	);
};

export default AddOrEditModalQuestionBank;
