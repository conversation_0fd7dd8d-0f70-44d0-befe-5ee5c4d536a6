import React from 'react';
import { useState } from 'react';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>dal<PERSON>eader,
	Accordion,
	AccordionItem,
	AccordionHeader,
	AccordionBody,
	Card,
	Badge,
	CardBody
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Question.style';

const ViewModalQuesBank = (props) => {
	const [openAccordion, setOpenAccordion] = useState('quesBank_1');

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	console.log('Props Data....', props?.data?.dependencies?.childQuestions);

	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Question Bank</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<Accordion open={openAccordion} toggle={toggleAccordion}>
							<AccordionItem>
								<AccordionHeader targetId="quesBank_1">
									Question Bank Details
								</AccordionHeader>
								<AccordionBody accordionId="quesBank_1">
									<div>
										{props?.data?.question_type?.title && (
											<div className="mb-10 mt-10">
												<strong>Question Type: </strong>
												<span className="badge text-bg-primary fw-normal align-text-bottom ml-5">
													{props?.data?.question_type?.title}
												</span>
											</div>
										)}
										<div className="mb-10 mt-10">
											<strong>Image Attachment: </strong>
											<span
												className={`badge fw-normal align-text-bottom ml-5 ${
													props?.data?.attachment_required
														? 'text-bg-success'
														: 'text-bg-danger'
												}`}>
												{props?.data?.attachment_required ? 'Yes' : 'No'}
											</span>
										</div>
										{props?.data?.is_video_required && (
											<div className="mb-10 mt-10">
												<strong>Video Attachment: </strong>
												<span
													className={`badge fw-normal align-text-bottom ml-5 ${
														props?.data?.is_video_required
															? 'text-bg-success'
															: 'text-bg-danger'
													}`}>
													{props?.data?.is_video_required ? 'Yes' : 'No'}
												</span>
											</div>
										)}
										<div className="mb-10 mt-15">
											<strong>Question & Options.</strong>
											<hr className="mx-1" />
											<ol className="m-0 mt-10">
												<li>
													{props?.data?.title}
													{props?.data?.question_type_id === 3 ? (
														<span
															className={`badge fw-normal text-bg-info text-white ml-10 align-text-bottom`}>
															{props?.data?.is_numeric
																? 'Numeric'
																: 'Text'}
														</span>
													) : null}
												</li>
											</ol>
											{props?.data?.question_options &&
												props?.data?.question_options.length > 0 && (
													<div className="d-flex justify-content-start flex-wrap align-items-center gap-2 mt-10">
														{props?.data?.question_options.map(
															(items, index) => (
																<div className="card w-49">
																	<div className="card-body">
																		<p>
																			<strong>
																				{String.fromCharCode(
																					65 + index
																				) + '. '}
																			</strong>
																			{items.title}
																		</p>
																	</div>
																</div>
															)
														)}
													</div>
												)}
										</div>
									</div>
									{props?.data?.dependencies?.childQuestions.map((data) => (
										<Card key={data.question.id} className="mb-3">
											<CardBody>
												<div className="d-flex justify-content-between align-items-start">
													<div>
														<h6>{data.question.title}</h6>
														<div className="d-flex flex-wrap gap-2 mt-2">
															<Badge color="primary">
																{data.question.question_type.title}
															</Badge>
															{data.question.question_options?.map(
																(option) => (
																	<Badge
																		key={option.id}
																		color="secondary">
																		{option.title}
																	</Badge>
																)
															)}
														</div>
													</div>
												</div>

												{/* Display current conditions */}
												{data.triggerConditions.map((condition, index) => (
													<div
														key={index}
														className="mt-2 p-2 bg-light rounded">
														<div className="d-flex justify-content-between align-items-center">
															<small>
																<strong>Operator:</strong>{' '}
																{condition.operator}
																{condition.option_ids?.length >
																	0 && (
																	<>
																		<br />
																		<strong>
																			Selected Options:
																		</strong>{' '}
																		{question.question_options
																			.filter((opt) =>
																				condition.option_ids.includes(
																					opt.id
																				)
																			)
																			.map((opt) => opt.title)
																			.join(', ')}
																	</>
																)}
																{condition.condition_value && (
																	<>
																		<br />
																		<strong>Value:</strong>{' '}
																		{condition.condition_value}
																	</>
																)}
															</small>
														</div>
													</div>
												))}
											</CardBody>
										</Card>
									))}
								</AccordionBody>
							</AccordionItem>
						</Accordion>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalQuesBank;
