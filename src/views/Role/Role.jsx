/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { getApi, postApi } from 'src/helper/api/Api';
import { ROLE, ROLES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { VideoWrapper, StatusWrapper } from './Role.style';
import AddOrEditModal from './Modal/AddOrEditModal';
import Button from '../../components/button/Button';
import EditIcon from 'src/assets/images/Edit.svg';
import DeleteIcon from '../../assets/images/delete.svg';
import Swal from 'sweetalert2';
import { CONFIRM_DELETE } from 'src/components/header/constants';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const Role = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Role',
					resizable: false,
					Cell: (row) => <div className="text-overflow">{row?.original?.name}</div>,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Module with permissions',
					resizable: false,
					Cell: (row) => (
						<div className="module-wise-permissions">
							<div className="w-100">
								{row?.original?.role_permissions?.length > 0
									? row?.original?.role_permissions.map((role, index) => (
											<div key={index}>
												<p className="title">#{index + 1} Module</p>
												<StatusWrapper>
													<div className="status module-tag">
														{role.module_data.title}
													</div>
												</StatusWrapper>
												<div className="permission-container">
													<p className="title pl-5">Permissions</p>
													<div className="d-flex gap-2 flex-wrap">
														{role.permissions_data.map(
															(item, index) => (
																<StatusWrapper key={index}>
																	<div className="status permission-tag">
																		{item.name}
																	</div>
																</StatusWrapper>
															)
														)}
													</div>
												</div>
											</div>
									  ))
									: '-'}
							</div>
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							{cell?.original?.feedback_status?.id !== 2 ? (
								<img
									src={EditIcon}
									alt="EditIcon"
									title="View"
									width={23}
									className="mr-10 cursor-pointer"
									onClick={handleChangeViewOrEditModal({
										open: true,
										view: false,
										data: cell.original
									})}
								/>
							) : null}
							<img
								src={DeleteIcon}
								alt="ViewIcon"
								title="Delete"
								width={21}
								className="mr-10 cursor-pointer"
								onClick={() => handleChangeDeleteModal(cell.original.id)}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	useEffect(() => {
		getRoles();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangeDeleteModal = (id) => {
		const params = {
			id: id
		};
		Swal.fire(CONFIRM_DELETE).then((result) => {
			if (result.isConfirmed) {
				postApi(ROLE.DELETE, { ...params })
					.then((response) => {
						if (response.data.status) {
							getRoles();
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};

	const getRoles = async (id) => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(ROLE.GET, { ...dataToSend });
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.roles);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addRole = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(ROLE.ADD, data, '');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getRoles();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const editRole = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(ROLE.UPDATE, data, '');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getRoles();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	return (
		<>
			<VideoWrapper>
				<PageTitle
					title="sidebar.role"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="btn-container">
							<Button
								className="btn form-button add-skill-btn"
								onClick={handleChangeViewOrEditModal({
									open: true,
									data: null,
									view: false
								})}>
								Add Role
							</Button>
						</div>

						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</VideoWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getRoles={getRoles}
					addRole={addRole}
					editRole={editRole}
				/>
			)}
		</>
	);
};

export default Role;
