/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup, Input } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Role.style';
import Loader from 'src/components/common/Loader';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const AddOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);

	useEffect(() => {
		if (props.data) {
			formik.setFieldValue('name', props.data.name);
		}
	}, [props.data]);

	const validationSchema = Yup.object().shape({
		name: Yup.string().required('Role is required!')
	});

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			handleCLoseModal();
			if (props.data) {
				const dataToSend = {
					id: props.data.id,
					name: values.name
				};
				props.editRole(dataToSend);
			} else {
				props.addRole(values);
			}
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSaveLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			name: ''
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.data ? 'Edit' : 'Add'} Role
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Role</Label>

									<Input
										id="name"
										name="name"
										value={formik.values.name}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="text"
										placeholder="Your role..."
									/>
									{formik.touched.name && formik.errors.name && (
										<span className="error-msg mt-0">{formik.errors.name}</span>
									)}
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								{props.data ? 'Save' : 'Add'}
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModal;
