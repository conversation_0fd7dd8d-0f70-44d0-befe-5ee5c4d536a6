import React from 'react';
import PageTitle from '../../components/common/PageTitle';
import ReportsWrapper from './Reports.style';
import { useEffect } from 'react';
import { useState } from 'react';
import { getApi } from 'src/helper/api/Api';
import { DASHBOARD, REPORTS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Loader from 'src/components/common/Loader';
import Toaster from 'src/components/common/Toaster';
import { useRef } from 'react';
import Chart from 'react-apexcharts';
import { FormGroup, Label } from 'reactstrap';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';

const Reports = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [years, setYears] = useState([]);
	const [selectedYear, setSelectedYear] = useState(null);

	const [pieData, setPieData] = useState({
		options: {
			series: [],
			labels: [
				'Service Head',
				'Service Engineer',
				'Fabricators',
				'Existing Supervisors',
				'Freelancer'
			],
			legend: {
				fontSize: '18px',
				fontWeight: 500,
				fontFamily: 'Inter, sans-serif',
				position: 'bottom',
				markers: {
					width: 17,
					height: 17,
					offsetY: 0
				}
			},
			chart: {
				type: 'donut'
			},
			plotOptions: {
				pie: {
					donut: {
						labels: {
							show: true,
							value: {
								show: true,
								fontSize: '40px',
								fontFamily: 'Inter, sans-serif',
								fontWeight: 700,
								color: '#000000',
								offsetY: 14,
								formatter: function (val) {
									return val;
								}
							},
							total: {
								show: true,
								showAlways: true,
								label: 'Total',
								fontFamily: 'Inter, sans-serif',
								fontSize: '18px',
								fontWeight: 700,
								color: '#7e7e7e',
								formatter: function (w) {
									return w.globals.seriesTotals.reduce((a, b) => {
										return a + b;
									}, 0);
								}
							}
						}
					}
				}
			},
			responsive: [
				{
					breakpoint: 480,
					options: {
						chart: {
							width: 200
						},
						legend: {
							position: 'bottom'
						}
					}
				}
			]
		}
	});

	const [barData, setBarData] = useState({
		series: [
			{
				name: 'New',
				data: []
			},
			{
				name: 'Ongoing',
				data: []
			},
			{
				name: 'Completed',
				data: []
			}
		],
		options: {
			chart: {
				type: 'bar',
				height: 350,
				stacked: true
			},
			plotOptions: {
				bar: {
					horizontal: false,
					dataLabels: {
						total: {
							enabled: true,
							offsetX: 0,
							style: {
								fontSize: '13px',
								fontWeight: 900
							}
						}
					}
				}
			},
			stroke: {
				width: 1,
				colors: ['#fff']
			},
			title: {
				text: ''
			},
			xaxis: {
				categories: [
					'Jan',
					'Feb',
					'Mar',
					'Apr',
					'May',
					'Jun',
					'Jul',
					'Aug',
					'Sep',
					'Oct',
					'Nov',
					'Dec'
				],
				labels: {
					formatter: function (val) {
						return val + '';
					}
				}
			},
			yaxis: {
				title: {
					text: undefined
				}
			},
			tooltip: {
				y: {
					formatter: function (val) {
						return val + '';
					}
				}
			},
			fill: {
				opacity: 1
			},
			legend: {
				position: 'top',
				horizontalAlign: 'left',
				offsetX: 0
			}
		}
	});

	const getReports = async () => {
		try {
			setLoading(true);
			const params = {};
			if (selectedYear?.name) {
				params.year = selectedYear?.name;
			}
			const response = await getApi(REPORTS.GET, { ...params }, 'order');
			if (response?.status === CODES.SUCCESS) {
				const responseData = response?.data?.data;
				const updatedPendingData = Object.keys(responseData).map((month) => {
					return responseData[month].New;
				});

				const updatedOngoingData = Object.keys(responseData).map((month) => {
					return responseData[month].Ongoing;
				});

				const updatedCompletedData = Object.keys(responseData).map((month) => {
					return responseData[month].Completed;
				});

				const updatedBarData = {
					...barData,
					series: [
						{
							name: 'New',
							data: updatedPendingData
						},
						{
							name: 'Ongoing',
							data: updatedOngoingData
						},
						{
							name: 'Completed',
							data: updatedCompletedData
						}
					]
				};

				setBarData(updatedBarData);
				setData(responseData);
			}
		} catch (error) {
			setData([]);
			setBarData([]);
			setLoading(false);
			toaster.current.error(error.message);
		} finally {
			setLoading(false);
		}
	};

	const getPieReports = async () => {
		try {
			setLoading(true);
			const response = await getApi(DASHBOARD.GET, {});
			if (response?.status === CODES.SUCCESS) {
				const responseData = response?.data?.data;

				// Calculate seriesData for main categories (service_head, service_engineer, fabricators)
				const mainCategoriesData = Object.values(responseData).map((category) => {
					if (Array.isArray(category)) {
						return category.map((zone) => zone.user_count).reduce((a, b) => a + b, 0);
					} else {
						// Handle supervisors (existing and freelancer)
						return Object.values(category).flatMap((subCategory) =>
							subCategory.map((zone) => zone.user_count).reduce((a, b) => a + b, 0)
						);
					}
				});

				// Combine the data for all categories
				const seriesData = [].concat(...mainCategoriesData);

				setPieData((prevState) => ({
					...prevState,
					options: {
						...prevState.options,
						series: seriesData
					}
				}));
			}
		} catch (error) {
			setPieData([]);
			setLoading(false);
			toaster.current.error(error.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangeStatusDropdown = (items) => {
		setSelectedYear(items);
	};

	useEffect(() => {
		if (selectedYear?.name) {
			getReports();
		}
	}, [selectedYear]);

	useEffect(() => {
		const currentYear = new Date().getFullYear();
		const startYear = 2000;
		const yearsArray = Array.from({ length: currentYear - startYear + 1 }, (_, index) => ({
			id: startYear + index,
			name: `${startYear + index}`
		}));
		const sortedYearsArray = yearsArray.sort((a, b) => b.id - a.id);

		// Set the default selected status to the latest year
		setSelectedYear(sortedYearsArray[0]);

		setYears(sortedYearsArray);
		getPieReports();
	}, []);

	return (
		<ReportsWrapper>
			<PageTitle title="sidebar.dashboard" className="plr-0" />

			<div className="plr-0">
				<Loader loading={loading} />
				<div className="row">
					<div className="col-md-12">
						<p className="mb-0 mt-20 main-heading">User Reports</p>
					</div>
					<div className="card mt-20">
						<Chart
							options={pieData.options}
							series={pieData.options.series}
							type="donut"
							width="100%"
							height="100%"
						/>
					</div>
					<div className="col-md-12">
						<div className="d-flex align-items-end justify-content-between">
							<p className="mb-10 mt-20 main-heading">Orders Reports</p>
							<FormGroup className="mt-0">
								<SingleDropdown
									data={years}
									keyProps={['name']}
									onSelect={handleChangeStatusDropdown}
									selectedData={selectedYear}
								/>
							</FormGroup>
						</div>
						<div className="card mt-20">
							<Chart
								options={barData.options}
								series={barData.series}
								type="bar"
								width="100%"
								height="100%"
							/>
						</div>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
		</ReportsWrapper>
	);
};

export default Reports;
