import styled from 'styled-components';

const ReportsWrapper = styled.div`
	.card {
		width: 100%;
		height: 400px;
		padding: 15px;
		background-color: #fff;
		border: unset;
		border-radius: 10px;
		transition: 0.5 all;
		.name {
			padding: 5px 18px;
			background: #2bafe5;
			border-radius: 60px;
			width: fit-content;
			color: #fff;
			font-size: 16px;
			font-weight: 700;
			vertical-align: middle;
		}
		.count {
			font-size: 72px;
			font-weight: bold;
			line-height: normal;
		}
		.total-count {
			font-size: 14px;
			font-weight: 600;
			padding-bottom: 25px;
			margin-bottom: auto;
		}
		.image-card {
			img {
				width: 30px;
				height: 30px;
				margin-right: 10px;
			}
		}
		&.bg-1 {
			background-color: #f6d78b;
		}
		&.bg-2 {
			background-color: #eebfac;
		}
		&.bg-3 {
			background-color: #ffa2df;
		}
		&.bg-4 {
			background-color: #06e2ff;
		}
		&.bg-5 {
			background-color: #a7fac2;
		}
		.zone {
			padding: 3px 5px;
			background-color: #fff9e4;
			color: #ff8463;
			font-size: 9px;
			border-radius: 6px;
			line-height: normal;
		}
	}
	.card-text {
		font-size: 14px;
		font-weight: 500;
		color: #737373;
	}
	.card-count {
		font-size: 28px;
		font-weight: 600;
		color: #000;
	}
	.border-bottom-1 {
		padding-bottom: 3px;
		border-bottom: 1px solid #efefef;
	}
	.main-heading {
		margin-left: 30px;
		padding-top: 4px;
		font-size: 16px;
		font-weight: 600;
		position: relative;
		&::before {
			content: '';
			position: absolute;
			left: -30px;
			top: 0;
			width: 15px;
			height: 30px;
			border-radius: 5px;
			background-color: #cabdff;
		}
	}
	.pills {
		background: #efefef;
		padding: 9px 10px;
		border-radius: 10px;
		display: flex;
		width: fit-content;
		gap: 0px;
		position: relative;
		max-width: 100%;
		span {
			padding: 4px 15px;
			border-radius: 8px;
			font-size: 14px;
			font-weight: 600;
			cursor: pointer;
			transition: 0.3s all;
			position: relative;
			z-index: 1;
			&.active {
				background-color: #ffffff;
				transition: 0.3s all;
			}
		}
		.active-slider {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 6px;
			margin: auto;
			height: calc(100% - 10px);
			width: calc(100% / 4 - 5px);
			padding: 4px 15px;
			border-radius: 8px;
			background-color: #ffffff;
			transition: 0.3s all;
			&.East {
				left: 6px;
			}
			&.West {
				left: 69px;
			}
			&.South {
				left: 136px;
			}
			&.North {
				left: 206px;
			}
		}
	}
`;

export default ReportsWrapper;
