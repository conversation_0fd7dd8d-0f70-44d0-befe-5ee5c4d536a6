/* eslint-disable no-unused-vars */
import React, { useRef, useState, useEffect } from 'react';
import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Input,
	InputGroup,
	// InputGroupAddon,
	InputGroupText,
	Label
} from 'reactstrap';
// import { connect } from 'react-redux';
// import { compose } from 'redux';
// import { withRouter } from 'react-router-dom';
import classNames from 'classnames';
// import VisibilityIcon from '@mui/icons-material/Visibility';
// import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

import { MAX_LENGTH, MIN_LENGTH, REGEX_VALIDATION, ROUTES } from '../../helper/constant';
// import AuthActions from "../../redux/auth/actions";
import { MobileValidator } from './LoginValidator';
import Toaster from '../../components/common/Toaster';
import Button from '../../components/button/Button';
import { postApi, getApi } from '../../helper/api/Api';
import { LoginStyles } from './login.style';
import { AUTH, ROLES } from '../../helper/api/endPoint';
import Error from '../../components/common/Error';
import { formAttributes } from '../../helper/functions';
import { ERROR } from '../../helper/messages';
import { useDispatch, useSelector } from 'react-redux';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useNavigate } from 'react-router-dom';
import { login } from 'src/redux/Slices/auth-slice';
import EYE_ICON from '../../assets/images/eye-svgrepo-com.svg';
import EYE_DISABLE_ICON from '../../assets/images/eye-slash-svgrepo-com.svg';
// const { login } = AuthActions;

const MobileLoginForm = (props) => {
	const toaster = useRef();
	const navigate = useNavigate();
	const dispatch = useDispatch();

	const isLogin = useSelector((state) => state?.auth?.isLogin);
	// const classes = LoginStyles();

	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [role, setRole] = useState([]);

	const {
		handleChange,
		handleSubmit,
		values,
		errors,
		handleBlur,
		touched,
		setValues,
		setFieldValue
	} = useFormik({
		validationSchema: Yup.object().shape({
			mobile: Yup.string()
				.matches(REGEX_VALIDATION.MOBILE, 'Please enter valid Mobile number.')
				.min(MIN_LENGTH.MOBILE, 'Minimum length should be 10 digits.')
				.max(MAX_LENGTH.MOBILE, 'Maximum length should be 10 digits.')
				.required('Mobile number is required.'),
			password: Yup.string().required('Please enter password.')
			// role: Yup.string().trim()
		}),
		initialValues: {
			password: '',
			// role: '',
			mobile: ''
		},
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleLogin();
		}
	});

	useEffect(() => {
		getRolesApi();

		if (isLogin) {
			navigate(ROUTES.DASHBOARD.BASE);
		}
		// eslint-disable-next-line
	}, []);

	const togglePasswordVisibility = () => {
		setShowPassword((prevShowPassword) => !prevShowPassword);
	};

	const handleSelectRole = (role) => {
		setFieldValue && setFieldValue('role', role);
	};
	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const getRolesApi = () => {
		getApi(ROLES.GET, {
			scope: 'CMS'
		})
			.then((response) => {
				if (response && response.data) {
					const apiData = response.data?.roles;
					if (apiData?.length) {
						setRole(apiData);
					}
				} else {
					toaster.current.error(error?.response?.data?.message);
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	//implemented login api
	const handleLogin = () => {
		const params = {
			type: 'MOBILE',
			mobile: values.mobile.toString(),
			country_code: '+91',
			password: values.password
			// role_id: values.role.id
		};

		setLoading(true);

		postApi(AUTH.LOGIN, params)
			.then((response) => {
				if (response) {
					const apiData = response.data?.data.user;

					if (apiData?.token) {
						toaster.current.success(response.data.message);
						setTimeout(() => {
							dispatch(
								login({
									isLogin: true,
									id: apiData?.id,
									email: apiData?.email,
									accessToken: apiData?.token,
									firstName: apiData?.first_name,
									lastName: apiData?.last_name,
									country_code: apiData?.country_code,
									mobile: apiData?.mobile,
									role: apiData?.role
								})
							);
						}, 1000);
					} else {
						toaster.current.error(error?.response?.data?.message);
					}
				} else {
					toaster.current.error(ERROR.DEFAULT);
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	const handleKeyPress = (e) => {
		if (e.key === 'Enter') {
			// Call handleSubmit when Enter key is pressed
			handleSubmit(e);
		}
	};

	return (
		<>
			<form className="pa-6 mt-18">
				{/* <div className="form-group">
					<Dropdown className="mt-20" isOpen={dropdownOpen} toggle={toggleRoleDropDown}>
						<DropdownToggle caret className={'roleBtn'}>
							{values?.role?.name || 'Select Role'}
						</DropdownToggle>

						<DropdownMenu className="width100">
							{role.length ? (
								role.map((role, index) => {
									return (
										<DropdownItem
											key={index}
											onClick={() => handleSelectRole(role)}>
											{role.name}
										</DropdownItem>
									);
								})
							) : (
								<DropdownItem disabled> No Data </DropdownItem>
							)}
						</DropdownMenu>
					</Dropdown>
					<Error errors={errors} touched={touched} fieldName="role" />
				</div> */}

				<div className="form-group">
					<Label>Enter Mobile</Label>
					<div className="flex">
						{/* <div> */}
						<Input
							type="text"
							value="+91"
							className={`form-control form-control-lg react-form-input mr-10 w-15 ${'mobilePadding'}`}
							disabled
						/>
						{/* <Error {...props} fieldName="countryCode" /> */}
						{/* </div> */}

						<div className="width100">
							<Input
								type="number"
								className="form-control form-control-lg react-form-input"
								placeholder="Enter mobile"
								{...formAttributes('mobile')}
							/>
							<Error errors={errors} touched={touched} fieldName="mobile" />
						</div>
					</div>
					<div className={'center'}></div>
				</div>

				<div className="form-group mt-10">
					<Label>Password</Label>
					<InputGroup>
						<Input
							type={showPassword ? 'text' : 'password'}
							placeholder="Enter password"
							onPaste={(e) => {
								e.preventDefault();
								return false;
							}}
							className={`form-control form-control-lg react-form-input ${'password'}`}
							{...formAttributes('password')}
							onKeyDown={handleKeyPress}
						/>
						<InputGroupText onClick={() => togglePasswordVisibility('password')}>
							{showPassword ? (
								<img src={EYE_ICON} alt="eye-icon" className="show-hide-icon" />
							) : (
								<img
									src={EYE_DISABLE_ICON}
									alt="eye-icon"
									className="show-hide-icon"
								/>
							)}
						</InputGroupText>
						{/* <InputGroupAddon
							addonType='append'
							onClick={togglePasswordVisibility}
						>
							<InputGroupText>
								{showPassword
									? 'VisibilityIcon' // <VisibilityIcon />
									: 'VisibilityOffIcon' // <VisibilityOffIcon />
								}
							</InputGroupText>
						</InputGroupAddon> */}
					</InputGroup>
					<Error errors={errors} touched={touched} fieldName="password" />
					<div
						className={classNames('textCenter', 'linkLabel')}
						onClick={() => navigate(ROUTES.PASSWORD.FORGOT_PASSWORD)}>
						Forgot Password?
					</div>
				</div>

				<div className={'center'}>
					<Button
						loading={loading}
						className={classNames('btnText', 'primaryFormButton')}
						dataStyle="expand-right"
						onClick={handleSubmit}>
						Log In
					</Button>
				</div>
			</form>

			<Toaster ref={toaster} />
		</>
	);
};

// const mapStateToProps = (state) => {
//     return {
//         isLogin: state.auth.isLogin,
//     };
// };

export default MobileLoginForm;
