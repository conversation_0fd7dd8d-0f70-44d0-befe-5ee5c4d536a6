import styled from 'styled-components';

export const LoginStyles = styled.div`
	.loginContainer {
		display: flex;
		width: 100%;
		height: 100vh;
		@media (max-width: 1024px) {
			flex-direction: column;
			overflow-y: scroll;
		}
		@media (max-width: 850px) {
			flex-direction: column;
			overflow-y: scroll;
		}
		@media (max-width: 770px) {
			overflow-y: scroll;
		}
	}
	.img_div {
		width: 55%;
		height: 100vh;
		@media (max-width: 1024px) {
			height: 50%;
			width: 100%;
		}
		@media (max-width: 850px) {
			width: 100%;
		}
	}
	.img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		@media (max-width: 1024px) {
			height: 100%;
		}
		@media (max-width: 850px) {
			height: 47vh;
		}
		// @media (max-width:600px) {
		//     height: 35vh;
		// }
	}
	.form_container {
		width: 50%;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: auto;
		@media (max-width: 1024px) {
			width: 100%;
			margin-top: 0px;
		}
		@media (max-width: 850px) {
			width: 100%;
		}
		// @media (max-width:600px) {
		//     height: 65vh;
		// }
		// @media (max-width:380px)]: {
		//     height: 42vh
		// }
	}
	.form {
		width: 65%;
		margin: auto;
		height: auto;
		display: flex;
		flex-direction: column;
		justify-content: center;
		@media (max-width: 1024px) {
			width: 80%;
			margin-top: 0px;
			// height: 68vh
		}
		@media (max-width: 850px) {
			width: 80%;
			margin-top: 10px;
			// height: 40vh
		}
		@media (max-width: 430px) {
			width: 90%;
			// height: 65vh
		}
		@media (max-width: 380px) {
			width: 90%;
			// height: 42vh
		}
	}
	.textCenter {
		text-align: center !important;
	}
	.linkLabel {
		float: right;
		font-weight: bold;
		font-size: 14px;
		margin-top: 5px;
		cursor: pointer;
		color: #76b828;
	}
	.loginIcon {
		margin-bottom: 10px;
		text-align: center;
		@media (max-width: 1024px) {
			margin-bottom: 0px;
		}
	}
	.Title {
		font-size: 22px;
		line-height: 32px;
		font-weight: 600;
		letter-spacing: 2px !important;
		text-align: center;
	}
	.subtext {
		font-size: 16px;
		line-height: 32px;
		font-weight: 600;
		letter-spacing: normal !important;
		text-align: center;
	}
	.btnText {
		background-color: #1cb4e3;
		color: white;
		font-weight: 600;
		border: none;
		padding: 9px;
	}
	.primaryFormButton {
		font-size: 18px;
		text-transform: capitalize;
		box-shadow: none !important;
		width: 100%;
		border-radius: 12px;
		background: #1cb4e3;
		color: white;
		border-color: #1cb4e3 !important;
		position: relative;
		margin-top: 24px;
		&:hover {
			background-color: #1cb4e3 !important;
			border-color: #1cb4e3 !important;
			opacity: 0.9;
		}
	}
	.center {
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		width: 100%;
	}
	.mobilePadding {
		padding: 0px;
		min-width: 50px;
		width: 50px;
		text-align: center;
	}
	.roleBtn {
		display: flex;
		box-shadow: none !important;
		align-items: center;
		border-radius: 6px;
		width: 100%;
		background: #8dc968 !important;
		border: #8dc968 !important;
		height: calc(1.5em + 1rem);
		justify-content: space-between;
		&:hover {
			background: #8dc968;
			border: #8dc968;
		}
		&.btn-secondary.dropdown-toggle {
			background: #8dc968;
			border: #8dc968;
		}
	}
	.company_logo {
		height: 120px;
		width: 240px;
		@media (max-width: 850px) {
			height: 70px;
			width: 160px;
		}
	}
	.password {
		border-radius: 6px 0px 0px 6px;
	}
	.otp {
		width: 100px;
	}
	.tab {
		background: #f4fbef;
		padding: 6px;
	}
	.navLink {
		background: green;
		&.active {
			background: pink;
		}
	}
	.otpBtn {
		border: none;
		background: none;
		color: #8dc968;
		font-weight: bold;
		cursor: pointer;
		&.not-active {
			color: #1cb4e396;
			cursor: not-allowed;
		}
	}
	.show-hide-icon {
		width: 21px;
		height: 21px;
		cursor: pointer;
	}
`;
