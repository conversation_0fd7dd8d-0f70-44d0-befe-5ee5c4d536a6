/* eslint-disable no-unused-vars */
import React, { useRef, useState, useEffect } from 'react';
import {
	Dropdown,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Input,
	InputGroup,
	InputGroupText,
	Label
} from 'reactstrap';

import { ROUTES } from '../../helper/constant';
import Toaster from '../../components/common/Toaster';
import Button from '../../components/button/Button';
import { postApi, getApi } from '../../helper/api/Api';
import { AUTH, ROLES } from '../../helper/api/endPoint';
import Error from '../../components/common/Error';
import { ERROR } from '../../helper/messages';
import { useDispatch, useSelector } from 'react-redux';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useNavigate } from 'react-router-dom';
import { login } from 'src/redux/Slices/auth-slice';
import EYE_ICON from '../../assets/images/eye-svgrepo-com.svg';
import EYE_DISABLE_ICON from '../../assets/images/eye-slash-svgrepo-com.svg';
// const { login } = AuthActions;

const EmailLoginForm = (props) => {
	const isLogin = useSelector((state) => state?.auth?.isLogin);
	const toaster = useRef();
	const navigate = useNavigate();
	const dispatch = useDispatch();
	// const classes = LoginStyles();

	const [showPassword, setShowPassword] = useState(false);
	const [loading, setLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [role, setRole] = useState([]);

	const {
		handleChange,
		handleSubmit,
		values,
		errors,
		handleBlur,
		touched,
		setValues,
		setFieldValue
	} = useFormik({
		validationSchema: Yup.object().shape({
			email: Yup.string().email().trim().required('Email is required.'),
			password: Yup.string().required('Please enter password.')
			// role: Yup.string().trim()
		}),
		initialValues: {
			email: '',
			password: ''
			// role: ''
		},
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleLogin();
		}
	});

	useEffect(() => {
		// getRolesApi();

		if (isLogin) {
			navigate(ROUTES.DASHBOARD.BASE);
		}
		// eslint-disable-next-line
	}, []);

	const togglePasswordVisibility = () => {
		setShowPassword((prevShowPassword) => !prevShowPassword);
	};

	const handleSelectRole = (role) => {
		setFieldValue && setFieldValue('role', role);
	};
	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const getRolesApi = () => {
		getApi(ROLES.GET, {
			params: {
				scope: 'CMS'
			}
		})
			.then((response) => {
				if (response && response.data) {
					const apiData = response.data?.roles;
					if (apiData?.length) {
						setRole(apiData);
					}
				} else {
					toaster.current.error(error?.response?.data?.message);
				}
			})
			.finally(() => {
				setLoading(false);
			});
	};

	//implemented login api
	const handleLogin = () => {
		const params = {
			type: 'EMAIL',
			email: values.email,
			password: values.password
		};

		setLoading(true);

		postApi(AUTH.LOGIN, params)
			.then((response) => {
				if (response) {
					const apiData = response.data?.data.user;
					if (apiData?.token) {
						toaster.current.success(response.data.message);
						setTimeout(() => {
							dispatch(
								login({
									isLogin: true,
									id: apiData?.id,
									email: apiData?.email,
									accessToken: apiData?.token,
									firstName: apiData?.first_name,
									lastName: apiData?.last_name,
									mobile: apiData?.mobile
								})
							);
						}, 1000);
					} else {
						toaster.current.error(error?.response?.data?.message);
					}
				} else {
					toaster.current.error(ERROR.DEFAULT);
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};
	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	const handleKeyPress = (e) => {
		if (e.key === 'Enter') {
			setLoading(true);
			handleSubmit(e);
		}
	};

	return (
		<>
			<form className="pa-6 mt-18">
				<div className="form-group">
					<Error errors={errors} touched={touched} fieldName="role" />
				</div>

				<div className="form-group">
					<Label>Email</Label>
					<Input
						type="email"
						className="form-control form-control-lg  react-form-input"
						placeholder="Enter your Email"
						{...formAttributes('email')}
					/>
					<Error errors={errors} touched={touched} fieldName="email" />
				</div>

				<div className="form-group mt-10">
					<Label>Password</Label>
					<InputGroup>
						<Input
							type={showPassword ? 'text' : 'password'}
							placeholder="Enter password"
							onPaste={(e) => {
								e.preventDefault();
								return false;
							}}
							className={`form-control form-control-lg react-form-input password`}
							{...formAttributes('password')}
							onKeyDown={handleKeyPress}
						/>
						<InputGroupText onClick={() => togglePasswordVisibility('password')}>
							{showPassword ? (
								<img src={EYE_ICON} alt="eye-icon" className="show-hide-icon" />
							) : (
								<img
									src={EYE_DISABLE_ICON}
									alt="eye-icon"
									className="show-hide-icon"
								/>
							)}
						</InputGroupText>
					</InputGroup>
					<Error errors={errors} touched={touched} fieldName="password" />
					<div
						className="textCenter linkLabel"
						onClick={() => navigate(ROUTES.PASSWORD.FORGOT_PASSWORD)}>
						Forgot Password?
					</div>
				</div>

				<div className="center">
					<Button
						loading={loading}
						className="btnText primaryFormButton"
						dataStyle="expand-right"
						onClick={handleSubmit}>
						Log In
					</Button>
				</div>
			</form>

			<Toaster ref={toaster} />
		</>
	);
};

// const mapStateToProps = state => {
// 	return {
// 		isLogin: state.auth.isLogin
// 	};
// };
export default EmailLoginForm;
