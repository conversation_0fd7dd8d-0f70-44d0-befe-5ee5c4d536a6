import React, { useRef, useState, useEffect } from 'react';
import { NavLink, Nav, NavItem } from 'reactstrap';
// import { connect } from 'react-redux';
// import { compose } from 'redux';
// import { withRouter } from 'react-router-dom';

import { ForgotTabs, ROUTES } from '../../helper/constant';
import { ROLES } from '../../helper/api/endPoint';
import { getApi } from '../../helper/api/Api';
import { LoginStyles } from './login.style';
import personImage from '../../assets/images/personimage.jpg';
// import AuthActions from '../../redux/auth/actions';
import Toaster from '../../components/common/Toaster';
import EmailLoginForm from './EmailLoginForm';
import MobileLoginForm from './MobileLoginForm';

import ICON_DEMO from '../../assets/images/Logo.svg';
import { useSelector } from 'react-redux';

// const { login } = AuthActions;

const Login = (props) => {
	const toaster = useRef();
	const isLogin = useSelector((state) => state?.auth?.isLogin);
	// const classes = LoginStyles();

	const [activeTab, setActiveTab] = useState('EMAIL');
	const [, setRole] = useState([]);

	useEffect(() => {
		// getRolesApi();

		if (isLogin) {
			// props.history.push(ROUTES.DASHBOARD.BASE);
			// alert('asd');
		}
		// eslint-disable-next-line
	}, []);

	const toggleTab = (tab) => {
		if (activeTab !== tab) {
			setActiveTab(tab);
		}
	};

	const getRolesApi = () => {
		getApi(ROLES.GET, {
			params: {
				scope: 'CMS'
			}
		}).then((response) => {
			if (response && response.data) {
				const apiData = response.data?.roles;
				if (apiData?.length) {
					setRole(apiData);
				}
			} else {
				toaster.current.error(error?.response?.data?.message);
			}
		});
	};

	return (
		<>
			<LoginStyles {...props}>
				<div className="loginContainer">
					<div className="img_div">
						<img src={personImage} alt="person" className="img"></img>
					</div>

					<div className="form_container">
						<div className="form">
							<div className="loginIcon">
								<img src={ICON_DEMO} alt="icon" className="company_logo" />
							</div>
							<div className="subtext">Sign in to your account</div>

							<form className="pa-24">
								<Nav pills className="border-radius-30 overflow-hidden w-100">
									{ForgotTabs.map((items, index) => (
										<div key={index} style={{ width: '50%' }}>
											<NavItem
												className={`text-center cursor-pointer tab`}
												key={index}>
												<NavLink
													className={
														activeTab === items.value ? 'active' : ''
													}
													onClick={() => toggleTab(items.value)}>
													{items.label}
												</NavLink>
											</NavItem>
										</div>
									))}
								</Nav>
								{activeTab === 'EMAIL' ? <EmailLoginForm /> : <MobileLoginForm />}
							</form>
							<Toaster ref={toaster} />
						</div>
					</div>
				</div>
			</LoginStyles>
		</>
	);
};

// const mapStateToProps = state => {
// 	return {
// 		isLogin: state.auth.isLogin
// 	};
// };

export default Login;
