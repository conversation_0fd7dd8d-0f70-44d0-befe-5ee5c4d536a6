import { withFormik } from "formik";
import * as Yup from "yup";
import {
    MAX_LENGTH,
    MIN_LENGTH,
    REGEX_VALIDATION,
} from "../../helper/constant";


export const EmailValidator = withFormik({
    validationSchema: Yup.object().shape({
        email: Yup.string().email().trim().required("Email is required."),
        password: Yup.string().required("Please enter password."),
        role: Yup.string().trim(),
    }),

    mapPropsToValues: (props) => ({
        email: "",
        password: "",
        role: "",
    }),
    handleSubmit: (values) => {},
    displayName: "EmailValidator",
    enableReinitialize: true,
});

export const MobileValidator = withFormik({
    validationSchema: Yup.object().shape({
        mobile: Yup.string()
            .matches(
                REGEX_VALIDATION.MOBILE,
                "Please enter valid Mobile number."
            )
            .min(MIN_LENGTH.MOBILE, "Minimum length should be 10 digits.")
            .max(MAX_LENGTH.MOBILE, "Maximum length should be 10 digits.")
            .required("Mobile number is required."),
        password: Yup.string().required("Please enter password."),
        role: Yup.string().trim(),
    }),

    mapPropsToValues: (props) => ({
        password: "",
        role: "",
        mobile: "",
    }),
    handleSubmit: (values) => {},
    displayName: "MobileValidator",
    enableReinitialize: true,
});
