/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE, TEMPLATE_ACTIONS } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import EditIcon from 'src/assets/images/Edit.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { SPACES, TEMPLATES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import SettingsWrapper from '../Settings/Settings.style';
import ViewOrEditModalNotifications from './Modal/ViewOrEditModalNotifications';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const Notifications = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [templatesActions, setTemplatesActions] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'Notifications',
			columns: [
				{
					Header: 'Title',
					resizable: false,
					Cell: (row) => `${row.original.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Body',
					resizable: false,
					Cell: (row) =>
						`${
							row.original.body
								? row.original.body
								: 'Hello {USERNAME}, your transaction of {AMOUNT} amount'
						}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				// {
				// 	Header: 'Active',
				// 	width: 100,
				// 	Cell: (cell) => (
				// 		<Switch
				// 			checked={cell.original.is_active}
				// 			onChange={() =>
				// 				handleChangeActiveInactive(
				// 					cell.original.id,
				// 					!cell.original.is_active
				// 				)
				// 			}
				// 		/>
				// 	),
				// 	headerClassName: 'text-right pa-20',
				// 	className: 'text-left pa-20 d-flex align-items-center'
				// },
				{
					Header: 'Action',
					resizable: false,
					width: 100,
					Cell: (cell) => (
						<div className="">
							<img
								src={EditIcon}
								alt="EditIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					minWidth: 80
				}
			]
		}
	];

	useEffect(() => {
		getNotifications();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getNotifications = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT,
				type: TEMPLATE_ACTIONS.NOTIFICATION
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}
			const response = await getApi(TEMPLATES.GET, {
				...dataToSend
			});
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.templates);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addNotifications = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(TEMPLATES.ADD, data);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				// getNotifications();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const editNotifications = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(TEMPLATES.UPDATE, data);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getNotifications();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<SettingsWrapper>
				<PageTitle
					title="sidebar.notifications"
					search={false}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</SettingsWrapper>
			{addOrEditModalData.open && (
				<ViewOrEditModalNotifications
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getNotifications={getNotifications}
					editNotifications={editNotifications}
					addNotifications={addNotifications}
					templatesActions={templatesActions}
				/>
			)}
		</>
	);
};

export default Notifications;
