/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useRef, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AddOrEditModalWrapper } from 'src/views/AddressApprove/AddressApprove.style';
import EmailEditor, { EditorRef, EmailEditorProps } from 'react-email-editor';

const AddOrEditModalEmail = (props) => {
	const emailEditorRef = useRef(null);
	const [saveLoading, setSaveLoading] = useState(false);

	useEffect(() => {
		if (props.data) {
			formik.setValues({
				title: props.data.title,
				body: props.data.body
			});
		}
	}, [props.data]);

	const handleCLoseModal = props.handleChangeViewOrEditModal({
		open: false,
		data: null
	});

	const validationSchema = Yup.object().shape({
		title: Yup.string().required('Title is required!'),
		body: Yup.string().required('Body is required!')
	});

	const onSubmitHandler = async (values) => {
		try {
			const data = values;
			data.id = props.data.id;
			props.editNotifications(data);
			handleCLoseModal();
		} catch (error) {
			console.log(error);
		}
	};

	const formik = useFormik({
		initialValues: {
			title: '',
			body: ''
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	const onReady = (unlayer) => {
		let json = {};
		if (!emailEditorRef.current)
			setTimeout(() => emailEditorRef.current.editor.loadDesign(JSON.parse(json)), 3000);
		else emailEditorRef.current.editor.loadDesign(JSON.parse(json));
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Notifications
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<p className="note">
									Note: Please do not change any word which is enclosed in braces{' '}
									{'{}'}
								</p>
								<FormGroup className="mt-15">
									<Label>Title</Label>
									<Input
										id="title"
										name="title"
										value={formik.values.title}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="text"
										placeholder="Type value..."
									/>
									{formik.touched.title && formik.errors.title && (
										<span className="error-msg mt-0">
											{formik.errors.title}
										</span>
									)}
								</FormGroup>
								<FormGroup className="mt-15">
									<Label>Body</Label>
									<Input
										id="body"
										name="body"
										value={formik.values.body}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="textarea"
										rows={7}
										placeholder="Type value..."
									/>
									{formik.touched.body && formik.errors.body && (
										<span className="error-msg mt-0">{formik.errors.body}</span>
									)}
								</FormGroup>
								{/* <EmailEditor ref={emailEditorRef} onReady={onReady} /> */}
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button loading={saveLoading} type="submit" className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							{props.data && props.edit && (
								<Button
									onClick={handleCLoseModal}
									loading={saveLoading}
									type="submit"
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalEmail;
