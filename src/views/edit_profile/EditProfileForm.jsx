import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Input, Label } from 'reactstrap';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { ROUTES } from '../../helper/constant';
import { Api, getApi, postApi } from '../../helper/api/Api';
import Button from '../../components/button/Button';

import { ErrorMessage, Formik, useFormik } from 'formik';
import * as Yup from 'yup';
import { EditProfileFormWrapper } from './EditProfileForm.style';
import Toaster from '../../components/common/Toaster';
import CODES from '../../helper/StatusCodes';

import { admin, login } from 'src/redux/Slices/auth-slice';
import { PROFILE, ZONE } from 'src/helper/api/endPoint';
import Error from 'src/components/common/Error';

const EditProfileForm = () => {
	const ProfileFormRef = useRef(null);
	const navigate = useNavigate();

	// Selecting specific values from the auth state
	const id = useSelector((state) => state.auth.id);
	const [user, setUser] = useState([]);

	const [loading, setLoading] = useState(false);

	const toaster = useRef();

	const { handleChange, handleSubmit, values, errors, handleBlur, touched, setValues } =
		useFormik({
			validationSchema: Yup.object().shape({
				first_name: Yup.string()
					.required('First name is required')
					.matches(/^[A-Za-z]*$/, 'Only letters are allowed in the first name.')
					.test(
						'no-spaces',
						'Spaces are not allowed in the first name.',
						(value) => !/\s/.test(value)
					),
				last_name: Yup.string()
					.required('Last name is required')
					.matches(/^[A-Za-z]*$/, 'Only letters are allowed in the last name.')
					.test(
						'no-spaces',
						'Spaces are not allowed in the last name.',
						(value) => !/\s/.test(value)
					),
				email: Yup.string()
					.email('Please enter a valid email.')
					.trim()
					.required('Email is required.')
					.nullable(),
				mobile: Yup.string().required('Mobile number is required')
			}),
			initialValues: {
				first_name: user.first_name || '',
				last_name: user.last_name || '',
				email: user.email || '',
				mobile: user.mobile || ''
			},
			validateOnChange: true,
			enableReinitialize: true,
			onSubmit: (values, { resetForm }) => {
				handleProfileSubmit();
			}
		});

	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	const handleProfileSubmit = async () => {
		try {
			setLoading(true);

			const response = await postApi(PROFILE.UPDATE, {
				id: id,
				...values
			});

			if (response.status === CODES.SUCCESS) {
				getUserProfile();
				toaster.current.success(response?.data?.message);
				setTimeout(() => {
					navigate(ROUTES.DASHBOARD.BASE);
				}, 1000);
				setLoading(false);
			}
		} catch (error) {
			setLoading(false);
			toaster.current.error(error.data.message);
		}
	};

	const getUserProfile = useCallback(async () => {
		try {
			if (id) {
				setLoading(true);

				const response = await getApi(PROFILE.GET, {
					id: id
				});

				if (response.status === CODES.SUCCESS) {
					setUser(response?.data?.data?.user);
					setLoading(false);
				}
			}
		} catch (error) {
			setLoading(false);
		}
	}, [id]);

	useEffect(() => {
		getUserProfile();
	}, [getUserProfile]);

	return (
		<EditProfileFormWrapper>
			<form innerRef={ProfileFormRef}>
				<div className="form-group">
					<Label className="fs-16 medium-text">First Name</Label>
					<Input
						type="text"
						className="form-control react-form-input"
						placeholder="First Name"
						{...formAttributes('first_name')}
					/>
					<Error errors={errors} touched={touched} fieldName="first_name" />
				</div>
				<div className="form-group">
					<Label className="fs-16 medium-text">Last Name</Label>
					<Input
						type="text"
						className="form-control react-form-input"
						placeholder="Last Name"
						{...formAttributes('last_name')}
					/>

					<Error errors={errors} touched={touched} fieldName="last_name" />
				</div>
				<div className="form-group">
					<Label className="fs-16 medium-text">Email</Label>
					<Input
						type="email"
						className="form-control react-form-input"
						placeholder="Email"
						disabled
						{...formAttributes('email')}
					/>
					<Error errors={errors} touched={touched} fieldName="email" />
				</div>
				<div className="form-group">
					<Label className="fs-16 medium-text">Mobile Number.</Label>
					<Input
						type="text"
						className="form-control react-form-input"
						placeholder="mobile"
						disabled
						{...formAttributes('mobile')}
					/>
					<Error errors={errors} touched={touched} fieldName="mobile" />
				</div>
				<div className="d-flex justify-content-center">
					<Button
						type="button"
						className="btn form-button col-1"
						style={{ maxWidth: '125px' }}
						loading={loading}
						onClick={handleSubmit}>
						Submit
					</Button>
				</div>
			</form>
			<Toaster ref={toaster} />
		</EditProfileFormWrapper>
	);
};

export default EditProfileForm;
