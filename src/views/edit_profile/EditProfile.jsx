import React from 'react';

import EditProfileForm from './EditProfileForm';
import PageTitle from '../../components/common/PageTitle';
import EditProfileWrapper from './EditProfile.style';
import { useNavigate } from 'react-router';
import { useSelector } from 'react-redux';

const EditProfile = (props) => {
	const submitFormHandler = (data) => {};
	const navigate = useNavigate();

	const themeChanger = useSelector((state) => state.themeChanger);

	return (
		<EditProfileWrapper {...props} sidebarTheme={themeChanger.sidebarTheme}>
			<div className="pos-relative">
				<PageTitle title="header.editprofile" className="plr-0" />
				<div
					className="back-icon fs-15 demi-bold-text cursor-pointer"
					onClick={() => navigate('/dashboard')}>
					<i className="fas fa-step-backward"></i> Back
				</div>
			</div>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-header module-header"></div>
					<div className="roe-card-body">
						<EditProfileForm onSubmit={submitFormHandler} />
					</div>
				</div>
			</div>
		</EditProfileWrapper>
	);
};

export default EditProfile;
