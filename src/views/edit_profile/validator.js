import { withFormik } from "formik";
import * as Yup from "yup";

const formikEnhancer = withFormik({
    validationSchema: Yup.object().shape({
        firstName: Yup
            .string()
            .trim()
            .matches(/^[A-Za-z ]*$/, "Only letters and spaces are allowed in the first name.")
            .required("First name is required."),
        lastName: Yup
            .string()
            .trim()
            .required("Last name is required."),
        email: Yup
            .string()
            .email("Please enter valid email.")
            .trim()
            .required("Email is required.").nullable(),
    }),
    mapPropsToValues: props => ({
        firstname: props.firstName,
        lastname: props.lastName,
        email: props.email,
        address: props.address
    }),
    handleSubmit: values => { },
    displayName: "CustomValidationForm",
    enableReinitialize: true
});

export default formikEnhancer;
