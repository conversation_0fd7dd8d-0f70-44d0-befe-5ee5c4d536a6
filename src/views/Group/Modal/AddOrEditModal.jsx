/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup, Input } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Group.style';
import Loader from 'src/components/common/Loader';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Dropdown from 'src/components/Dropdown/Dropdown';

const AddOrEditModal = (props) => {
	const [roles, setRoles] = useState([]);
	const [saveLoading, setSaveLoading] = useState(false);
	const [selectedRoles, setSelectedRoles] = useState(null);

	useEffect(() => {
		if (props.data) {
			formik.setFieldValue('name', props.data.name);
			formik.setFieldValue('big_spring_id', props.data.big_spring_id);

			const roleIds = props.data.group_role_data.map((item) => item.role_data.id);
			formik.setFieldValue('role_ids', roleIds);
			setSelectedRoles(roleIds);

			const updatedRoles = props.roles.map((role) => {
				if (roleIds.includes(role.id)) {
					return {
						...role,
						selected: true
					};
				}
				return role;
			});
			setRoles(updatedRoles);
		} else {
			setRoles(
				props.roles.map((value) => ({
					selected: false,
					id: value.id,
					name: value.name,
					is_active: value.is_active,
					is_deleted: value.is_deleted
				}))
			);
		}
	}, [props.data, props.roles]);

	const validationSchema = Yup.object().shape({
		name: Yup.string().required('Group is required!'),
		big_spring_id: Yup.string().required('ID is required.'),
		role_ids: Yup.array().min(1, 'Please select at least one role.')
	});

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const handleChangeRoleDropdown = (ids) => {
		setSelectedRoles(ids);
		formik.setFieldValue('role_ids', ids);
	};

	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			handleCLoseModal();
			if (props.data) {
				const dataToSend = {
					id: props.data.id,
					...values
				};
				props.editGroup(dataToSend);
			} else {
				props.addGroup(values);
			}
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSaveLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			name: '',
			big_spring_id: '',
			role_ids: []
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.data ? 'Edit' : 'Add'} Group
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Group</Label>
									<Input
										id="name"
										name="name"
										value={formik.values.name}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="text"
										placeholder="Your group..."
									/>
									{formik.touched.name && formik.errors.name && (
										<span className="error-msg mt-0">{formik.errors.name}</span>
									)}
								</FormGroup>
								<FormGroup className="mt-10">
									<Label>Big Sprint Group ID</Label>
									<Input
										id="big_spring_id"
										name="big_spring_id"
										value={formik.values.big_spring_id}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="text"
										placeholder="Your ID..."
									/>
									{formik.touched.big_spring_id &&
										formik.errors.big_spring_id && (
											<span className="error-msg mt-0">
												{formik.errors.big_spring_id}
											</span>
										)}
								</FormGroup>
								<FormGroup className="mt-5">
									<Label>Role</Label>
									<Dropdown
										data={roles}
										placeholder="Select roles"
										handleChange={handleChangeRoleDropdown}
										disabled={false}
									/>
									{formik.touched.role_ids && formik.errors.role_ids && (
										<span className="error-msg mt-0">
											{formik.errors.role_ids}
										</span>
									)}
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								{props.data ? 'Save' : 'Add'}
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModal;
