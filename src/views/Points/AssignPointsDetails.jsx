/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import EditIcon from 'src/assets/images/Edit.svg';
import ViewIcon from '../../assets/images/View.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { FEEDBACK, POINTS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import { VideoWrapper, StatusWrapper } from './Points.style';
import { useLocation, useNavigate } from 'react-router-dom';
import { convertDateTimeToLocal, convertTimeToLocal } from 'src/helper/functions';
import Button from 'src/components/button/Button';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const AssignPointsDetails = () => {
	const { state } = useLocation();
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Work Order Type',
					resizable: false,
					Cell: (row) => `${row.original.work_order_type_data.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Assigned Points',
					resizable: false,
					Cell: (row) => `${row.original.points_to_assign}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Added By (Name)',
					resizable: false,
					Cell: (row) =>
						`${
							row.original.added_by_data.first_name +
							' ' +
							row.original.added_by_data.last_name
						}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Added By (Title)',
					resizable: false,
					Cell: (row) => `${row.original.added_by_data.email}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Transaction Type',
					resizable: false,
					Cell: (row) => `${row.original.transaction_type}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					width: 170
				},
				{
					Header: 'Created On',
					resizable: false,
					width: 220,
					Cell: (row) =>
						`${
							convertTimeToLocal(row.original.createdAt) +
							' | ' +
							convertDateTimeToLocal(row.original.createdAt)
						}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 justify-content-center d-flex align-items-center'
				}
			]
		}
	];

	useEffect(() => {
		getViewMore(state.work_order_type_data.id);
	}, []);

	const getViewMore = async (id) => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT,
				wo_type_id: id
			};
			const response = await getApi(
				POINTS.GET_ASSIGN_POINTS_MORE,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.points);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	return (
		<>
			<PageTitle
				title="sidebar.assignPointsDetails"
				search={false}
				searchKey={searchKey}
				setSearchKey={setSearchKey}
			/>
			<VideoWrapper>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={() => navigate(-1)}
							type="submit"
							className="btn form-button"
							style={{ width: '80px' }}>
							Back
						</Button>
					</div>
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</VideoWrapper>
		</>
	);
};

export default AssignPointsDetails;
