/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup, Input } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Points.style';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import Loader from 'src/components/common/Loader';
import { getApi } from 'src/helper/api/Api';
import { WORK_ORDER_TYPE } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import { API_STATUS } from 'src/helper/constant';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const AddOrEditVideoTraining = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);

	useEffect(() => {
		if (props.data) {
			formik.setValues({
				work_order_type_id: props.data.work_order_type_data.id,
				points_to_assign: props.data.points_data.points_to_assign
			});
		}
	}, [props.data]);

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const validationSchema = Yup.object().shape({
		work_order_type_id: Yup.string().required('Work order type is required!'),
		points_to_assign: Yup.number().required('Points is required!')
	});

	const onSubmitHandler = async (values) => {
		try {
			props.assignPoints(values);
			handleCLoseModal();
		} catch (error) {
			console.log(error);
		}
	};

	const formik = useFormik({
		initialValues: {
			work_order_type_id: '',
			points_to_assign: ''
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'View'} Assign Points
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{props.data && props?.edit && (
							<>
								<FormGroup className="mt-8 mb-30">
									<Label>Assign Points</Label>
									<Input
										id="points_to_assign"
										name="points_to_assign"
										value={formik.values.points_to_assign}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="number"
										placeholder="Your points to assign..."
									/>
									{formik.touched.points_to_assign &&
										formik.errors.points_to_assign && (
											<span className="error-msg mt-0">
												{formik.errors.points_to_assign}
											</span>
										)}
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditVideoTraining;
