/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import { getApi, postApi } from 'src/helper/api/Api';
import { FEEDBACK, POINTS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { VideoWrapper, StatusWrapper } from './Points.style';
import { Form, FormGroup, Input, Label } from 'reactstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Button from 'src/components/button/Button';
import Pagination from 'src/components/Pagination/Pagination';
import { convertDateTimeToLocal, convertTimeToLocal } from 'src/helper/functions';
const ReactTableFixedColumns = withFixedColumns(ReactTable);

const DefineRate = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [loading, setLoading] = useState(false);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	useEffect(() => {
		getPoints();
	}, []);

	useEffect(() => {
		if (data) {
			formik.setValues({
				points: data?.active_rate?.points,
				rate: data?.active_rate?.rate
			});
		}
	}, [data]);

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Points',
					resizable: false,
					Cell: (row) => `${row.original.points}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Rate',
					resizable: false,
					Cell: (row) => `${row.original.rate}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Currency',
					resizable: false,
					Cell: (row) => `${row.original.currency}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Created On',
					resizable: false,
					width: 220,
					Cell: (row) =>
						`${
							convertTimeToLocal(row.original.createdAt) +
							' | ' +
							convertDateTimeToLocal(row.original.createdAt)
						}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 justify-content-center d-flex align-items-center'
				}
			]
		}
	];

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const validationSchema = Yup.object().shape({
		points: Yup.number().required('Points is required!'),
		rate: Yup.number().required('Rate is required!')
	});

	const onSubmitHandler = async (values) => {
		try {
			setLoading(true);
			const response = await postApi(POINTS.UPDATE_DEFINE_RATE, values, 'order');

			if (response?.status === CODES.SUCCESS) {
				getPoints();
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			setLoading(false);
			toaster.current.success(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			points: '',
			rate: ''
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	const getPoints = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			const response = await getApi(POINTS.GET_DEFINE_RATE, { ...dataToSend }, 'order');

			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	return (
		<VideoWrapper>
			<Loader loading={loading} />
			<PageTitle title="sidebar.defineRate" />
			<div className="p-0 bg-white">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-body p-4">
						<p className="points-text">Points Conversion</p>
						<Form onSubmit={formik.handleSubmit}>
							<FormGroup className="mt-10">
								<Label>Points</Label>
								<Input
									id="points"
									name="points"
									value={formik.values.points}
									onChange={formik.handleChange}
									onBlur={formik.handleBlur}
									type="number"
									placeholder="Your points..."
								/>
								{formik.touched.points && formik.errors.points && (
									<span className="error-msg mt-0">{formik.errors.points}</span>
								)}
							</FormGroup>
							<p className="text-center mt-20">
								<strong>to</strong>
							</p>
							<FormGroup className="mt-8 mb-30">
								<Label>Rate (in ₹)</Label>
								<Input
									id="rate"
									name="rate"
									value={formik.values.rate}
									onChange={formik.handleChange}
									onBlur={formik.handleBlur}
									type="number"
									placeholder="Your rate..."
								/>
								{formik.touched.rate && formik.errors.rate && (
									<span className="error-msg mt-0">{formik.errors.rate}</span>
								)}
							</FormGroup>
							<Button
								disabled={formik.isSubmitting}
								type="submit"
								loading={loading}
								className="btn form-button mb-8 w-auto px-5">
								Save
							</Button>
						</Form>
						<p className="points-text mt-20">Points History</p>
						<ReactTableFixedColumns
							manual
							data={data.rate_history}
							pages={pages}
							sortable={false}
							columns={columns}
							page={activePage - 1}
							onPageChange={handleChangePage}
							totalCount={count}
							pageSize={TABLE.LIMIT}
							minRows={TABLE.MIN_ROW}
							LoadingComponent={Loader}
							PaginationComponent={Pagination}
							style={{ border: 'none', boxShadow: 'none' }}
							className="-striped -highlight custom-react-table-theme-class mt-10"
							defaultFilterMethod={(filter, row) => {
								const id = filter.pivotId || filter.id;
								return row[id] !== undefined
									? String(row[id].toLowerCase()).includes(
											filter.value.toLowerCase()
									  )
									: true;
							}}
						/>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
		</VideoWrapper>
	);
};

export default DefineRate;
