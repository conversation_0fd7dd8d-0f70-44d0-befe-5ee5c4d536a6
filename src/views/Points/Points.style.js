import styled from 'styled-components';
import { Modal } from 'reactstrap';

export const VideoWrapper = styled.div`
	.rejected {
		color: #000;
		padding: 5px;
		text-align: center;
		border-radius: 10px;
		background-color: red;
		max-width: 100px;
	}

	.approved {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: green;
		max-width: 100px;
	}

	.pending {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: yellow;
		max-width: 100px;
	}
	.maxWidth {
		max-width: 115px;
	}

	.icon {
		width: 18px;
		height: 18px;
	}

	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups {
		display: none;
	}
	.-padRow.-even {
		display: none;
	}
	.last-column {
		display: flex;
		justify-content: flex-end;
		gap: 5px;
		align-items: center;
	}
	.points-text {
		font-size: 16px;
		font-weight: 600;
		line-height: normal;
		margin-bottom: 0px;
		margin-left: 30px;
		padding-top: 5px;
		padding-bottom: 15px;
		position: relative;
		&::after {
			content: '';
			position: absolute;
			left: -30px;
			top: 0;
			width: 15px;
			height: 30px;
			border-radius: 5px;
			background-color: #cabdff;
		}
	}
	form {
		padding-left: 30px;
		max-width: 400px;
	}
`;

export const StatusWrapper = styled.div`
	.status {
		background-color: #fff8ec;
		padding: 6px 12px;
		border-radius: 14px;
		font-size: 14px;
		width: 80px;
		text-align: center;
		font-weight: 500;
	}
	/* New */
	.status.id-1 {
		background-color: #eaf0ff;
		color: #3061de;
	}
	/* In progress */
	.status.id-2 {
		color: #8b610d;
	}
	/* Completed */
	.status.id-4 {
		background-color: #edfef2;
		color: #0e702e;
	}
	/* Overdue */
	.status.id-3 {
		background-color: #ffefd2;
		color: #d39a28;
	}
	.status.id-spaces {
		background-color: #1cb4e3;
		color: #fff;
	}
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
`;
