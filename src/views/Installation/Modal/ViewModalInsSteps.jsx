import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../installation.style';

const ViewModalInsSteps = (props) => {
    const handleCLoseModal = props.handleChangeViewModal({
        open: false,
        data: null
    });

    return (
        <>
            <AddOrEditModalWrapper
                isOpen={props.open}
                centered
                toggle={handleCLoseModal}
                style={{ width: '100%', maxWidth: '650px' }}
                backdrop={'static'}>
                <ModalHeader toggle={handleCLoseModal}>View Installation Step</ModalHeader>

                <ModalBody>
                    <div>
                        <div className="mb-10 mt-10 text-capitalize">
                            <strong>Step:</strong> {props?.data?.title ? props?.data?.title : '-'}
                        </div>
                        <div className="mb-10 mt-10 text-capitalize">
                            <strong>Is Comment Required:</strong> {props?.data?.is_comment_required ? "Yes" : "No"}
                        </div>
                        <div className="mb-10 mt-10 text-capitalize">
                            <strong>Is Image Required:</strong> {props?.data?.is_attachment_required ? "Yes" : "No"}
                        </div>
                    </div>
                </ModalBody>

                <ModalFooter>
                    <div>
                        <Button
                            type="button"
                            onClick={handleCLoseModal}
                            className="btn form-button c-secondary">
                            Cancel
                        </Button>
                    </div>
                </ModalFooter>
            </AddOrEditModalWrapper>
        </>
    );
};

export default ViewModalInsSteps;
