/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../installation.style';

const AddOrEditModalInsSteps = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const validationSchema = Yup.object().shape({
		title: Yup.string().required('Step name is required!'),
	});

	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			let submissionData = {
				title: formik?.values?.title,
				is_comment_required: formik?.values?.is_comment_required,
				is_attachment_required: formik?.values?.is_attachment_required
			};
			if (props?.edit) {
				submissionData.id = props?.data?.id;
				props?.editStep(submissionData);
			} else {
				props?.addStep(submissionData);
			}
			setSaveLoading(false);
			handleCLoseModal();
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			title: '',
			is_comment_required: false,
			is_attachment_required: false
		},
		validationSchema,
		validateOnChange: true,
		onSubmit: onSubmitHandler
	});

	useEffect(() => {
		if (props?.data) {
			formik?.setValues({
				title: props?.data?.title ?? '',
				is_comment_required: props?.data?.is_comment_required == 1,
				is_attachment_required: props?.data?.is_attachment_required == 1,
			});
		}
	}, [props?.data]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Step
				</ModalHeader>
				<Form onSubmit={formik?.handleSubmit}>
					<ModalBody>
						<FormGroup className="mb-10">
							<Label for="title">Step Name</Label>
							<Input
								id="title"
								name="title"
								value={formik?.values?.title}
								onChange={formik?.handleChange}
								onBlur={formik?.handleBlur}
								type="text"
								placeholder="Your step..."
							/>
							{formik?.touched?.title && formik?.errors?.title && (
								<span className="error-msg mt-0">{formik.errors.title}</span>
							)}
						</FormGroup>
						<FormGroup check>
							<Label for="is_comment_required" check>
								<Input
									id="is_comment_required"
									name="is_comment_required"
									checked={formik?.values?.is_comment_required}
									onChange={() =>
										formik.setFieldValue(
											'is_comment_required',
											!formik?.values?.is_comment_required
										)
									}
									type="checkbox"
								/>{' '}
								Is Comment mandatory?
							</Label>
						</FormGroup>
						<FormGroup check>
							<Label for="is_attachment_required" check>
								<Input
									id="is_attachment_required"
									name="is_attachment_required"
									checked={formik?.values?.is_attachment_required}
									onChange={() =>
										formik.setFieldValue(
											'is_attachment_required',
											!formik?.values?.is_attachment_required
										)
									}
									type="checkbox"
								/>{' '}
								Is Image mandatory?
							</Label>
						</FormGroup>
					</ModalBody>
					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								{props?.data ? 'Save' : 'Add'}
							</Button>
						</div>
						<div>
							{!props.data && (
								<Button
									onClick={handleCLoseModal}
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalInsSteps;
