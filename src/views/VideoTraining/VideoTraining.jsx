/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE, allQuestionsTypes } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import EditIcon from 'src/assets/images/Edit.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { ROLES, SCOPES, VIDEO_TRAINING } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import VideoWrapper from './VideoTraining.style';
import AddOrEditVideoTraining from './Modal/AddOrEditModal';
import Switch from 'src/components/Switch/Switch';
import ViewIcon from '../../assets/images/View.svg';
import ViewModalVideoTraining from './Modal/ViewModal';
import { FormGroup, Label } from 'reactstrap';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const filterOption = {
	id: 0,
	title: 'All',
	is_active: true,
	is_deleted: false
};

const VideoTraining = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [filterRoles, setFilterRoles] = useState([]);
	const [selectedRoles, setSelectedRoles] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const [roles, setRoles] = useState([]);
	const [categories, setCategories] = useState([]);
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Video',
					resizable: false,
					Cell: (row) => `${row.original.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Description',
					resizable: false,
					Cell: (row) => `${row.original.tm_category_data.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Roles',
					resizable: false,
					Cell: (row) => (
						<div className="d-flex align-items-center flex-wrap gap-2">
							{row.original.tm_roles.map((items, index) => (
								<span
									key={index}
									className="badge text-bg-eternia fw-normal align-middle">
									{items.role_data.name}
								</span>
							))}
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={EditIcon}
								alt="EditIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className={`cursor-pointer mr-10'}`}
								onClick={handleChangeViewModal({
									open: true,
									data: cell.original,
									view: true
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	useEffect(() => {
		getCategories();
		getRoles();
		getVideoMaterials();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);
	const handleChangeViewModal = (params) => () => setViewModalData(params);

	const getVideoMaterials = async (id) => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await postApi(VIDEO_TRAINING.GET, {
				...dataToSend
			});
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.training_materials);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const getCategories = async () => {
		getApi(VIDEO_TRAINING.GET_CATEGORIES, {})
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.training_material_categories;

					if (apiData?.length) {
						setCategories(apiData);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const getRoles = async () => {
		getApi(ROLES.GET, { scope: 'APP', type: 'CHILD' })
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.roles;

					if (apiData?.length) {
						setRoles(apiData);
						setFilterRoles([filterOption, ...apiData]);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const editVideoMaterial = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SCOPES.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getVideoMaterials();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(VIDEO_TRAINING.UPDATE_ACTIVE_INACTIVE, {
					id: id,
					status: is_active ? 1 : 0
				});

				if (response.status === 200) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	const handleChangeRolesDropdown = (items) => {
		setSelectedRoles(items);
	};

	useEffect(() => {
		handleChangeRolesDropdown(filterOption);
	}, [filterRoles]);

	return (
		<>
			<VideoWrapper>
				<PageTitle
					title="sidebar.videoTraining"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white pt-3">
					<div className="d-flex justify-content-end align-items-start">
						{/* <FormGroup className="mr-10">
							<SingleDropdown
								data={filterRoles}
								keyProps={['name']}
								onSelect={handleChangeRolesDropdown}
								selectedData={selectedRoles}
							/>
						</FormGroup> */}
						<Button
							onClick={handleChangeViewOrEditModal({
								open: true,
								view: false,
								edit: false,
								data: null
							})}
							type="submit"
							className="btn form-button h-100 mr-10"
							style={{ width: '150px', padding: '4.3px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</VideoWrapper>
			{addOrEditModalData.open && (
				<AddOrEditVideoTraining
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					roles={roles}
					categories={categories}
					getVideoMaterials={getVideoMaterials}
					editVideoMaterial={editVideoMaterial}
				/>
			)}
			{viewModalData.open && (
				<ViewModalVideoTraining
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
					roles={roles}
					categories={categories}
				/>
			)}
		</>
	);
};

export default VideoTraining;
