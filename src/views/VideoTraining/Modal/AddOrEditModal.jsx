/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../VideoTraining.style';
import Dropdown from 'src/components/Dropdown/Dropdown';
import ReactQuill from 'react-quill';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { getApi, postApi, putApi, putVideoApi } from 'src/helper/api/Api';
import { VIDEO_TRAINING } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import { VIDEO__SUPPORTED__FORMATS, supportedFormats } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import axios from 'axios';
import ImageViewer from 'src/components/ImageTouchPoint/ImageViewer';

const AddOrEditVideoTraining = (props) => {
	const [roles, setRoles] = useState();
	const [categories, setCategories] = useState();
	const [selectedRoles, setSelectedRoles] = useState(null);
	const [selectedCategories, setSelectedCategories] = useState(null);
	const [descData, setDescData] = useState([]);
	const [step, setStep] = useState(1);
	const [saveLoading, setSaveLoading] = useState(false);
	const [imageSrc, setImageSrc] = useState(null);
	const [imageThumbSrc, setImageThumbSrc] = useState(null);

	useEffect(() => {
		if (props.data) {
			const categoriesData = {
				...props.data.tm_category_data,
				name: props.data.tm_category_data.title
			};

			formik.setFieldValue('title', props.data.title);
			// formik.setFieldValue('thumbnail_url', props.data.thumbnail);
			setImageThumbSrc(props.data.thumbnail_url);
			handleChangeDescription(props.data.description);
			handleChangeCategoriesDropdown(categoriesData);
		}
	}, [props.data]);

	useEffect(() => {
		setRoles(
			props.roles.map((value) => ({
				selected: false,
				id: value.id,
				name: value.name,
				is_active: value.is_active,
				is_deleted: value.is_deleted
			}))
		);
		setCategories(
			props.categories.map((value) => ({
				id: value.id,
				name: value.title,
				is_active: value.is_active,
				is_deleted: value.is_deleted
			}))
		);
	}, [props.roles, props.categories]);

	useEffect(() => {
		if (props.data && props.roles && props.roles.length > 0) {
			// Initialize roles with default values
			const initialRoles = props.roles.map((value) => ({
				selected: false,
				id: value.id,
				name: value.name,
				is_active: value.is_active,
				is_deleted: value.is_deleted
			}));

			// Set the initial state of roles based on props.data.tm_roles
			const rolesData = props.data.tm_roles.map((value) => ({
				selected: true,
				id: value.role_data.id,
				name: value.role_data.name,
				is_active: true,
				is_deleted: false
			}));

			const updatedRoles = initialRoles.map((role) => ({
				...role,
				selected: rolesData.some((dataRole) => dataRole.id === role.id)
			}));

			setRoles(updatedRoles);
		}
	}, [props.data, props.roles]);

	const commonValidationSchema = Yup.object().shape({
		title: Yup.string().required('Title is required!'),
		categories: Yup.string().required('Please select category.'),
		roles: Yup.array().min(1, 'Please select at least one role.'),
		description: Yup.string().required('Description is required.')
	});

	const validationOfVideo = Yup.object().shape({
		video_upload: Yup.mixed()
			.required('Video upload is required')
			.test('fileSize', 'File size is too large.! (Max upload size is 25 mb)', (value) => {
				// Max file size in bytes (e.g., 25MB)
				const maxFileSize = 25000000;
				return !value || value.size <= maxFileSize;
			})
			.test('fileFormat', 'Unsupported file format!', (value) => {
				return !value || (value && VIDEO__SUPPORTED__FORMATS.includes(value.type));
			})
	});

	const validationSchema = Yup.lazy((values) => {
		let dynamicSchema = {};
		if (step === 2) {
			const fieldValidations = {
				...validationOfVideo.fields
			};

			const dynamicMCQObj = Yup.object().shape(fieldValidations);

			return Yup.object().shape({
				...dynamicMCQObj.fields,
				...commonValidationSchema.fields
			});
		} else if (step === 1 && !props.edit) {
			dynamicSchema = {
				...commonValidationSchema.fields,
				thumbnail: Yup.mixed()
					.required('Thumbnail Image is required!')
					.test(
						'fileSize',
						'File size is too large.! (Max upload size is 5 mb)',
						(value) => {
							const maxFileSize = 5000000;
							return !value || value.size <= maxFileSize;
						}
					)
					.test('fileFormat', 'Unsupported file format!', (value) => {
						return !value || (value && supportedFormats.includes(value.type));
					})
			};
		} else {
			dynamicSchema = commonValidationSchema.fields;
		}

		return Yup.object().shape(dynamicSchema);
	});

	const addDesc = async () => {
		try {
			setSaveLoading(true);
			const data = {
				title: formik.values.title,
				training_material_category: formik.values.categories,
				thumbnail: formik.values.thumbnail,
				description: formik.values.description,
				role_ids: formik.values.roles
			};

			const response = await postApi(VIDEO_TRAINING.ADD, data, '', true);
			if (response?.status === CODES.SUCCESS) {
				setDescData(response?.data?.data);
				setStep(step + 1);
			}
		} catch (error) {
			console.log(error);
		} finally {
			setSaveLoading(false);
		}
	};

	const generateVideoURL = async () => {
		try {
			setSaveLoading(true);
			const data = {
				training_material_id: props.data.id
			};

			const response = await getApi(VIDEO_TRAINING.GENERATE_FILE_URL, data);
			if (response?.status === CODES.SUCCESS) {
				setDescData({
					training_material_id: props.data.id,
					...response?.data?.data
				});
				setStep(step + 1);
			}
		} catch (error) {
			console.log(error);
		} finally {
			setSaveLoading(false);
		}
	};

	const updateDesc = async () => {
		try {
			setSaveLoading(true);
			const data = {
				id: props.data.id,
				title: formik.values.title,
				thumbnail: formik.values.thumbnail,
				description: formik.values.description
			};

			const response = await postApi(VIDEO_TRAINING.UPDATE, data, '', true);
			if (response?.status === CODES.SUCCESS) {
				generateVideoURL();
			}
		} catch (error) {
			console.log(error);
		} finally {
			setSaveLoading(false);
		}
	};

	const markFileUpload = async () => {
		try {
			setSaveLoading(true);
			const data = {
				training_material_id: descData.training_material_id
			};
			const response = await postApi(VIDEO_TRAINING.UPDATE_FILE_UPLOAD_STATUS, data, '');
			if (response?.status === CODES.SUCCESS) {
				handleCLoseModal();
				props.getVideoMaterials();
			}
		} catch (error) {
			console.log(error);
		} finally {
			setSaveLoading(false);
		}
	};

	const uploadVideo = async (signedUrl) => {
		try {
			setSaveLoading(true);

			const response = await axios.put(signedUrl, formik.values.video_upload, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});

			if (response?.status === CODES.SUCCESS) {
				markFileUpload();
			}
		} catch (error) {
			console.error('Error uploading file:', error);
		} finally {
			setSaveLoading(false);
		}
	};

	const onSubmitHandler = async (values, { setSubmitting }) => {
		try {
			if (formik.values.description.replace(/<(.|\n)*?>/g, '').trim().length === 0) {
				formik.setFieldError('description', 'Please enter description.');
				return;
			}
			if (step === 1) {
				const isValid = await formik.validateForm();
				if (Object.keys(isValid).length === 0) {
					if (props.edit) {
						updateDesc();
					} else {
						addDesc();
					}
					return;
				}
			}

			uploadVideo(descData.signed_url);
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSubmitting(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			title: '',
			categories: '',
			roles: [],
			thumbnail: null,
			description: '',
			video_upload: null
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const handleChangeRoleDropdown = (ids) => {
		setSelectedRoles(ids);
		formik.setFieldValue('roles', ids);
	};

	const handleChangeCategoriesDropdown = (items) => {
		setSelectedCategories(items);
		formik.setFieldValue('categories', items.name);
	};

	const handleChangeDescription = (value) => formik.setFieldValue('description', value);

	// Image getting from image upload
	const handleImageChange = (event) => {
		const file = event.target.files[0];
		const reader = new FileReader();

		reader.onloadend = () => {
			setImageThumbSrc(reader.result);
		};

		if (file) {
			reader.readAsDataURL(file);
			formik.setFieldValue('thumbnail', file);
		}
	};

	// Video getting from video upload
	const handleChangeFile = (event) => {
		setImageSrc(null);
		const file = event.currentTarget.files[0];

		if (file) {
			const reader = new FileReader();

			reader.onloadend = () => {
				setImageSrc(reader.result);
			};

			reader.readAsDataURL(file);
			formik.setFieldValue('video_upload', file);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Video
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<div className={`stepper-progress ${step === 2 ? 'active' : ''}`}>
									<div>
										<div
											onClick={() => setStep(1)}
											className={`step ${step >= 1 ? 'active' : ''}`}>
											1
										</div>{' '}
										<p>Add Description</p>
									</div>
									<div>
										<div className={`step ${step === 2 ? 'active' : ''}`}>
											2
										</div>{' '}
										<p>Upload video</p>
									</div>
								</div>
								{step === 1 && (
									<>
										<FormGroup>
											<Label>Title</Label>
											<Input
												id="title"
												name="title"
												value={formik.values.title}
												onChange={formik.handleChange}
												onBlur={formik.handleBlur}
												type="text"
												placeholder="Your title..."
											/>
											{formik.touched.title && formik.errors.title && (
												<span className="error-msg mt-0">
													{formik.errors.title}
												</span>
											)}
										</FormGroup>
										<div className="row mt-10">
											<div className="col-sm-12 col-md-6">
												<FormGroup>
													<Label>Categories</Label>
													<SingleDropdown
														data={categories}
														keyProps={['name']}
														onSelect={handleChangeCategoriesDropdown}
														selectedData={selectedCategories}
														className="basic-btn"
														disabled={props.edit}
													/>
													{formik.touched.categories &&
														formik.errors.categories && (
															<span className="error-msg mt-0">
																{formik.errors.categories}
															</span>
														)}
												</FormGroup>
											</div>
											<div className="col-sm-12 col-md-6">
												<FormGroup>
													<Label>Roles</Label>
													<Dropdown
														data={roles}
														placeholder="Select roles"
														handleChange={handleChangeRoleDropdown}
														disabled={props.edit}
													/>
													{formik.touched.roles &&
														formik.errors.roles && (
															<span className="error-msg mt-0">
																{formik.errors.roles}
															</span>
														)}
												</FormGroup>
											</div>
										</div>

										<FormGroup className="mt-10">
											<Label>Thumbnail Image</Label>
											<Input
												id="thumbnail"
												className="mt-0"
												name="thumbnail"
												type="file"
												onChange={handleImageChange}
												placeholder="Type Scope value..."
											/>
											{formik.touched.thumbnail &&
												formik.errors.thumbnail && (
													<span className="error-msg mt-0">
														{formik.errors.thumbnail}
													</span>
												)}
											{imageThumbSrc ? (
												<div className="text-center mt-20">
													<img
														src={imageThumbSrc}
														alt="thumb"
														className="thumb-img"
													/>
												</div>
											) : null}
										</FormGroup>

										<FormGroup className="mt-10">
											<Label>Description</Label>
											<ReactQuill
												theme="snow"
												placeholder="Description"
												readOnly={props.viewOnly}
												value={formik.values.description}
												onChange={handleChangeDescription}
												style={{
													backgroundColor: props.viewOnly
														? '#E9ECEF'
														: undefined
												}}
											/>
											{formik.touched.description &&
												formik.errors.description && (
													<span className="error-msg mt-0">
														{formik.errors.description}
													</span>
												)}
										</FormGroup>
									</>
								)}
								{step === 2 && (
									<>
										<FormGroup>
											<Label>Video Upload</Label>
											<Input
												id="video_upload"
												name="video_upload"
												defaultValue={formik.values.video_upload}
												onChange={handleChangeFile}
												type="file"
											/>
											{formik.touched.video_upload &&
												formik.errors.video_upload && (
													<span className="error-msg mt-0">
														{formik.errors.video_upload}
													</span>
												)}
										</FormGroup>
										{imageSrc && !formik.errors.video_upload ? (
											<div className="text-center mt-20">
												<video width="100%" height="250" controls>
													<source
														src={imageSrc}
														type="video/mp4"></source>
												</video>
											</div>
										) : null}
									</>
								)}
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button
								disabled={formik.isSubmitting}
								type="submit"
								loading={saveLoading}
								className="btn form-button">
								{step === 1 ? 'Next' : 'Save'}
							</Button>
						</div>
						<div>
							{!props.data && (
								<>
									{step === 1 ? (
										<Button
											onClick={handleCLoseModal}
											type="submit"
											loading={formik.isSubmitting}
											className="btn form-button c-secondary">
											Cancel
										</Button>
									) : (
										<Button
											onClick={() => setStep(1)}
											type="submit"
											loading={formik.isSubmitting}
											className="btn form-button c-secondary">
											Back
										</Button>
									)}
								</>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditVideoTraining;
