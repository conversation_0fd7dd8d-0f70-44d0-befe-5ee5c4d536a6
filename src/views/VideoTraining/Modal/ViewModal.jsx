/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Accordion,
	AccordionItem,
	AccordionBody,
	AccordionHeader
} from 'reactstrap';
import { AddOrEditModalWrapper } from '../VideoTraining.style';
import Button from 'src/components/button/Button';
import ReactQuill from 'react-quill';

const ViewModalVideoTraining = (props) => {
	const [openAccordion, setOpenAccordion] = useState('video_1');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleCLoseModal = props.handleChangeViewModal({ open: false, data: null });

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Video Training</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							<Accordion open={openAccordion} toggle={toggleAccordion}>
								<AccordionItem>
									<AccordionHeader targetId="video_1">
										Description Details
									</AccordionHeader>
									<AccordionBody accordionId="video_1">
										{props.data.title && (
											<div className="mb-10 text-capitalize">
												<strong>Title:</strong> {props.data.title}
											</div>
										)}
										{props.data.tm_category_data.title && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Category:</strong>{' '}
												<span className="badge text-bg-secondary fw-normal align-middle">
													{props.data.tm_category_data.title}
												</span>
											</div>
										)}
										{props?.data?.tm_roles &&
										props?.data?.tm_roles.length > 0 ? (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Roles:</strong>{' '}
												{props?.data?.tm_roles.map((items, index) => (
													<span
														key={index}
														className="badge text-bg-secondary fw-normal align-middle mr-10">
														{items.role_data.name}
													</span>
												))}
											</div>
										) : null}
										{props.data.description && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Description:</strong>{' '}
												<div className="mt-10">
													<ReactQuill
														theme="snow"
														readOnly={true}
														value={props.data.description}
													/>
												</div>
											</div>
										)}
									</AccordionBody>
								</AccordionItem>
								{props.data.file_url && (
									<AccordionItem>
										<AccordionHeader targetId="video_2">
											Video file
										</AccordionHeader>
										<AccordionBody accordionId="video_2">
											<div className="mb-10 mt-10 text-capitalize">
												<video
													width="100%"
													height="250"
													controls
													className="mt-10">
													<source
														src={props.data.file_url}
														type="video/mp4"></source>
												</video>
											</div>
										</AccordionBody>
									</AccordionItem>
								)}
							</Accordion>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						{props.data && !props.edit && (
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button"
								style={{ backgroundColor: '#6c757d' }}>
								Cancel
							</Button>
						)}
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalVideoTraining;
