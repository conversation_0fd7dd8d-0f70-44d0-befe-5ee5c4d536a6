import styled from 'styled-components';
import { Modal } from 'reactstrap';

const VideoWrapper = styled.div`
	.rejected {
		color: #000;
		padding: 5px;
		text-align: center;
		border-radius: 10px;
		background-color: red;
		max-width: 100px;
	}

	.approved {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: green;
		max-width: 100px;
	}

	.pending {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: yellow;
		max-width: 100px;
	}
	.maxWidth {
		max-width: 115px;
	}

	.icon {
		width: 18px;
		height: 18px;
	}

	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups {
		display: none;
	}
	.-padRow.-even {
		display: none;
	}
	.last-column {
		display: flex;
		justify-content: flex-end;
		gap: 5px;
		align-items: center;
	}
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
	.thumb-img {
		max-width: 100%;
		height: auto;
	}

	.stepper-progress {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: auto auto 15px auto;
		max-width: 400px;
		transition: 1s all;

		&::after {
			content: '';
			display: block;
			position: absolute;
			top: 0px;
			left: 6px;
			bottom: 31px;
			right: 0;
			height: 3px;
			width: calc(100% - 145px);
			margin: auto;
			background-color: #dad6d6;
		}

		&.active {
			&::after {
				background-color: #1cb4e3;
				transition: 1s all;
			}
		}

		p {
			font-size: 14px;
			text-align: center;
			font-weight: 500;
		}

		.step {
			position: relative;
			color: #ffffff;
			text-align: center;
			margin-bottom: 10px;
			font-size: 14px;
			cursor: pointer;
			z-index: 1;

			&::after {
				content: '';
				display: block;
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
				margin: auto;
				background-color: #c7c3c3;
				width: 25px;
				height: 25px;
				border-radius: 50%;
				z-index: -1;
			}

			&.active {
				&::after {
					background-color: #1cb4e3;
				}
			}
		}
	}
`;

export default VideoWrapper;
