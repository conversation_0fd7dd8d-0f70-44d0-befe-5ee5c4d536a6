import { useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';
import StaticPagesWrapper from './StaticPages.style';
import { TABLE } from '../../helper/constant';
import { getApi, postApi } from '../../helper/api/Api';
import ViewOrEditModal from './Modals/ViewOrEditModal';
import { STATIC_PAGES } from '../../helper/api/endPoint';
import Pagination from '../../components/Pagination/Pagination';
import Loader from '../../components/common/Loader';
import PageTitle from '../../components/common/PageTitle';
import Toaster from '../../components/common/Toaster';
// ASSETS
import EditIcon from 'src/assets/images/Edit.svg';
import ViewIcon from 'src/assets/images/View.svg';
import CODES from 'src/helper/StatusCodes';

const StaticPages = () => {
	const toaster = useRef();

	const [activePage, setActivePage] = useState(1);
	const [data, setData] = useState();
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [loading, setLoading] = useState(true);
	const [viewOrEditModalData, setViewOrEditModalData] = useState({
		open: false,
		data: null,
		viewOnly: true
	});

	useEffect(() => {
		getStaticPages();
	}, [activePage]);

	const getStaticPages = async () => {
		try {
			setLoading(true);

			const params = {
				scope: 'CMS',
				type: 'PAGE',
				page: activePage,
				limit: TABLE.LIMIT
			};

			const response = await getApi(STATIC_PAGES.GET, params);

			if (response.status === CODES.SUCCESS) {
				setPages(Math.ceil(response?.data?.data?.count / TABLE.LIMIT));
				setData(response?.data?.data?.templates);
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		}
	};

	const handleEditStaticPage = async (data) => {
		try {
			toaster.current.info('Static page saving in background!');

			const response = await postApi(STATIC_PAGES.UPDATE, data);

			if (response.data.status) {
				getStaticPages();
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'Title',
			accessor: 'title',
			headerClassName: 'text-left pa-20',
			className: 'text-left pa-20'
		},
		{
			Header: 'Action',
			Cell: (cell) => (
				<>
					<img
						src={EditIcon}
						alt="EditIcon"
						title="Edit"
						width={23}
						className="cursor-pointer"
						onClick={handleChangeViewOrEditModal({
							open: true,
							viewOnly: false,
							data: cell.original
						})}
					/>

					<img
						src={ViewIcon}
						alt="View"
						title="View"
						width={23}
						className="ml-18 cursor-pointer"
						onClick={handleChangeViewOrEditModal({
							open: true,
							viewOnly: true,
							data: cell.original
						})}
					/>
				</>
			),
			headerClassName: 'text-right pa-20',
			className: 'text-right pa-20'
		}
	];

	const handleChangeViewOrEditModal = (params) => () => setViewOrEditModalData(params);

	return (
		<>
			<StaticPagesWrapper>
				<PageTitle title="sidebar.staticpages" />

				<div className="plr-0 pb-15 bg-white">
					<div className="roe-card-style">
						<div className="roe-card-body">
							<ReactTable
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class pt-3"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</StaticPagesWrapper>

			<Toaster ref={toaster} />

			{viewOrEditModalData.open && (
				<ViewOrEditModal
					{...viewOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					handleEditStaticPage={handleEditStaticPage}
				/>
			)}
		</>
	);
};

export default StaticPages;
