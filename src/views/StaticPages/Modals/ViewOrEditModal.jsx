import { useState } from 'react';
import {
	Input,
	<PERSON>dal,
	<PERSON>dal<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>oot<PERSON>,
	ModalHeader,
	Label,
	Form,
	FormGroup
} from 'reactstrap';
import ReactQuill from 'react-quill';
import Button from '../../../components/button/Button';
import { QUILL_VALIDATIONS } from '../../../helper/constant';
import { ViewOrEditModalWrapper } from '../StaticPages.style';

const ViewOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		title: props.data.title || '',
		description: props.data.body || ''
	});
	const [formErrors, setFormErrors] = useState({ title: '', description: '' });

	const handleCLoseModal = props.handleChangeViewOrEditModal({
		open: false,
		data: null,
		viewOnly: true
	});

	const handleChangeTitle =
		(key) =>
		({ target }) =>
			setFormValues((prev) => ({ ...prev, [key]: target.value }));

	const handleChangeDescription = (key) => (value) =>
		setFormValues((prev) => ({ ...prev, [key]: value }));

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			let hasError = false;
			const newFormErrors = { title: '', description: '' };

			if (!formValues.title) newFormErrors.title = 'Title is required!';
			if (QUILL_VALIDATIONS.includes(formValues.description))
				newFormErrors.description = 'Description is required!';

			setFormErrors(newFormErrors);

			Object.keys(newFormErrors).forEach((key) => {
				const element = newFormErrors[key];

				if (element) hasError = true;
			});

			if (!hasError && props.data.id) {
				handleCLoseModal();
				props.handleEditStaticPage({
					id: props.data.id,
					title: formValues.title,
					body: formValues.description
				});
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	return (
		<ViewOrEditModalWrapper>
			<Modal
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '50%', maxWidth: '800px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.viewOnly ? 'View' : 'Edit'}
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						<FormGroup>
							<Label>Title</Label>
							<Input
								type="text"
								placeholder="Title"
								value={formValues.title}
								readOnly={props.viewOnly}
								onChange={handleChangeTitle('title')}
								className="form-control react-form-input"
							/>
							<span className={props.class ? props.class : 'error-msg'}>
								{formErrors.title}
							</span>
						</FormGroup>

						<FormGroup>
							<Label>Description</Label>
							<ReactQuill
								theme="snow"
								placeholder="Description"
								readOnly={props.viewOnly}
								value={formValues.description}
								onChange={handleChangeDescription('description')}
								style={{ backgroundColor: props.viewOnly ? '#E9ECEF' : undefined }}
							/>
							<span className={props.class ? props.class : 'error-msg'}>
								{formErrors.description}
							</span>
						</FormGroup>
					</ModalBody>

					<ModalFooter>
						{!props.viewOnly && (
							<div>
								<Button
									type="submit"
									loading={saveLoading}
									className="btn form-button">
									Save
								</Button>
							</div>
						)}

						<div>
							<Button
								onClick={handleCLoseModal}
								disabled={saveLoading}
								className="btn form-button c-secondary">
								{props.viewOnly ? 'Close' : 'Cancel'}
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</Modal>
		</ViewOrEditModalWrapper>
	);
};

export default ViewOrEditModal;
