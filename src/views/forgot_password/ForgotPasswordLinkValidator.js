import { withFormik } from "formik";
import * as Yup from "yup";
import {
  MAX_LENGTH,
  MIN_LENGTH,
  REGEX_VALIDATION,
} from "../../helper/constant";
import { withRouter } from "react-router-dom";

export const withEmailFormik = (FormComponent) => {
  return withRouter(
    withFormik({
      validationSchema: Yup.object().shape({
        email: Yup.string().email().trim().required("Email is required."),
      }),
      mapPropsToValues: (props) => ({
        email: props.values?.email || "",
        // mobile: props.values?.mobile || "",
        // countryCode: "",
      }),
      handleSubmit: {},
      displayName: "EmailForm",
      enableReinitialize: true,
    })(FormComponent)
  );
};


export const withMobileFormik = (FormComponent) => {
  return withRouter(
    withFormik({
      validationSchema: Yup.object().shape({
        mobile: Yup.string()
          .matches(REGEX_VALIDATION.MOBILE, "Please enter valid Mobile number.")
          .min(MIN_LENGTH.MOBILE, "Minimum length should be 10 digits.")
          .max(MAX_LENGTH.MOBILE, "Maximum length should be 10 digits.")
          .required("Mobile number is required."),
      }),
      mapPropsToValues: (props) => ({
        // email: props.values?.email || "",
        mobile: props.values?.mobile || "",
      }),
      displayName: "MobileForm",
      handleSubmit: (values) => { },
      enableReinitialize: true,
    })(FormComponent)
  );
};
