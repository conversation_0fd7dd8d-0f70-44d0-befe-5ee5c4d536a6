import React, { useEffect, useRef, useState } from 'react';
import OtpInput from 'react-otp-input';
// import AccessTimeIcon from "@mui/icons-material/AccessTime";

import { LoginStyles } from '../login/login.style';
import Toaster from '../../components/common/Toaster';
import Button from '../../components/button/Button';
import { ROUTES } from '../../helper/constant';
import { postApi } from '../../helper/api/Api';
import { PASSWORD } from '../../helper/api/endPoint';

import ICON_DEMO from '../../assets/images/Logo.svg';
import TIMER_ICON from '../../assets/images/time-past-svgrepo-com.svg';
import personImage from '../../assets/images/personimage.jpg';
import { useLocation, useNavigate } from 'react-router-dom';
let interval;
const OtpVerify = (props) => {
	// const classes = LoginStyles();
	let timeInterval = useRef();

	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [otp, setOtp] = useState('');
	const [showTimer, setShowTimer] = useState();
	const [counter, setCounter] = useState(true);
	const [resendBtn, setResendBtn] = useState(true);
	const location = useLocation();
	const navigate = useNavigate();

	useEffect(() => {
		const checkOtpReqTime = location.state.data.data.new_otp_request_time;
		if (checkOtpReqTime) {
			startTimer(checkOtpReqTime);
		}
		//eslint-disable-next-line
	}, []);

	const startTimer = (duration) => {
		let minutes = duration;
		let seconds = duration;
		let timer = duration;

		interval = setInterval(() => {
			minutes = parseInt(timer / 60, 10);
			seconds = parseInt(timer % 60, 10);

			minutes = minutes < 10 ? '0' + minutes : minutes;
			seconds = seconds < 10 ? '0' + seconds : seconds;

			setShowTimer(minutes + ':' + seconds);
			if (--timer < 0) {
				timer = duration;
				clearInterval(interval);
				setResendBtn(false);
			}
		}, 1000);
	};

	const resendOTP = () => {
		let params = {};
		clearInterval(interval);
		if (location.state?.activeTab === 'EMAIL') {
			params = {
				...params,
				email: location.state?.email,
				type: 'EMAIL',
				action: 'FORGOT_PASSWORD'
			};
		} else {
			params = {
				...params,
				type: 'MOBILE',
				action: 'FORGOT_PASSWORD',
				country_code: '+91',
				mobile: location.state?.mobile
			};
		}

		postApi(PASSWORD.FORGOT_PASSWORD_OTP, params)
			.then((response) => {
				const count = response.data.data?.new_otp_request_time;
				const count_repeat_num = response.data.data?.requested_otp_count;
				if (response && response.data) {
					setCounter(count ? true : false);
					startTimer(Number(count));
					setResendBtn(true);
					if (count_repeat_num > 1) {
						toaster.current.success(response.data.message);
					}
				} else {
					startTimer(Number(count));
					setResendBtn(true);
					toaster.current.error(error?.response?.data?.message);
				}
			})
			.catch((error) => {
				startTimer(Number(error.response.data.data?.new_otp_request_time));
				setResendBtn(true);
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const handleOtp = (e) => {
		setOtp(e);
	};

	const handleVerification = () => {
		if (!otp) {
			toaster.current.error('Enter OTP');
			return;
		}
		setLoading(true);
		let params = {};
		if (location.state?.activeTab === 'EMAIL') {
			params = {
				...params,
				email: location.state?.email,
				type: 'EMAIL',
				otp: otp,
				action: 'FORGOT_PASSWORD'
			};
		} else {
			params = {
				...params,
				type: 'MOBILE',
				otp: otp,
				action: 'FORGOT_PASSWORD',
				country_code: '+91',
				mobile: location.state?.mobile
			};
		}

		postApi(PASSWORD.OTP_VERIFY, params)
			.then((response) => {
				if (response) {
					toaster.current.success(response.data.message);
					setTimeout(
						() =>
							navigate(ROUTES.PASSWORD.RESET, {
								state: location?.state
							}),
						[1000]
					);
				} else {
					toaster.current.error(response.message);
				}
			})
			.catch((error) => {
				setOtp('');
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	return (
		<>
			<LoginStyles {...props}>
				<div className="loginContainer">
					<div className="img_div">
						<img src={personImage} alt="person" className="img"></img>
					</div>

					<div className="form_container">
						<div className="form">
							<div className="loginIcon">
								<img src={ICON_DEMO} alt="icon" height="120px" width="240px" />
							</div>
							<div className="padding">
								<div className="form-group">
									<div className="center">
										<div className="font-weight-bold">Enter a OTP</div>
										<OtpInput
											value={otp}
											shouldAutoFocus={true}
											numInputs={6}
											inputType="number"
											isInputNum={true}
											className={otp}
											onChange={handleOtp}
											renderSeparator={<span>&nbsp;</span>}
											inputStyle={{
												border: '1px solid gray',
												borderRadius: '8px',
												width: '42px',
												height: '42px',
												fontSize: '12px',
												color: '#000',
												fontWeight: '400',
												caretColor: '#1cb4e3',
												marginTop: '15px'
											}}
											renderInput={(props) => <input {...props} />}
										/>
									</div>
								</div>
								<div className="center mt-10">
									{counter && (
										<div className="flex mt-10 mb-10">
											<img
												src={TIMER_ICON}
												alt="eye-icon"
												className="show-hide-icon"
											/>
											<p className="bold-p ml-5">{showTimer}</p>
										</div>
									)}

									<div className="flex">
										<p>Didn't recieve your code?</p>
										<button
											disabled={resendBtn}
											className={`${
												resendBtn ? 'otpBtn not-active' : 'otpBtn'
											}`}
											onClick={resendOTP}>
											Resend OTP
										</button>
									</div>
								</div>

								<div className="center">
									<Button
										loading={loading}
										className="btnText primaryFormButton"
										dataStyle="expand-right"
										style={{ position: 'relative' }}
										onClick={handleVerification}>
										Verify
									</Button>
								</div>
							</div>
							<Toaster ref={toaster} />
						</div>
					</div>
				</div>
			</LoginStyles>
		</>
	);
};

export default OtpVerify;
