/* eslint-disable react/display-name */
import React, { useState, useRef, useMemo, forwardRef, useEffect } from 'react';
import { Nav, NavItem, NavLink, Label, Input } from 'reactstrap';
import classNames from 'classnames';
import {
	ForgotTabs,
	ICON_DEMO,
	MAX_LENGTH,
	MIN_LENGTH,
	REGEX_VALIDATION,
	ROUTES
} from '../../helper/constant';
import Toaster from '../../components/common/Toaster';
import personImage from '../../assets/images/personimage.jpg';
import Button from '../../components/button/Button';
import { postApi } from '../../helper/api/Api';
import { PASSWORD } from '../../helper/api/endPoint';
import { LoginStyles } from '../login/login.style';
import Error from '../../components/common/Error';
import Loader from '../../components/common/Loader';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { useNavigate } from 'react-router-dom';
const ForgotPasswordLink = (props) => {
	const toaster = useRef();
	const navigate = useNavigate();
	const [loading, setLoading] = useState(false);
	const [activeTab, setActiveTab] = useState('EMAIL');

	const {
		handleChange,
		handleSubmit,
		values,
		errors,
		handleBlur,
		dirty,
		isValid,
		touched,
		setValues,
		setFieldValue
	} = useFormik({
		validationSchema: Yup.object().shape({
			mobile: Yup.string()
				.matches(REGEX_VALIDATION.MOBILE, 'Please enter valid Mobile number.')
				.min(MIN_LENGTH.MOBILE, 'Minimum length should be 10 digits.')
				.max(MAX_LENGTH.MOBILE, 'Maximum length should be 10 digits.')
				.when(activeTab, {
					is: 'MOBILE',
					then: Yup.string().required('Mobile number is required.')
				}),
			email: Yup.string()
				.email()
				.trim()
				.when(activeTab, {
					is: 'EMAIL',
					then: Yup.string().required('Email is required.')
				})
		}),
		initialValues: {
			email: '',
			mobile: ''
		},
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleForgotPassword();
		}
	});

	const toggleTab = (tab) => {
		if (activeTab !== tab) {
			setActiveTab(tab);
		}
	};

	const handleForgotPassword = () => {
		setLoading(true);
		let params = {};
		if (activeTab === 'EMAIL') {
			params = {
				...params,
				email: values?.email,
				type: 'EMAIL',
				action: 'FORGOT_PASSWORD'
			};
		} else {
			params = {
				...params,
				type: 'MOBILE',
				action: 'FORGOT_PASSWORD',
				country_code: '+91' || values?.countryCode,
				mobile: values?.mobile
			};
		}

		postApi(PASSWORD.FORGOT_PASSWORD_OTP, params)
			.then((response) => {
				if (response && response.data) {
					toaster.current.success(response.data.message);
					setTimeout(() => {
						navigate(ROUTES.PASSWORD.OTP_VERIFY, {
							state:
								activeTab === 'EMAIL'
									? {
											email: values.email,
											activeTab: 'EMAIL',
											data: response.data
									  }
									: {
											countryCode: '+91' || values?.countryCode,
											mobile: values.mobile,
											activeTab: 'MOBILE',
											data: response.data
									  }
						});
					}, 1000);
				} else {
					toaster.current.error(error?.response?.data?.message);
				}
			})
			.catch((error) => {
				toaster.current.error(error.response.data.message);
			})
			.finally(() => {
				setLoading(false);
			});
	};

	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	const EmailForm = () => {
		return (
			<>
				<Label>Enter Email</Label>
				<Input
					type="email"
					name="email"
					className="form-control form-control-lg react-form-input mb-20"
					placeholder="Enter email"
					// value={values.email}
					{...formAttributes('email')}
				/>
				<Error errors={errors} touched={touched} fieldName="email" />
			</>
		);
	};

	const MobileForm = () => {
		return (
			<>
				<Label>Enter Mobile</Label>
				<div className="flex mb-20">
					<div className="mr-20">
						<Input
							type="text"
							value="+91"
							className="form-control form-control-lg react-form-input"
							disabled
						/>
					</div>

					<div className="width100">
						<Input
							type="text"
							className="form-control form-control-lg react-form-input"
							placeholder="Enter mobile"
							name="mobile"
							{...formAttributes('mobile')}
						/>
						<Error errors={errors} touched={touched} fieldName="mobile" />
					</div>
				</div>
			</>
		);
	};

	return (
		<LoginStyles {...props}>
			<div className={'loginContainer'}>
				<Loader loading={loading} />
				<div className={'img_div'}>
					<img src={personImage} alt="person" className={'img'}></img>
				</div>

				<div className={'form_container'}>
					<div className={'form'}>
						<div className={'loginIcon'}>
							<img src={ICON_DEMO} alt="icon" height="120px" width="240px" />
						</div>

						<div className={'Title'}>Forgot Password?</div>
						<div className="text-center form-info-text mt-8">
							{`Provide your ${activeTab.toLocaleLowerCase()} to reset your password`}
						</div>
						<form>
							<div className="pa-24">
								<Nav pills className="ml-10 border-radius-30 overflow-hidden w-96">
									{ForgotTabs.map((items, index) => (
										<div key={index} style={{ width: '50%' }}>
											<NavItem
												className={`text-center cursor-pointer ${'tab'}`}
												key={index}>
												<NavLink
													className={
														activeTab === items.value ? 'active' : ''
													}
													onClick={() => {
														toggleTab(items.value);
														if (items?.value === 'EMAIL') {
															setFieldValue('mobile', '');
														} else {
															setFieldValue('email', '');
														}
													}}>
													{items.label}
												</NavLink>
											</NavItem>
										</div>
									))}
								</Nav>

								<div className="pa-24">
									<div className="form-group">
										<div
											className={classNames({
												'd-none': activeTab !== 'EMAIL'
											})}>
											<Label>Enter Email</Label>
											<Input
												type="email"
												name="email"
												className="form-control form-control-lg react-form-input mb-20"
												placeholder="Enter email"
												// value={values.email}
												{...formAttributes('email')}
											/>
											<Error
												errors={errors}
												touched={touched}
												fieldName="email"
											/>
										</div>
										<div
											className={classNames({
												'd-none': activeTab == 'EMAIL'
											})}>
											<Label>Enter Mobile</Label>
											<div className="flex mb-20">
												<div className="mr-20">
													<Input
														type="text"
														value="+91"
														className="form-control form-control-lg react-form-input"
														disabled
													/>
												</div>

												<div className="width100">
													<Input
														type="text"
														className="form-control form-control-lg react-form-input"
														placeholder="Enter mobile"
														name="mobile"
														{...formAttributes('mobile')}
													/>
													<Error
														errors={errors}
														touched={touched}
														fieldName="mobile"
													/>
												</div>
											</div>
										</div>
										{/* {activeTab === 'EMAIL' ? <EmailForm /> : <MobileForm />} */}
										<div className={'center'}>
											<Button
												loading={loading}
												className={classNames(
													'btnText',
													'primaryFormButton'
												)}
												dataStyle="expand-right"
												style={{ position: 'relative' }}
												disabled={!dirty || !isValid}
												onClick={() => handleSubmit()}>
												Send OTP
											</Button>
										</div>
									</div>

									<div className={'center'}>
										<Button
											className={classNames('btnText', 'primaryFormButton')}
											style={{ marginTop: '10px' }}
											onClick={() => navigate(ROUTES.LOGIN)}>
											Back
										</Button>
									</div>
								</div>

								<Toaster ref={toaster} />
							</div>
						</form>
					</div>
				</div>
			</div>
		</LoginStyles>
	);
};

export default ForgotPasswordLink;
