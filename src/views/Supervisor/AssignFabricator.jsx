import { useEffect, useState, useRef } from 'react';
import ReactTable from 'react-table';
import { useLocation } from 'react-router-dom';
// MODALS
import AssignFabricatorModal from './Modals/AssignFabricatorModal';
// COMPONENTS
import Button from 'src/components/button/Button';
import Loader from 'src/components/common/Loader';
import Toaster from 'src/components/common/Toaster';
import PageTitle from 'src/components/common/PageTitle';
import Pagination from 'src/components/Pagination/Pagination';
// HELPERS
import { TABLE } from 'src/helper/constant';
import { postApi } from 'src/helper/api/Api';
import { FABRICATOR } from 'src/helper/api/endPoint';
// STYLES
import { AssignFabricatorWrapper } from './Supervisor.style';

const AssignFabricator = () => {
	const toaster = useRef();
	const location = useLocation();

	const [fabricators, setFabricators] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);

	const [assignFabricatorModal, setAssignFabricatorModal] = useState({ open: false, data: null });

	useEffect(() => {
		fabricatorListApi();
	}, []); // eslint-disable-line

	const fabricatorListApi = async () => {
		try {
			setLoading(true);

			const response = await postApi(FABRICATOR.GET_USER, {
				page: 1,
				profile_status: [0, 1, 2],
				limit: 100
			});

			if (response.status === 200) {
				setFabricators(response.data?.data?.fabricators || []);
			}

			setLoading(false);
		} catch (error) {
			setLoading(false);

			toaster.current.error(error?.response?.data?.message);
		}
	};

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'Full Name',
			Cell: (cell) => (
				<span>
					{cell.original?.parent_user?.first_name} {cell.original?.parent_user?.last_name}
				</span>
			),
			headerClassName: 'text-left pa-20',
			className: 'text-left pa-20'
		},
		{
			Header: 'Email',
			Cell: (cell) => <span>{cell.original?.parent_user?.email}</span>,
			headerClassName: 'text-left pa-20',
			className: 'text-left pa-20'
		},
		{
			Header: 'Mobile',
			Cell: (cell) => <span>{cell.original?.parent_user?.mobile}</span>,
			headerClassName: 'text-left pa-20',
			className: 'text-left pa-20'
		}
	];

	const handleChangeAssignFabricatorModal = (data) => () =>
		setAssignFabricatorModal((prev) => ({ open: !prev.open, data }));

	return (
		<>
			<AssignFabricatorWrapper>
				<PageTitle title="sidebar.assignFabricator" />

				<div className="plr-0 pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="btn-container">
							<Button
								className="btn form-button assign-fabricator-btn"
								onClick={handleChangeAssignFabricatorModal()}>
								Assign Fabricator
							</Button>
						</div>

						<div className="roe-card-body">
							<ReactTable
								manual
								pages={Math.ceil(
									(location.state?.assigned_parent_user
										? location.state.assigned_parent_user.length
										: 0) / TABLE.LIMIT
								)}
								sortable={false}
								columns={columns}
								page={activePage}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								onPageChange={setActivePage}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								data={
									location.state.assigned_parent_user
										? location.state.assigned_parent_user
										: []
								}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</AssignFabricatorWrapper>

			<Toaster ref={toaster} />

			{
				<AssignFabricatorModal
					data={location.state}
					fabricators={fabricators}
					open={assignFabricatorModal.open}
					handleChangeAssignFabricatorModal={handleChangeAssignFabricatorModal}
				/>
			}
		</>
	);
};

export default AssignFabricator;
