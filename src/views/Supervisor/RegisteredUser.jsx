import { useEffect, useState, useRef } from 'react';
import ReactTable from 'react-table';
import ImageViewer from 'react-simple-image-viewer';
// MODALS
import ViewModal from './Modals/ViewModal';
import RejectModal from './Modals/RejectModal';
// COMPONENTS
import Loader from 'src/components/common/Loader';
import Toaster from 'src/components/common/Toaster';
import PageTitle from 'src/components/common/PageTitle';
import Pagination from 'src/components/Pagination/Pagination';
// HELPERS
import { TABLE } from 'src/helper/constant';
import { postApi } from 'src/helper/api/Api';
import useDebounce from 'src/util/hooks/useDebounce';
import { convertTimeToLocal } from 'src/helper/functions';
import { ROLES, SUPERVISOR } from 'src/helper/api/endPoint';
// STYLES
import SupervisorWrapper from './Supervisor.style';
// ASSETS
import ApproveIcon from 'src/assets/images/approve.svg';
import RejectIcon from 'src/assets/images/reject.svg';
import ViewIcon from 'src/assets/images/View.svg';
import CODES from 'src/helper/StatusCodes';
import Swal from 'sweetalert2';
import { CONFIRM_APPROVE_POPUP } from 'src/components/header/constants';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';

const ReactTableFixedColumns = withFixedColumns(ReactTable);
const RegisteredUser = () => {
	const toaster = useRef();

	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [searchKey, setSearchKey] = useState('');

	const [viewModal, setViewModal] = useState({ open: false, data: null });
	const [rejectModal, setRejectModal] = useState({ open: false, data: null });

	const debounceSearch = useDebounce(searchKey, 400);

	useEffect(() => {
		if (debounceSearch?.length > 2 || !debounceSearch) {
			getSupervisorListApi();
		}
	}, [activePage, debounceSearch]); // eslint-disable-line

	const getSupervisorListApi = async () => {
		try {
			setLoading(true);
			const obj = {
				page: activePage,
				profile_status: [2],
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				obj.page = 1;
				obj.search = searchKey;
			}
			const response = await postApi(SUPERVISOR.GET, obj);

			if (response.status === 200) {
				setData(response.data.data.supervisors);
				setPages(Math.ceil(response.data.data.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setLoading(false);
			setData([]);
			setPages(1);
			setCount(0);
			if (error?.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleApprovedUser = async (data) => {
		const params = {
			user_id: data.user_role.user_id,
			role_id: data.user_role.Role.id,
			role_name: data.user_role.Role.name,
			profile_status: 1,
			type_category: data.user_role.business_type_category
		};

		Swal.fire(CONFIRM_APPROVE_POPUP).then((result) => {
			if (result.isConfirmed) {
				postApi(ROLES.APPROVE_PROFILE, params)
					.then((response) => {
						if (response.data.status) {
							getSupervisorListApi();
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};

	const handleRejectUser = async (data) => {
		try {
			const response = await postApi(ROLES.APPROVE_PROFILE, data);

			if (response.data.status) {
				getSupervisorListApi();

				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
			if (error.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: ' User Type',
					width: 180,
					accessor: 'kyc_data.type.name',
					resizable: false,
					default: '-',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'City',
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Zone',
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Created On',
					resizable: false,
					maxWidth: 130,
					minWidth: 110,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					sticky: 'right',
					resizable: false,
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125,
					Cell: (cell) => (
						<div>
							<img
								src={ViewIcon}
								width={23}
								alt="ViewIcon"
								title="View"
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal(cell.original)}
							/>

							{cell.original?.kyc_data?.type?.name !== 'Existing Supervisor' && (
								<>
									<img
										src={ApproveIcon}
										alt="ApproveIcon"
										title="Approve"
										className="mr-10 cursor-pointer"
										width={22}
										onClick={() => handleApprovedUser(cell.original)}
									/>

									<img
										src={RejectIcon}
										alt="RejectIcon"
										title="Reject"
										className="cursor-pointer"
										width={20}
										onClick={handleChangeRejectModal(cell.original)}
									/>
								</>
							)}

							{/* <img src={PageIcon} className="icon ml-15 cursor-pointer" /> */}
						</div>
					)
				}
			]
		}
	];

	const handleChangeViewModal = (data) => () =>
		setViewModal((prev) => ({ open: !prev.open, data }));

	const handleChangeRejectModal = (data) => () =>
		setRejectModal((prev) => ({ open: !prev.open, data }));

	const handleChangeImageViewer = (open, images) => () => setImageViewer({ open, images });

	return (
		<>
			<SupervisorWrapper>
				<PageTitle
					title="sidebar.registerUser"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>

				<div className="plr-0 pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</SupervisorWrapper>

			<Toaster ref={toaster} />

			{viewModal.open && (
				<ViewModal
					open={viewModal.open}
					data={viewModal.data}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}

			{rejectModal.open && (
				<RejectModal
					open={rejectModal.open}
					data={rejectModal.data}
					handleChangeRejectModal={handleChangeRejectModal}
					handleRejectUser={handleRejectUser}
				/>
			)}

			{imageViewer.open && (
				<ImageViewer
					currentIndex={0}
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default RegisteredUser;
