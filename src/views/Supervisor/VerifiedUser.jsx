import { useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';
import { useNavigate } from 'react-router-dom';
import <PERSON><PERSON>iewer from 'react-simple-image-viewer';
// COMPONENTS
import Loader from 'src/components/common/Loader';
import PageTitle from 'src/components/common/PageTitle';
import Pagination from 'src/components/Pagination/Pagination';
import Toaster from 'src/components/common/Toaster';
import Switch from 'src/components/Switch/Switch';
// MODALS
import ViewModal from './Modals/ViewModal';
// STYLES
import SupervisorWrapper from './Supervisor.style';
// HELPERS
import { postApi } from 'src/helper/api/Api';
import useDebounce from 'src/util/hooks/useDebounce';
import { convertTimeToLocal } from 'src/helper/functions';
import { PROFILE_STATUS, TABLE } from 'src/helper/constant';
import { PROFILE, REDEEM, SUPERVISOR } from 'src/helper/api/endPoint';
// ASSETS
import ViewIcon from 'src/assets/images/View.svg';
import PayoutIcon from 'src/assets/images/Sidebar/redeem.svg';
import HistoryIcon from 'src/assets/images/Notify/history.svg';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import CODES from 'src/helper/StatusCodes';
import ViewOrEditModalPayout from './Modals/ViewOrEditModalPayout';
import Swal from 'sweetalert2';
import { CONFIRM_ACTIVATE_DELETED_USER } from 'src/components/header/constants';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const RegisteredUser = () => {
	const toaster = useRef();
	const navigate = useNavigate();

	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [searchKey, setSearchKey] = useState('');

	const [viewModal, setViewModal] = useState({ open: false, data: null });

	const [editModalPayout, setEditModalPayout] = useState({ open: false, data: null });

	const debounceSearch = useDebounce(searchKey, 400);

	useEffect(() => {
		if (debounceSearch?.length > 2 || !debounceSearch) {
			getSupervisorListApi();
		}
	}, [activePage, debounceSearch]); // eslint-disable-line

	const getSupervisorListApi = async () => {
		try {
			setLoading(true);
			const obj = {
				page: activePage,
				profile_status: [1],
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				obj.page = 1;
				obj.search = searchKey;
			}
			const response = await postApi(SUPERVISOR.GET, obj);

			if (response.status === CODES.SUCCESS) {
				setData(response.data.data.supervisors);
				setPages(Math.ceil(response.data.data.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setLoading(false);
			setData([]);
			setPages(1);
			setCount(0);
			if (error?.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			const user = data[index];

			// Check if user is being activated and is deleted
			if (!user.is_active && is_active && user.is_deleted) {
				Swal.fire(CONFIRM_ACTIVATE_DELETED_USER).then((result) => {
					if (result.isConfirmed) {
						performStatusUpdate(id, is_active, index);
					}
				});
				return;
			}

			// For all other cases, proceed normally
			performStatusUpdate(id, is_active, index);
		}
	};

	const performStatusUpdate = async (id, is_active, index) => {
		try {
			const response = await postApi(PROFILE.UPDATE_ACTIVE_INACTIVE, {
				user_id: id,
				status: is_active ? 1 : 0
			});

			if (response.status === CODES.SUCCESS) {
				setData((prev) => {
					const copyPrev = [...prev];

					copyPrev[index].is_active = is_active;

					return copyPrev;
				});

				toaster.current.success(response.data.message);
			}
		} catch (error) {
			if (error?.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleChangeViewModal = (data) => () =>
		setViewModal((prev) => ({ open: !prev.open, data }));

	const handleChangeEditModal = (data) => () =>
		setEditModalPayout((prev) => ({ open: !prev.open, data }));

	const updateManualPayout = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(REDEEM.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getSupervisorListApi();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleNavigateWalletLogs = (state) => () =>
		navigate('/supervisor/verifiedUser/wallet-logs', { state });

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 200,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'User Type',
					width: 200,
					accessor: 'kyc_data.type.name',
					resizable: false,
					default: '-',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Mobile Number',
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-'),
					width: 170
				},
				{
					Header: 'Wallet Points',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: (cell) => (
						<div>
							{parseInt(
								Number(
									cell.original.points_earnings.total_points
										? cell.original.points_earnings.total_points
										: 0
								) -
									Number(
										cell.original.points_earnings.redeemed_points
											? cell.original.points_earnings.redeemed_points
											: 0
									)
							)}
						</div>
					),
					width: 150
				},
				{
					Header: 'Wallet Balance (Rs.)',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: (cell) => (
						<div>
							{parseInt(
								Number(
									cell.original.points_earnings.total_earnings
										? cell.original.points_earnings.total_earnings
										: 0
								) -
									Number(
										cell.original.points_earnings.redeemed_earnings
											? cell.original.points_earnings.redeemed_earnings
											: 0
									)
							)}
						</div>
					),
					width: 200
				},
				{
					Header: 'City',
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-'),
					width: 200
				},
				{
					Header: 'Zone',
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Created On',
					resizable: false,
					width: 140,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					resizable: false,
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 150,
					width: 150,
					Cell: (cell) => (
						<>
							<img
								src={ViewIcon}
								width={23}
								alt="ViewIcon"
								title="View"
								className="mr-15 cursor-pointer"
								onClick={handleChangeViewModal(cell.original)}
							/>
							<img
								src={PayoutIcon}
								width={23}
								alt="Manual Payout"
								title="Manual Payout"
								className="mr-15 cursor-pointer"
								onClick={handleChangeEditModal(cell.original)}
							/>
							<img
								src={HistoryIcon}
								width={19}
								alt="HistoryIcon"
								title="Wallet History"
								className="mr-0 cursor-pointer"
								onClick={handleNavigateWalletLogs(cell.original)}
							/>
						</>
					)
				}
			]
		}
	];

	const handleChangeImageViewer = (open, images) => () => setImageViewer({ open, images });

	return (
		<>
			<SupervisorWrapper>
				<PageTitle
					title="sidebar.verifiedUser"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>

				<div className="plr-0 pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</SupervisorWrapper>

			<Toaster ref={toaster} />

			{viewModal.open && (
				<ViewModal
					open={viewModal.open}
					data={viewModal.data}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}

			{editModalPayout.open && (
				<ViewOrEditModalPayout
					open={editModalPayout.open}
					data={editModalPayout.data}
					updateManualPayout={updateManualPayout}
					handleChangeEditModal={handleChangeEditModal}
				/>
			)}

			{imageViewer.open && (
				<ImageViewer
					currentIndex={0}
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default RegisteredUser;
