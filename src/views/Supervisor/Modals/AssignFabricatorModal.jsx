import { useEffect, useRef, useState } from 'react';
import { Form, FormGroup, Label, ModalBody, ModalFooter, ModalHeader } from 'reactstrap';
// COMPONENTS
import Button from 'src/components/button/Button';
import Toaster from 'src/components/common/Toaster';
import Dropdown from 'src/components/Dropdown/Dropdown';
// STYLES
import { ViewModalWrapper } from '../Supervisor.style';
// HELPERS
import { postApi } from 'src/helper/api/Api';
import { FABRICATOR } from 'src/helper/api/endPoint';

const AssignFabricatorModal = (props) => {
	const toaster = useRef();

	const [fabricator, setFabricator] = useState(
		props.fabricators.map((value) => ({
			...value,
			name: `${value.first_name} ${value.last_name}`,
			selected: false
		}))
	);
	const [selectedFabricator, setSelectedFabricator] = useState([]);
	const [formErrors, setFormErrors] = useState({ fabricators: '' });
	const [saveLoading, setSaveLoading] = useState(false);

	useEffect(() => {
		setFabricator(
			props.fabricators.map((value) => ({
				...value,
				name: `${value.first_name} ${value.last_name}`,
				selected: false
			}))
		);
	}, [props.fabricators]);

	const handleCLoseModal = props.handleChangeAssignFabricatorModal({ open: false, data: null });

	const handleChangeFabricDropdown = (ids) => {
		setSelectedFabricator(ids);
		setFormErrors((prev) => ({ ...prev, fabricators: '' }));
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			let hasError = false;
			const newFormErrors = { fabricators: '' };

			if (!selectedFabricator.length) newFormErrors.fabricators = 'Fabricator is required!';

			setFormErrors(newFormErrors);

			Object.keys(newFormErrors).forEach((key) => {
				const element = newFormErrors[key];

				if (element) hasError = true;
			});

			if (!hasError) {
				try {
					setSaveLoading(true);

					const parent_user = fabricator.map((value) => {
						if (selectedFabricator.includes(value.id))
							return { user_id: value.id, role_id: value.user_role.role_id };
						return undefined;
					});

					const response = await postApi(FABRICATOR.ASSIGN_FABRICATOR, {
						parent_user,
						child_user_id: props.data.id,
						child_user_role_id: props.data.user_role.role_id
					});

					if (response.status === 200) {
						toaster.current.success(response.data.message);
					}

					setSaveLoading(false);
				} catch (error) {
					setSaveLoading(false);

					toaster.current.error(error?.response?.data?.message);
				}

				handleCLoseModal();
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	return (
		<>
			<ViewModalWrapper
				centered
				backdrop={'static'}
				isOpen={props.open}
				toggle={handleCLoseModal}
				style={{ width: '50%', maxWidth: '800px' }}>
				<ModalHeader toggle={handleCLoseModal}>Assign Fabricator</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody className="modal-body">
						<FormGroup>
							<Label for="exampleSelect">Select Fabricators</Label>
							<Dropdown
								data={fabricator}
								placeholder="Select Fabricators"
								handleChange={handleChangeFabricDropdown}
							/>
							<span className="error-msg">{formErrors.fabricators}</span>
						</FormGroup>
					</ModalBody>

					<ModalFooter>
						<div>
							<Button
								type="submit"
								loading={saveLoading}
								className="btn form-button c-secondary">
								Save
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</ViewModalWrapper>

			<Toaster ref={toaster} />
		</>
	);
};

export default AssignFabricatorModal;
