/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader } from 'reactstrap';
import Image<PERSON>iewer from 'react-simple-image-viewer';
// COMPONENTS
import Button from 'src/components/button/Button';
// HELPERS
import { KYC } from 'src/helper/api/endPoint';
import { getApi } from 'src/helper/api/Api';
import IntlMessages from 'src/util/intlMessages';
import { convertTimeToLocal, downloadFile, extractFileName } from 'src/helper/functions';
// STYLES
import { ViewModalWrapper } from '../Supervisor.style';
// IMAGES
import pdfIcon from '../../../assets/images/pdf.svg';
import { PROFILE_STATUS } from 'src/helper/constant';
import { Accordion, AccordionBody, AccordionHeader, AccordionItem } from 'reactstrap';

const ViewModal = (props) => {
	const panCardImages = (props.data.kyc_docs || [])
		.filter((doc) => doc.field === 'pan_card')
		.sort((a, b) => {
			if (a.type === 'front' && b.type !== 'front') {
				return -1; // "front" comes before other types
			} else if (a.type !== 'front' && b.type === 'front') {
				return 1; // "front" comes before other types
			} else {
				// If types are the same or both are not "front," maintain their original order
				return 0;
			}
		})
		.map((doc) => doc.file_url);

	const selfieImage = (props.data.kyc_docs || [])
		.filter((doc) => doc.field === 'selfie')
		.map((doc) => doc.file_url);

	const [fields, setFields] = useState([]);
	const [kycDocs, setKycDocs] = useState([]);
	const [currentImage, setCurrentImage] = useState(0);
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [openAccordion, setOpenAccordion] = useState('viewSupervisor_1');

	useEffect(() => {
		if (props.data?.kyc_data?.type?.name) {
			getFieldsBasedOnUserType(props.data?.kyc_data?.type?.name);
		}
	}, [props.data]);

	const getFieldsBasedOnUserType = async (type) => {
		const obj = {
			type: type
		};

		const response = await getApi(KYC.GET_KYC_FIELDS, { ...obj });
		if (response?.data?.data?.types?.length) {
			const all = response?.data?.data?.types[0];
			const docs = props?.data?.kyc_docs?.filter((item) => item.file_url.includes('.pdf'));
			setKycDocs(docs);
			setFields(all?.kyc_types);
		}
	};

	const handleChangeImageViewer = (open, images, imageIndex) => () => {
		setCurrentImage(imageIndex);
		setImageViewer({ open, images });
	};

	const handleCLoseModal = props.handleChangeViewModal({ open: false, data: null });

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	return (
		<>
			<ViewModalWrapper
				centered
				isOpen={props.open}
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Supervisor</ModalHeader>

				<ModalBody className="modal-body">
					<Accordion open={openAccordion} toggle={toggleAccordion}>
						<AccordionItem>
							<AccordionHeader targetId="viewSupervisor_1">
								User Details
							</AccordionHeader>
							<AccordionBody accordionId="viewSupervisor_1">
								{props.data.profile_image_url && (
									<div className="mb-10 d-flex flex-column">
										<strong className="mb-10">Profile image:</strong>{' '}
										<img
											className="profile-img-wrapper cursor-pointer"
											src={props.data.profile_image_url}
											alt={props.data.profile_image_key}
											onClick={handleChangeImageViewer(
												true,
												[props.data.profile_image_url],
												0
											)}
										/>
									</div>
								)}

								{props.data.first_name && props.data.last_name && (
									<div className="text-capitalize mb-10 mt-10">
										<strong>Name:</strong>{' '}
										{props.data.first_name + ' ' + props.data.last_name}
									</div>
								)}

								{props.data.email && (
									<div className="mb-10 mt-10">
										<strong>Email Id:</strong> {props.data.email}{' '}
										{props.data.is_email_verified &&
										props.data.is_email_verified ? (
											<span className="badge text-bg-primary fw-normal">
												Verified
											</span>
										) : (
											<span className="badge text-bg-danger fw-normal">
												Not Verified
											</span>
										)}
									</div>
								)}

								{props.data.mobile && (
									<div className="mb-10 mt-10">
										<strong>Mobile:</strong> {props.data.country_code}{' '}
										{props.data.mobile}{' '}
										{props.data.is_mobile_verified &&
										props.data.is_mobile_verified ? (
											<span className="badge text-bg-primary fw-normal">
												Verified
											</span>
										) : (
											<span className="badge text-bg-danger fw-normal">
												Not Verified
											</span>
										)}
									</div>
								)}

								{props.data.address?.[0]?.city && (
									<div className="mb-10 mt-10">
										<strong>City:</strong> {props.data.address[0].city}
									</div>
								)}

								{props.data.address?.[0]?.state && (
									<div className="mb-10 mt-10">
										<strong>State:</strong> {props.data.address[0].state}
									</div>
								)}

								{props.data.address?.[0]?.zone && (
									<div className="mb-10 mt-10">
										<strong>Zone:</strong> {props.data.address[0].zone.name}
									</div>
								)}

								{props.data.user_role?.Role?.name && (
									<div className="mb-10 mt-10">
										<strong>User Role:</strong> {props.data.user_role.Role.name}
									</div>
								)}

								{props.data.user_skills.length ? (
									<div className="mb-10 mt-10">
										<strong>User Skills:</strong>{' '}
										{props.data?.user_skills.map((items) => (
											<span
												className="badge text-bg-secondary mr-5 fw-normal"
												key={items.skills_data.id}>
												{items.skills_data.title}
											</span>
										))}
									</div>
								) : null}
								{props.data?.kyc_data?.type?.name === 'Existing Supervisor' && (
									<>
										{props?.data?.assigned_parent_user?.[0]?.parent_user && (
											<div className="text-capitalize mb-10 mt-10">
												<strong>Fabricator Name:</strong>{' '}
												{`${props?.data?.assigned_parent_user?.[0]?.parent_user?.first_name} ${props?.data?.assigned_parent_user?.[0]?.parent_user?.last_name}`}
											</div>
										)}
										{props?.data?.assigned_parent_user?.[0]?.parent_user && (
											<div className="text-capitalize mb-10 mt-10">
												<strong>Fabricator ID:</strong>{' '}
												{`${props?.data?.assigned_parent_user?.[0]?.parent_user?.id}`}
											</div>
										)}
										{props?.data?.kyc_data?.employee_code && (
											<div className="mb-10 mt-10">
												<strong>Supervisor Employee Code :</strong>{' '}
												{props?.data?.kyc_data?.employee_code}
											</div>
										)}
									</>
								)}

								{props.data?.requester_user_review_history?.[0] &&
								props?.data.profile_status !== PROFILE_STATUS.APPROVED ? (
									<div className="mb-10 mt-10">
										<strong>
											{props.profile_status === PROFILE_STATUS.PENDING &&
												'Last'}{' '}
											Reason:
										</strong>{' '}
										{props.data?.requester_user_review_history?.[0]?.reason
											? props.data?.requester_user_review_history?.[0]?.reason
											: props.data?.requester_user_review_history?.[0]
													?.profile_reject_reason?.reason}
									</div>
								) : (
									''
								)}
							</AccordionBody>
						</AccordionItem>

						{fields?.length ? (
							<AccordionItem>
								<AccordionHeader targetId="viewSupervisor_2">
									KYC Details
								</AccordionHeader>
								<AccordionBody accordionId="viewSupervisor_2">
									{fields.map((item) => {
										const key = item?.kyc_field?.key;
										const value = props.data.kyc_data[key];

										if (!value) return null;

										switch (key) {
											case 'date_of_birth':
												return (
													<div
														key={item?.kyc_field?.title}
														className="py-1">
														<strong>
															{item?.kyc_field?.title}
															{' :'}
														</strong>{' '}
														{convertTimeToLocal(value)}
													</div>
												);

											case 'employee_code':
												return null;

											default:
												return (
													<div
														key={item?.kyc_field?.title}
														className="py-1">
														<strong>
															{item?.kyc_field?.title}
															{' :'}
														</strong>{' '}
														{value}
													</div>
												);
										}
									})}
								</AccordionBody>
							</AccordionItem>
						) : (
							''
						)}

						<AccordionItem>
							<AccordionHeader targetId="viewSupervisor_3">
								Images & Documents Details
							</AccordionHeader>
							<AccordionBody accordionId="viewSupervisor_3">
								{panCardImages && panCardImages.length ? (
									<>
										<div className="container p-3">
											<strong>PAN Card</strong>
											<div className="kyc-doc-container">
												{panCardImages.map((kycDoc, index) => (
													<img
														onClick={handleChangeImageViewer(
															true,
															panCardImages,
															index
														)}
														className="kyc-doc cursor-pointer"
														key={kycDoc}
														src={kycDoc}
														alt={'kyc_' + index}
													/>
												))}
											</div>
										</div>
									</>
								) : null}

								{selfieImage && selfieImage.length ? (
									<>
										<hr />
										<div className="container p-3">
											<strong>Selfie</strong>
											<div className="kyc-doc-container">
												{selfieImage.map((kycDoc, index) => (
													<img
														onClick={handleChangeImageViewer(
															true,
															selfieImage,
															index
														)}
														className="kyc-doc cursor-pointer"
														key={kycDoc}
														src={kycDoc}
														alt=""
													/>
												))}
											</div>
										</div>
									</>
								) : null}

								<div>
									<hr />
									{kycDocs?.length
										? kycDocs.map((item) => (
												<div key={item?.field} className="">
													<strong>
														<IntlMessages id={item?.field} />
													</strong>
													<div
														className="d-flex py-2 gap-2 align-items-center cursor-pointer"
														onClick={() =>
															downloadFile(
																item.file_url,
																extractFileName(item.file_url)
															)
														}>
														<img
															src={pdfIcon}
															className="avatar avatar-lg"
														/>
														<div className="font-14 m-0">
															{extractFileName(item.file_url)}.pdf
														</div>
													</div>
													{/* {props.data.kyc_data[item?.kyc_field?.key]} */}
												</div>
										  ))
										: null}
								</div>
							</AccordionBody>
						</AccordionItem>
					</Accordion>
				</ModalBody>

				<ModalFooter>
					<div>
						<Button onClick={handleCLoseModal} className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</ViewModalWrapper>

			{imageViewer.open && (
				<ImageViewer
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					currentIndex={currentImage}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default ViewModal;
