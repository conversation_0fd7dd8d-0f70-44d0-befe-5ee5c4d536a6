/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AddOrEditModalWrapper } from 'src/views/AddressApprove/AddressApprove.style';

const ViewOrEditModalPayout = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);

	const handleCLoseModal = props.handleChangeEditModal({
		open: false,
		data: null
	});

	const validationSchema = Yup.object().shape({
		amount: Yup.number()
			.required('Amount is required!')
			.min(2, 'Amount must be more than 2!')
			.max(500, 'Amount must be less than 500!'),
		note: Yup.string()
	});

	const onSubmitHandler = async (values) => {
		try {
			const data = values;
			data.user_id = props.data.id;
			props.updateManualPayout(data);
			handleCLoseModal();
		} catch (error) {
			console.log(error);
		}
	};

	const formik = useFormik({
		initialValues: {
			amount: null,
			note: ''
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{/* {props.edit ? 'Edit' : 'Add'} Manual Payout */}
					Manual Payout
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								{/* <p className="note">
									Note: Please do not change any word which is enclosed in braces{' '}
									{'{}'}
								</p> */}
								<FormGroup className="mt-0">
									<Label>Amount (Rs)</Label>
									<Input
										id="amount"
										name="amount"
										value={formik.values.amount}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="number"
										placeholder="Type value..."
									/>
									{formik.touched.amount && formik.errors.amount && (
										<span className="error-msg mt-0">
											{formik.errors.amount}
										</span>
									)}
								</FormGroup>
								<FormGroup className="mt-15">
									<Label>Message</Label>
									<Input
										id="note"
										name="note"
										value={formik.values.note}
										onChange={formik.handleChange}
										onBlur={formik.handleBlur}
										type="textarea"
										rows={7}
										placeholder="Type value..."
									/>
									{formik.touched.note && formik.errors.note && (
										<span className="error-msg mt-0">{formik.errors.note}</span>
									)}
								</FormGroup>
								{/* <EmailEditor ref={emailEditorRef} onReady={onReady} /> */}
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button loading={saveLoading} type="submit" className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							{props.data && props.edit && (
								<Button
									onClick={handleCLoseModal}
									loading={saveLoading}
									type="submit"
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewOrEditModalPayout;
