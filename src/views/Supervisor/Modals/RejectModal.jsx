import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Dropdown,
	DropdownToggle,
	DropdownMenu,
	DropdownItem
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { ViewModalWrapper } from '../Supervisor.style';
import { Api, getApi } from 'src/helper/api/Api';
import { REASONS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import { PROFILE_STATUS, REASON_OTHER } from 'src/helper/constant';

const RejectModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({ reason: '', id: '', type: '' });
	const [formErrors, setFormErrors] = useState({ reason: '' });
	const [allReasons, setAllReasons] = useState([]);
	const [dropdownOpen, setDropdownOpen] = useState(false);

	const handleCLoseModal = props.handleChangeRejectModal(null);

	useEffect(() => {
		getReasons();
	}, [props.data]);

	const handleChange = ({ target }) => {
		const newReason = target.value;
		const reasonObj = structuredClone(formValues);
		reasonObj.reason = newReason;
		setFormValues(reasonObj);

		const errorMessage = validateReason(newReason);
		setFormErrors({ reason: errorMessage });
	};

	const validateReason = (reason) => {
		if (!reason.trim()) {
			return 'Reason is required!';
		}
		return ''; // No error
	};

	const getReasons = async () => {
		try {
			const response = await getApi(REASONS.GET_REASONS, {});
			if (response?.status === CODES.SUCCESS) {
				const data = response?.data?.data?.reasons;
				setAllReasons(data);
			}
		} catch (error) {
			setAllReasons([]);
		}
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessage = validateReason(formValues.reason);

			setFormErrors({ reason: errorMessage });

			if (!errorMessage) {
				handleCLoseModal();

				const obj = {
					user_id: props.data.user_role.user_id,
					role_id: props.data.user_role.Role.id,
					role_name: props.data.user_role.Role.name,
					profile_status: PROFILE_STATUS.REJECTED,
					reason_id: formValues.id
				};
				if (formValues.type === REASON_OTHER) {
					obj.reason = formValues.reason;
				}
				if (obj.role_name === 'Supervisor') {
					obj.type_category = props.data.user_role.business_type_category;
				}

				props.handleRejectUser(obj);
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const handleChangeReason = (value) => {
		const formValue = structuredClone(formValues);
		formValue.id = value.id;
		formValue.type = value.type;
		formValue.reason = value.type === REASON_OTHER ? '' : value.reason;
		setFormValues(formValue);
	};

	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	return (
		<ViewModalWrapper
			isOpen={props.open}
			centered
			toggle={handleCLoseModal}
			style={{ width: '100%', maxWidth: '500px' }}
			backdrop={'static'}>
			<ModalHeader toggle={handleCLoseModal}>Reject Supervisor</ModalHeader>

			<Form onSubmit={handleSubmit}>
				<ModalBody>
					<FormGroup>
						<Label>Reason</Label>
						<Dropdown
							className="mt-10 mb-4"
							isOpen={dropdownOpen}
							toggle={toggleRoleDropDown}>
							<DropdownToggle caret className="roleBtn text-truncate">
								{formValues.type === REASON_OTHER
									? 'Other'
									: formValues.reason || 'Select Reason'}
							</DropdownToggle>

							<DropdownMenu className="w100" style={{ maxWidth: '50%' }}>
								{allReasons.length ? (
									allReasons.map((role, index) => {
										return (
											<DropdownItem
												key={index}
												onClick={() => {
													handleChangeReason(role);
												}}
												className="text-truncate">
												{role.reason}
											</DropdownItem>
										);
									})
								) : (
									<DropdownItem disabled> No Data </DropdownItem>
								)}
							</DropdownMenu>
						</Dropdown>
						{formValues.type === REASON_OTHER && (
							<>
								<Input
									id="reason"
									value={formValues.reason}
									onChange={handleChange}
									type="textarea"
									placeholder="Type reject reason..."
								/>
							</>
						)}
						<span className="error-msg mt-10">{formErrors.reason}</span>
					</FormGroup>
				</ModalBody>

				<ModalFooter>
					<div>
						<Button loading={saveLoading} type="submit" className="btn form-button">
							Reject
						</Button>
					</div>

					<div>
						<Button
							onClick={handleCLoseModal}
							disabled={saveLoading}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</Form>
		</ViewModalWrapper>
	);
};

export default RejectModal;
