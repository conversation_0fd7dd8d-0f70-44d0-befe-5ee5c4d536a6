import { useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';
import { useLocation, useNavigate } from 'react-router-dom';
import <PERSON><PERSON>iewer from 'react-simple-image-viewer';
// COMPONENTS
import Loader from 'src/components/common/Loader';
import PageTitle from 'src/components/common/PageTitle';
import Pagination from 'src/components/Pagination/Pagination';
import Toaster from 'src/components/common/Toaster';
import Switch from 'src/components/Switch/Switch';
// MODALS
import ViewModal from './Modals/ViewModal';
// STYLES
import SupervisorWrapper from './Supervisor.style';
// HELPERS
import { getApi, postApi } from 'src/helper/api/Api';
import useDebounce from 'src/util/hooks/useDebounce';
import { convertTimeToLocal } from 'src/helper/functions';
import { PROFILE_STATUS, TABLE } from 'src/helper/constant';
import { PROFILE, REDEEM, SUPERVISOR } from 'src/helper/api/endPoint';
// ASSETS
import ViewIcon from 'src/assets/images/View.svg';
import PayoutIcon from 'src/assets/images/Sidebar/redeem.svg';
import HistoryIcon from 'src/assets/images/Notify/history.svg';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import CODES from 'src/helper/StatusCodes';
import ViewOrEditModalPayout from './Modals/ViewOrEditModalPayout';
import Button from 'src/components/button/Button';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const WalletLogs = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const { state } = useLocation();

	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');

	const debounceSearch = useDebounce(searchKey, 400);

	useEffect(() => {
		if (debounceSearch?.length > 2 || !debounceSearch) {
			getWalletLogs();
		}
	}, [activePage, debounceSearch]); // eslint-disable-line

	const getWalletLogs = async () => {
		try {
			setLoading(true);
			const obj = {
				user_id: state.id,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				obj.page = 1;
				obj.search = searchKey;
			}
			const response = await getApi(REDEEM.GET, obj);

			if (response.status === CODES.SUCCESS) {
				setData(response.data.data.redeemed_earnings_history);
				setPages(Math.ceil(response.data.data.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setLoading(false);
			setData([]);
			setPages(1);
			setCount(0);
			if (error?.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Redeem Amount (Rs)',
					width: 210,
					resizable: false,
					Cell: (row) => `${row.original.redeem_amount}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'Note',
					Cell: (row) => `${row.original.note}`,
					resizable: false,
					default: '-',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				},
				{
					Header: 'Created On',
					resizable: false,
					width: 130,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				}
			]
		}
	];

	return (
		<>
			<SupervisorWrapper>
				<PageTitle
					title="sidebar.walletLogs"
					search={false}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="d-flex justify-content-end">
					<Button
						onClick={() => navigate(-1)}
						type="submit"
						className="btn form-button"
						style={{ width: '80px' }}>
						Back
					</Button>
				</div>
				<div className="plr-0 pb-15 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</SupervisorWrapper>

			<Toaster ref={toaster} />
		</>
	);
};

export default WalletLogs;
