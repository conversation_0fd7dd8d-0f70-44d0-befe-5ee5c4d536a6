import styled from 'styled-components';
import { Modal } from 'reactstrap';

const SupervisorWrapper = styled.div`
	.card {
		min-height: calc(100vh - 260px);
	}
	.rt-thead, .rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups{
		display: none;
	}
	.-padRow.-even{
		display: none;
	}

	.icon {
		width: 25px;
		height: 25px;
	}
`;

export const ViewModalWrapper = styled(Modal)`
	.modal-body {
		gap: 15px;
		display: flex;
		flex-direction: column;

		.profile-img-wrapper {
			height: 150px;
			width: fit-content;
		}

		.detail {
			justify-content: space-between;
		}

		.full-name {
			margin-top: 30px;
		}

		.kyc-doc {
			width: 30%;
		}

		.container {
			padding: 20px;
			background-color: #f9f9f9;

			.kyc-doc-container {
				gap: 15px;
				display: flex;
				margin-top: 15px;
				align-items: center;

				.kyc-doc {
					object-fit: contain;
					width: auto;
					max-height: 250px;
					max-width: calc(50% - 8px);
				}
			}
		}

		.skills-chip-container {
			gap: 15px;
			display: flex;
			flex-wrap: wrap;
			margin-top: 15px;
			align-items: center;

			.skills-chip {
				padding: 5px 10px;
				border-radius: 10px;
				border: 0.5px solid #000;

				.close-icon {
					cursor: pointer;
					margin-left: 5px;
				}
			}
		}
		.roleBtn {
			display: flex;
			box-shadow: none !important;
			align-items: center;
			border-radius: 6px;
			width: 50%;
			background: #8dc968 !important;
			border: #8dc968 !important;
			height: calc(1.5em + 1rem);
			justify-content: space-between;
		}
	}
`;

export const AssignFabricatorWrapper = styled.div`
	.btn-container {
		display: flex;
		justify-content: flex-end;

		.assign-fabricator-btn {
			margin: 20px 0px;
			width: fit-content;
		}
	}
`;

export default SupervisorWrapper;
