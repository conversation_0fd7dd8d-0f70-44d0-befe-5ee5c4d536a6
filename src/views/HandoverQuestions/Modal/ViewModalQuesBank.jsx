import React from 'react';
import { useState } from 'react';
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>eader,
	Accordion,
	AccordionItem,
	AccordionHeader,
	AccordionBody
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../HandoverQuestions.style';

const ViewModalQuesBank = (props) => {
	const [openAccordion, setOpenAccordion] = useState('quesBank_1');

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Question Details</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<Accordion open={openAccordion} toggle={toggleAccordion}>
							<AccordionItem>
								<AccordionHeader targetId="quesBank_1">
									Question Details
								</AccordionHeader>
								<AccordionBody accordionId="quesBank_1">
									<div>
										{props?.data?.question_type && (
											<div className="mb-10 mt-10">
												<strong>Question Type: </strong>
												<span className="badge text-bg-primary fw-normal align-text-bottom ml-5">
													{props?.data?.question_type}
												</span>
											</div>
										)}
										<div className="mb-10 mt-10">
											<strong>Image Attachment: </strong>
											<span
												className={`badge fw-normal align-text-bottom ml-5 ${
													props?.data?.attachment_required
														? 'text-bg-success'
														: 'text-bg-danger'
												}`}>
												{props?.data?.attachment_required ? 'Yes' : 'No'}
											</span>
										</div>
										{props?.data?.comment_required?.required && (
											<div className="mb-10 mt-10">
												<strong>Comment Required: </strong>
												<span
													className={`badge fw-normal align-text-bottom ml-5 ${
														props?.data?.comment_required?.required
															? 'text-bg-success'
															: 'text-bg-danger'
													}`}>
													{props?.data?.comment_required?.required
														? 'Yes'
														: 'No'}
												</span>
											</div>
										)}
										<div className="mb-10 mt-10">
											<strong>Question: </strong>
											<span>{props?.data?.title}</span>
										</div>
										{props?.data?.handover_question_options?.length > 0 && (
											<div className="mb-10 mt-10">
												<strong>Options:</strong>
												<div className="d-flex justify-content-start flex-wrap align-items-center gap-2 mt-10">
													{props?.data?.handover_question_options.map(
														(items, index) => (
															<div className="card w-49">
																<div className="card-body">
																	<p>
																		<strong>
																			{String.fromCharCode(
																				65 + index
																			) + '. '}
																		</strong>
																		{items.title}
																	</p>
																</div>
															</div>
														)
													)}
												</div>
											</div>
										)}
										{props?.data?.question_type === 'Rating' && (
											<>
												<div className="mb-10 mt-10">
													<strong>Minimum Rating Value: </strong>
													<span>{props?.data?.min_number}</span>
												</div>
												<div className="mb-10 mt-10">
													<strong>Maximum Rating Value: </strong>
													<span>{props?.data?.max_number}</span>
												</div>
											</>
										)}
									</div>
								</AccordionBody>
							</AccordionItem>
						</Accordion>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalQuesBank;
