import React from 'react';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Modal<PERSON>ooter,
	FormGroup,
	Label,
	Input,
	Card,
	Button
} from 'reactstrap';

const ConditionsModal = ({ isOpen, toggle, type, conditions, setConditions, title }) => {
	const addCondition = () => {
		setConditions((prev) => ({
			...prev,
			conditions: [
				...prev.conditions,
				{ type: 'Equals', value: '' } // Default to Equals for additional conditions
			]
		}));
	};

	const updateCondition = (index, field, value) => {
		setConditions((prev) => {
			const newConditions = [...prev.conditions];
			newConditions[index] = { ...newConditions[index], [field]: value };
			return { ...prev, conditions: newConditions };
		});
	};

	const removeCondition = (index) => {
		setConditions((prev) => ({
			...prev,
			conditions: prev.conditions.filter((_, i) => i !== index)
		}));
	};

	return (
		<>
			<Modal isOpen={isO<PERSON>} toggle={toggle} className="modal-dialog-centered">
				<ModalHeader toggle={toggle}>Set Conditions for {title}</ModalHeader>
				<ModalBody>
					{conditions.conditions.map((condition, index) => (
						<Card key={index} className="mb-3 p-3">
							<div className="d-flex justify-content-between">
								<FormGroup className="flex-grow-1 mr-2">
									<Label>Type</Label>
									<Input type="select" value={condition.type} disabled>
										<option value="Equals">Equals</option>
									</Input>
								</FormGroup>
								<FormGroup className="flex-grow-1 ml-2">
									<Label>Value</Label>
									<Input
										type={
											['Greater than', 'Less than'].includes(condition.type)
												? 'number'
												: 'text'
										}
										value={condition.value}
										onChange={(e) =>
											updateCondition(index, 'value', e.target.value)
										}
										placeholder="Enter value"
									/>
								</FormGroup>

								{/* Show remove button if there's more than one condition OR if it's not an Always condition */}
								{(conditions.conditions.length > 1 ||
									condition.type !== 'Always') && (
									<Button
										className="btn-danger ml-2 align-self-end"
										onClick={() => removeCondition(index)}>
										Remove
									</Button>
								)}
							</div>
						</Card>
					))}
					<Button className="btn btn-primary mt-2" onClick={addCondition}>
						Add Condition
					</Button>
				</ModalBody>
				<ModalFooter>
					<Button onClick={toggle}>Done</Button>
				</ModalFooter>
			</Modal>
		</>
	);
};

export default ConditionsModal;
