import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from 'reactstrap';
import Button from '../../../components/button/Button';

const AlertModal = ({ isOpen, toggle, onConfirm, title, message }) => {
	return (
		<Modal isOpen={isOpen} toggle={toggle} className="modal-dialog-centered">
			<ModalHeader toggle={toggle}>{title}</ModalHeader>
			<ModalBody>{message}</ModalBody>
			<ModalFooter>
				<Button className="btn form-button" onClick={onConfirm}>
					OK
				</Button>
				<Button className="btn form-button c-secondary" onClick={toggle}>
					Cancel
				</Button>
			</ModalFooter>
		</Modal>
	);
};

export default AlertModal;
