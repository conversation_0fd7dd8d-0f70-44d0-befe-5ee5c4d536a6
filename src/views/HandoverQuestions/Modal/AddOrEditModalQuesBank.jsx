/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Dropdown
} from 'reactstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../HandoverQuestions.style';
import RejectIcon from '../../../assets/images/reject.svg';
import ConditionsModal from './ConditionsModal';

const AddOrEditModalQuestionBank = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [selectedQuesType, setSelectedQuesType] = useState('');
	const [mcqFields, setMCQFields] = useState([1, 2]);
	const [showCommentConditions, setShowCommentConditions] = useState(false);
	const [commentConditions, setCommentConditions] = useState({
		conditions: [
			{
				type: 'Equals',
				value: ''
			}
		]
	});

	const maxCharacterCountQues = 200;
	const countMaxMCQValue = 4;

	const dynamicMCQInitialValues = mcqFields.reduce((acc, field) => {
		acc[`mcq_${field}`] = '';
		return acc;
	}, {});

	const mcqObject = Object.fromEntries(Array.from({ length: 2 }, (_, i) => [`mcq_${i + 1}`, '']));
	console.log('mcqObject', mcqObject);

	const initialValues = {
		order: null,
		ques_name: '',
		...mcqObject,
		attach_img: true,
		is_comment_required: false,
		min_number: null,
		max_number: null
	};

	const commonValidationSchema = Yup.object().shape({
		order: Yup.number()
			.positive('Question order should be greater than 0')
			.integer('Question order should be integer')
			.required(`Question order is required`),
		ques_name: Yup.string()
			.required('Question name is required')
			.max(maxCharacterCountQues, 'Question name is too long'),
		attach_img: Yup.boolean(),
		is_comment_required: Yup.boolean()
	});

	const validationSchema = Yup.lazy((values) => {
		if (selectedQuesType === 'Single' || selectedQuesType === 'Multiple') {
			const fieldValidations = {
				...commonValidationSchema.fields
			};

			Object.keys(dynamicMCQInitialValues).forEach((field) => {
				fieldValidations[field] = Yup.string().required(
					`MCQ ${field.split('_')[1]} is required`
				);
			});
			const dynamicMCQObj = Yup.object().shape(fieldValidations);

			return Yup.object().shape({
				...dynamicMCQObj.fields,
				...commonValidationSchema.fields
			});
		}
		if (selectedQuesType === 'Rating') {
			const ratingValidationSchema = Yup.object().shape({
				min_number: Yup.number()
					.positive('Minimum rating number should be greater than 0')
					.integer('Minimum rating number should be integer')
					.required(`Minimum rating number is required`),
				max_number: Yup.number()
					.positive('Maximum rating number should be greater than 0')
					.integer('Maximum rating number should be integer')
					.required(`Maximum rating number is required`)
			});
			return Yup.object().shape({
				...commonValidationSchema.fields,
				...ratingValidationSchema.fields
			});
		}
		return commonValidationSchema;
	});

	const formik = useFormik({
		initialValues,
		validationSchema,
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleQuesBank();
		}
	});

	const addMCQField = () => {
		const nextField = mcqFields.length + 1;
		formik.setFieldValue(`mcq_${nextField}`, '');
		setMCQFields([...mcqFields, nextField]);
	};

	const removeMCQField = (field) => {
		setMCQFields(mcqFields.slice(0, -1));
		formik.setFieldValue(`mcq_${field}`, '');
	};

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const characterCount = formik.values.ques_name.length;

	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const handleQuesBank = () => {
		try {
			formik.validateForm(formik.values).then((errors) => {
				if (Object.keys(errors).length === 0) {
					setSaveLoading(true);
					let submissionData = {
						order: formik.values.order,
						title: formik.values.ques_name,
						attachment_required: formik.values.attach_img ? 1 : 0,
						comment_required: {
							required: formik.values.is_comment_required,
							...(formik.values.is_comment_required
								? {
									conditions: commentConditions.conditions
								}
								: {})
						},
						que_type: selectedQuesType
					};

					if (selectedQuesType === 'Single' || selectedQuesType === 'Multiple') {
						const mcqValuesArray = Object.keys(formik.values)
							.filter((key) => key.startsWith('mcq_') && formik.values[key])
							.map((key) => formik.values[key]);
						submissionData.options = mcqValuesArray;
					}
					if (selectedQuesType === 'Rating') {
						submissionData.min_number = formik.values.min_number;
						submissionData.max_number = formik.values.max_number;
					}
					props.addQuestions(submissionData);
					handleCLoseModal();
				}
			});
		} catch (error) {
			console.error('Error in handleQuesBank:', error);
			setSaveLoading(false);
		}
	};

	const handleChangeQuestionsType = (value) => {
		setSelectedQuesType(value.title);
	};

	const renderConditionModals = () => (
		<>
			{(selectedQuesType === 'Single' || selectedQuesType === 'Multiple') &&
				showCommentConditions && (
					<ConditionsModal
						isOpen={showCommentConditions}
						toggle={() => setShowCommentConditions(false)}
						type="comment"
						conditions={commentConditions}
						setConditions={setCommentConditions}
						title="Comment"
					/>
				)}
		</>
	);
	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>Add Handover Question</ModalHeader>
				<ModalBody>
					<FormGroup>
						<Label>Select Question types</Label>
						<Dropdown
							className={`mt-0 mb-10 bg-blue`}
							isOpen={dropdownOpen}
							toggle={toggleRoleDropDown}>
							<DropdownToggle caret className="roleBtn text-truncate">
								{selectedQuesType || 'Select'}
							</DropdownToggle>

							<DropdownMenu className="w-100" style={{ maxWidth: '50%' }}>
								{props.questionType.length ? (
									props.questionType.map((item, index) => {
										return (
											<DropdownItem
												key={index}
												onClick={() => {
													handleChangeQuestionsType(item);
												}}
												className="text-truncate">
												{item.title}
											</DropdownItem>
										);
									})
								) : (
									<DropdownItem disabled> No Data </DropdownItem>
								)}
							</DropdownMenu>
						</Dropdown>
					</FormGroup>
					<Form>
						{selectedQuesType && (
							<>
								<FormGroup>
									<Label>Question Order</Label>
									<Input
										id="order"
										name="order"
										value={formik.values.order}
										onChange={formik.handleChange}
										type="number"
										placeholder="Enter question order..."
									/>
									{formik.touched.order && formik.errors.order && (
										<span className="error-msg my-2">
											{formik.errors.order}
										</span>
									)}
								</FormGroup>
								<FormGroup>
									<Label>Question Name</Label>
									<>
										<Input
											id="ques_name"
											name="ques_name"
											value={formik.values.ques_name}
											onChange={formik.handleChange}
											type="textarea"
											placeholder="Your Question..."
										/>
									</>
									<div
										className={`font-14 mt-5 ${characterCount > maxCharacterCountQues
												? 'text-danger'
												: ''
											}`}>
										Total characters you can add {characterCount}/
										{maxCharacterCountQues}
									</div>
									{formik.touched.ques_name && formik.errors.ques_name && (
										<span className="error-msg my-2">
											{formik.errors.ques_name}
										</span>
									)}
								</FormGroup>
							</>
						)}
						{selectedQuesType.length > 0 &&
							(selectedQuesType === 'Single' || selectedQuesType === 'Multiple') ? (
							<>
								<div className="d-flex justify-content-between align-items-center mb-10">
									<p>
										<strong>MCQs</strong>
									</p>
									<Button
										className="btn form-button w-auto"
										type="button"
										onClick={addMCQField}>
										Add MCQ
									</Button>
								</div>
								{mcqFields.map((field, index) => (
									<div key={field}>
										<FormGroup>
											<div className="d-flex align-items-center w-100">
												<Label className="mr-30 ml-10 mt-7">
													{String.fromCharCode(64 + field)}
												</Label>
												<div className="d-flex flex-row align-items-center w-100">
													<Input
														id={`mcq_${field}`}
														name={`mcq_${field}`}
														value={formik.values[`mcq_${field}`]}
														onChange={formik.handleChange}
														onBlur={formik.handleBlur}
														type="text"
														placeholder={`MCQ Options ${field}`}
														className="mt-10 mb-10"
													/>
													{index >= 2 && (
														<img
															src={RejectIcon}
															onClick={() => removeMCQField(field)}
															alt="cross-icon"
															title="Delete MCQ type"
															className="ml-10 cursor-pointer"
															width="20px"
														/>
													)}
												</div>
											</div>
											{formik.touched[`mcq_${field}`] &&
												formik.errors[`mcq_${field}`] && (
													<span className="error-msg my-0 ml-55">
														{formik.errors[`mcq_${field}`]}
													</span>
												)}
										</FormGroup>
									</div>
								))}
							</>
						) : null}
						{selectedQuesType === 'Rating' && (
							<>
								<FormGroup>
									<Label>Minimum Rating Number</Label>
									<Input
										id="min_number"
										name="min_number"
										value={formik.values.min_number}
										onChange={formik.handleChange}
										type="number"
										placeholder="Enter minimum rating number..."
									/>
									{formik.touched.min_number && formik.errors.min_number && (
										<span className="error-msg my-2">
											{formik.errors.min_number}
										</span>
									)}
								</FormGroup>
								<FormGroup>
									<Label>Maximum Rating Number</Label>
									<Input
										id="max_number"
										name="max_number"
										value={formik.values.max_number}
										onChange={formik.handleChange}
										type="number"
										placeholder="Enter maximum rating number..."
									/>
									{formik.touched.max_number && formik.errors.max_number && (
										<span className="error-msg my-2">
											{formik.errors.max_number}
										</span>
									)}
								</FormGroup>
							</>
						)}
						{selectedQuesType && (
							<>
								<FormGroup
									className="mt-10 d-flex align-items-center justify-content-between"
									check>
									<Label for="attach_img" check>
										<Input
											id="attach_img"
											name="attach_img"
											checked={formik.values.attach_img}
											onChange={() =>
												formik.setFieldValue(
													'attach_img',
													!formik.values.attach_img
												)
											}
											type="checkbox"
										/>{' '}
										Attach Image
									</Label>
								</FormGroup>
							</>
						)}
						{(selectedQuesType === 'Single' || selectedQuesType === 'Multiple') && (
							<FormGroup
								className="mt-10 d-flex align-items-center justify-content-between"
								check>
								<Label for="is_comment_required" check>
									<Input
										id="is_comment_required"
										name="is_comment_required"
										checked={formik.values.is_comment_required}
										onChange={() =>
											formik.setFieldValue(
												'is_comment_required',
												!formik.values.is_comment_required
											)
										}
										type="checkbox"
									/>{' '}
									Is comment required
								</Label>
								{formik.values.is_comment_required && (
									<Button
										className="btn btn-sm btn-outline-primary"
										onClick={() => setShowCommentConditions(true)}>
										Set Conditions
									</Button>
								)}
							</FormGroup>
						)}
					</Form>
				</ModalBody>
				<ModalFooter>
					<div>
						<Button
							type="submit"
							className="btn form-button"
							onClick={formik.handleSubmit}
							loading={saveLoading}
							disabled={formik.isSubmitting || selectedQuesType.length === 0}>
							Save
						</Button>
					</div>
					<div>
						<Button
							onClick={handleCLoseModal}
							loading={formik.isSubmitting}
							type="submit"
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
				{selectedQuesType && <>{renderConditionModals()}</>}
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalQuestionBank;
