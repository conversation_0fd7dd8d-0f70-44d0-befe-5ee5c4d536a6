import styled from 'styled-components';
import { Modal } from 'reactstrap';

const QuestionWrapper = styled.div`
	.rejected {
		color: #000;
		padding: 5px;
		text-align: center;
		border-radius: 10px;
		background-color: red;
		max-width: 100px;
	}

	.approved {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: green;
		max-width: 100px;
	}

	.pending {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: yellow;
		max-width: 100px;
	}
	.maxWidth {
		max-width: 115px;
	}

	.icon {
		width: 18px;
		height: 18px;
	}

	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups {
		display: none;
	}
	.-padRow.-even {
		display: none;
	}
	.last-column {
		display: flex;
		justify-content: flex-end;
		gap: 5px;
		align-items: center;
	}
	.question-overflow {
		max-height: 250px;
		overflow: auto;
	}
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
	.add-btn {
		background: #1cb4e3;
		padding: 5px 10px;
		border-radius: 10px;
		width: fit-content;
		color: #fff;
		cursor: pointer;
	}
	.profile-img-wrapper {
		height: 150px;
		width: fit-content;
	}
	.kyc-doc-container {
		gap: 15px;
		display: flex;
		margin-top: 5px;
		align-items: center;

		.kyc-doc {
			object-fit: contain;
			max-width: calc(50% - 8px);
		}
	}
`;

export default QuestionWrapper;
