/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import QuestionWrapper from './HandoverQuestions.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE, allQuestionsTypes } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import ViewIcon from 'src/assets/images/View.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { HANDOVER_QUESTION } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';
import AddOrEditModalQuestionBank from './Modal/AddOrEditModalQuesBank';
import ViewModalQuesBank from './Modal/ViewModalQuesBank';

const ReactTableFixedColumns = withFixedColumns(ReactTable);
const questionType = [
	{ title: 'Single', id: 1 },
	{ title: 'Multiple', id: 2 },
	{ title: 'Descriptive', id: 3 },
	{ title: 'Boolean', id: 4 },
	{ title: 'Rating', id: 5 }
];

const HandoverQuestions = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [selectedQuesType, setSelectedQuesType] = useState(undefined);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [activeTab, setActiveTab] = useState(1);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true,
		edit: false
	});
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const toggle = (id, title) => {
		if (activeTab !== id) {
			getQuestionBank(title);
			setActivePage(1);
			setActiveTab(id);
		}
	};

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Question',
					resizable: false,
					Cell: (row) => `${row.original.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 100
				}
			]
		}
	];

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(
					HANDOVER_QUESTION.CHANGE_STATUS,
					{
						id: id,
						status: is_active ? 1 : 0
					},
					'order'
				);

				if (response.status === 200) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		setActiveTab(1);
		getQuestionBank(questionType[0]?.title);
	}, []);

	useEffect(() => {
		if (selectedQuesType) {
			getQuestionBank(selectedQuesType);
		}
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);
	const handleChangeViewModal = (params) => () => setViewModalData(params);

	const getQuestionBank = async (type) => {
		try {
			setLoading(true);
			setSelectedQuesType(type);
			const dataToSend = {
				que_type: type,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(
				HANDOVER_QUESTION.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.questions);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addQuestions = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(HANDOVER_QUESTION.ADD, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getQuestionBank(selectedQuesType);
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<QuestionWrapper>
				<PageTitle
					title="sidebar.handoverQuestions"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={handleChangeViewOrEditModal({
								open: true,
								view: false,
								edit: false,
								data: null
							})}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mt-8 mb-15">
						<div className="roe-card-body">
							<Loader loading={!questionType.length > 0} />
							<Nav tabs className="mb-10">
								{questionType.map((role) => (
									<NavItem key={role.id} className="cursor-pointer">
										<NavLink
											className={`${activeTab === role.id ? 'active' : ''}`}
											onClick={() => toggle(role.id, role.title)}>
											{role.title}
										</NavLink>
									</NavItem>
								))}
							</Nav>
							<TabContent activeTab={activeTab}>
								{questionType.map((role) => (
									<TabPane key={role.id} tabId={role.id}>
										<ReactTableFixedColumns
											manual
											data={data}
											pages={pages}
											sortable={false}
											columns={columns}
											page={activePage - 1}
											onPageChange={handleChangePage}
											totalCount={count}
											loading={loading}
											pageSize={TABLE.LIMIT}
											minRows={TABLE.MIN_ROW}
											LoadingComponent={Loader}
											PaginationComponent={Pagination}
											style={{ border: 'none', boxShadow: 'none' }}
											className="-striped -highlight custom-react-table-theme-class"
											defaultFilterMethod={(filter, row) => {
												const id = filter.pivotId || filter.id;
												return row[id] !== undefined
													? String(row[id].toLowerCase()).includes(
															filter.value.toLowerCase()
													  )
													: true;
											}}
										/>
									</TabPane>
								))}
							</TabContent>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</QuestionWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModalQuestionBank
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					questionType={questionType}
					addQuestions={addQuestions}
				/>
			)}
			{viewModalData.open && (
				<ViewModalQuesBank
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}
		</>
	);
};

export default HandoverQuestions;
