/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import WorkOrderTypeWrapper from './WorkOrderType.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import EditIcon from 'src/assets/images/Edit.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { SKILLS, SPACES, WORK_ORDER_TYPE } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import AddOrEditModalWorkOrderType from './Modal/AddOrEditModal';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const WorkOrderType = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [skills, setSkills] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Work Order type',
					resizable: false,
					Cell: (row) => `${row.original.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 450
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={EditIcon}
								alt="EditIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 80
				}
			]
		}
	];

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(
					WORK_ORDER_TYPE.CHANGE_STATUS,
					{
						id: id,
						status: is_active ? 1 : 0
					},
					'order'
				);

				if (response.status === 200) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		getSkills();
	}, []);

	useEffect(() => {
		getWorkOrderType();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getSkills = async () => {
		try {
			const response = await getApi(SKILLS.GET, {
				role_id: 8, // Supervisor Only skills
				status: 'Active',
				page: activePage,
				limit: TABLE.LIMIT
			});

			if (response.status) {
				setSkills(response?.data?.data?.skills);
			}
		} catch (error) {
			setSkills([]);
		}
	};

	const getWorkOrderType = async () => {
		try {
			setLoading(true);

			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}
			const response = await getApi(
				WORK_ORDER_TYPE.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.work_order_types);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
		} finally {
			setLoading(false);
		}
	};

	const addWorkOrderType = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(WORK_ORDER_TYPE.ADD, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrderType();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const editWorkOrderType = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(WORK_ORDER_TYPE.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrderType();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<WorkOrderTypeWrapper>
				<PageTitle
					title="sidebar.workOrderType"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={handleChangeViewOrEditModal({
								open: true,
								view: false,
								edit: false,
								data: null
							})}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</WorkOrderTypeWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModalWorkOrderType
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					skills={skills}
					getWorkOrderType={getWorkOrderType}
					editWorkOrderType={editWorkOrderType}
					addWorkOrderType={addWorkOrderType}
				/>
			)}
		</>
	);
};

export default WorkOrderType;
