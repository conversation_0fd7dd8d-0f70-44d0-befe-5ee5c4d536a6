/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../WorkOrderType.style';
import { regexCharactersNumbers } from 'src/helper/constant';
import Dropdown from 'src/components/Dropdown/Dropdown';

const AddOrEditModalWorkOrderType = (props) => {
	const [skills, setSkills] = useState(
		props.skills.map((value) => ({
			selected: false,
			id: value.skill_id,
			name: value.skill,
			is_active: value.is_active,
			is_deleted: value.is_deleted
		}))
	);
	const [selectedSkills, setSelectedSkills] = useState([]);
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		title: ''
	});
	const [formErrors, setFormErrors] = useState({ title: '', skills: '' });
	const [dropdownTouched, setDropdownTouched] = useState(false);

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	useEffect(() => {
		if (props.data) {
			setFormValues(props.data);
		}
	}, [props.data]);

	useEffect(() => {
		setSkills(
			props.skills.map((value) => ({
				selected: false,
				id: value.skill_id,
				name: value.skill,
				is_active: value.is_active,
				is_deleted: value.is_deleted
			}))
		);
	}, [props.skills]);

	const handleChange = ({ target }) => {
		const newTitle = target.value;
		setFormValues((prevFormValues) => ({
			...prevFormValues,
			title: newTitle
		}));

		// Validate input on change and set error message
		const errorMessage = validateWorkOrderType({ title: newTitle });
		setFormErrors((prevFormValues) => ({ ...prevFormValues, title: errorMessage }));
	};

	const validateWorkOrderType = (value) => {
		const isValid = regexCharactersNumbers.test(value?.title);
		if (!value?.title.trim()) {
			return 'Work order type is required!';
		}
		if (!isValid) {
			return 'Title can only contain letters and spaces.';
		}
		return ''; // No error
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			setDropdownTouched(true);
			const errorMessage = validateWorkOrderType(formValues);
			setFormErrors({ title: errorMessage });

			if (!props.edit) {
				if (selectedSkills.length === 0) {
					setFormErrors((prevFormErrors) => ({
						...prevFormErrors,
						skills: 'Please select at least one skill.'
					}));
				} else {
					setFormErrors((prevFormErrors) => ({
						...prevFormErrors,
						skills: ''
					}));
				}
			}

			if (!errorMessage && (props.edit || selectedSkills.length > 0)) {
				if (props.edit) {
					props.editWorkOrderType({
						id: props.data.id,
						title: formValues.title
					});
				} else {
					props.addWorkOrderType({
						title: formValues.title,
						skill_ids: selectedSkills
					});
				}

				handleCLoseModal();
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const handleChangeRoleDropdown = (ids) => {
		setSelectedSkills(ids);

		const errorMessage = validateSkills(ids);
		setFormErrors((prevFormErrors) => ({
			...prevFormErrors,
			skills: errorMessage
		}));
	};

	const validateSkills = (ids) => {
		if (dropdownTouched && ids.length === 0) {
			return 'Please select at least one skill.';
		}
		return ''; // No error
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Work Order Type
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Work Order Type</Label>
									<>
										<Input
											id="title"
											value={formValues?.title}
											onChange={handleChange}
											type="text"
											placeholder="Type value..."
										/>
									</>
									<span className="error-msg mt-10">{formErrors.title}</span>
								</FormGroup>
								{!props.edit ? (
									<FormGroup>
										<Label>Skills</Label>
										<>
											<Dropdown
												data={skills}
												placeholder="Select Skills"
												handleChange={handleChangeRoleDropdown}
											/>
										</>
										<span className="error-msg mt-10">{formErrors.skills}</span>
									</FormGroup>
								) : (
									<>
										<FormGroup>
											<Label>Assigned Skills</Label>
											<div className="d-flex gap-2 flex-wrap">
												{props.data.skills.map((items) => (
													<p className="skills-chip" key={items.id}>
														{items.title}
													</p>
												))}{' '}
											</div>
										</FormGroup>
									</>
								)}
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button
								// onClick={handleCLoseModal}
								disabled={saveLoading}
								type="submit"
								className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							{props.data && props.edit && (
								<Button
									onClick={handleCLoseModal}
									loading={saveLoading}
									type="submit"
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalWorkOrderType;
