import { useNavigate } from 'react-router-dom';
import { PROFILE_LOCK_SCREEN, ROUTES } from '../../helper/constant';

const LockScreen = () => {
	const navigate = useNavigate();

	return (
		<div className='container-fluid authFormContainer'>
			<div className='form-container'>
				<div className='login-icon lock-screen-profile'>
					<img src={require(PROFILE_LOCK_SCREEN)} alt='icon' height='100px' />
				</div>
				<div className='login-title'>Alice Blue</div>
				<div className='text-center form-info-text plr-24 mt-16'>
					Your session is locked due to no activity. Enter your password to
					continue.
				</div>
				<form className='pa-24' onSubmit={e => e.preventDefault()}>
					<div className='form-group'>
						<input
							type='password'
							className='form-control react-form-input'
							id='exampleInputPassword1'
							placeholder='Password'
						/>
					</div>
					<button type='submit' className='btn form-button'>
						Login
					</button>
					<div
						className='text-center link-label'
						onClick={() => navigate(ROUTES.LOGIN)}
					>
						Use Another Account ?
					</div>
				</form>
			</div>
		</div>
	);
};

export default LockScreen;
