//CORE
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';

//CUSTOM
import { InternalTeamWrapper } from './InternalTeamManagement.style';
import PageTitle from '../../components/common/PageTitle';
import { Api, postApi } from '../../helper/api/Api';
import { ROLES, USER } from '../../helper/api/endPoint';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import CODES from '../../helper/StatusCodes';
import Loader from '../../components/common/Loader';
import Pagination from '../../components/Pagination/Pagination';
import Button from 'src/components/button/Button';
import AddOrEditModal from './Modals/AddOrEditModal';
import Toaster from 'src/components/common/Toaster';
import ViewIcon from '../../assets/images/View.svg';
import ApproveIcon from '../../assets/images/approve.svg';
import RejectIcon from '../../assets/images/reject.svg';
import useDebounce from 'src/util/hooks/useDebounce';
import Swal from 'sweetalert2';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
import { CONFIRM_APPROVE_POPUP } from 'src/components/header/constants';
import { convertTimeToLocal } from 'src/helper/functions';

// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const InternalTeamManagementRegistered = () => {
	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [internalData, setInternalData] = useState([]);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });

	const getInternalManagementData = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				profile_status: [PROFILE_STATUS.PENDING],
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}

			const response = await postApi(USER.GET_USER_BY_ROLE, {
				...dataToSend
			});

			if (response.status === CODES.SUCCESS) {
				setInternalData(response?.data?.data?.internal_team);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setInternalData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		}
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleApprovedUser = (data) => {
		let params = {
			user_id: data.user_role.user_id,
			role_id: data.user_role.Role.id,
			role_name: data.user_role.Role.name,
			profile_status: PROFILE_STATUS.APPROVED
		};
		Swal.fire(CONFIRM_APPROVE_POPUP).then((result) => {
			if (result.isConfirmed) {
				postApi(ROLES.APPROVE_PROFILE, { ...params })
					.then((response) => {
						if (response.data.status) {
							getInternalManagementData();
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};
	const handleRejectUser = async (data) => {
		try {
			const response = await postApi(ROLES.APPROVE_PROFILE, { ...data });
			if (response.data.status) {
				getInternalManagementData();
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	useEffect(() => {
		getInternalManagementData();
	}, [debounceSearch, activePage]);

	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'Role',
					minWidth: 170,
					accessor: 'user_role.Role.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'City',
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Zone',
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Created On',
					resizable: false,
					maxWidth: 130,
					minWidth: 110,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: true,
									profile_status: PROFILE_STATUS.PENDING
								})}
							/>
							<img
								src={ApproveIcon}
								alt="ApproveIcon"
								title="Approve"
								className="mr-10 cursor-pointer"
								width={22}
								onClick={() => handleApprovedUser(cell.original)}
							/>

							<img
								src={RejectIcon}
								alt="RejectIcon"
								title="Reject"
								className="cursor-pointer"
								width={20}
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: false
								})}
							/>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];

	return (
		<InternalTeamWrapper>
			<PageTitle
				title="sidebar.registerUser"
				className="plr-0"
				search={true}
				searchKey={searchKey}
				setSearchKey={setSearchKey}
			/>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-body">
						<ReactTableFixedColumns
							manual
							sortable={false}
							data={internalData}
							pages={pages}
							columns={columns}
							page={activePage - 1}
							onPageChange={handleChangePage}
							totalCount={count}
							loading={loading}
							pageSize={TABLE.LIMIT}
							minRows={TABLE.MIN_ROW}
							LoadingComponent={Loader}
							PaginationComponent={Pagination}
							style={{ border: 'none', boxShadow: 'none' }}
							className="-striped -highlight custom-react-table-theme-class"
						/>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
					handleRejectUser={handleRejectUser}
				/>
			)}
		</InternalTeamWrapper>
	);
};

export default InternalTeamManagementRegistered;
