//CORE
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';

//CUSTOM
import { InternalTeamWrapper } from './InternalTeamManagement.style';
import PageTitle from '../../components/common/PageTitle';
import { Api, postApi } from '../../helper/api/Api';
import { PROFILE, USER } from '../../helper/api/endPoint';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import CODES from '../../helper/StatusCodes';
import Loader from '../../components/common/Loader';
import Pagination from '../../components/Pagination/Pagination';
import ViewIcon from '../../assets/images/View.svg';
import AddOrEditModal from './Modals/AddOrEditModal';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch';
import Toaster from 'src/components/common/Toaster';

import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';

// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import { convertTimeToLocal } from 'src/helper/functions';
import Swal from 'sweetalert2';
import { CONFIRM_ACTIVATE_DELETED_USER } from 'src/components/header/constants';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const InternalTeamManagementApproved = () => {
	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [internalData, setInternalData] = useState([]);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });

	const getInternalManagementData = async () => {
		try {
			setLoading(true);

			const dataToSend = {
				profile_status: [PROFILE_STATUS.APPROVED],
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}

			const response = await postApi(USER.GET_USER_BY_ROLE, {
				...dataToSend
			});

			if (response.status === CODES.SUCCESS) {
				setInternalData(response?.data?.data?.internal_team);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setInternalData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		}
	};

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = internalData.findIndex((value) => value.id === id);

		if (index !== -1) {
			const user = internalData[index];

			// Check if user is being activated and is deleted
			if (!user.is_active && is_active && user.is_deleted) {
				Swal.fire(CONFIRM_ACTIVATE_DELETED_USER).then((result) => {
					if (result.isConfirmed) {
						performStatusUpdate(id, is_active, index);
					}
				});
				return;
			}

			// For all other cases, proceed normally
			performStatusUpdate(id, is_active, index);
		}
	};

	const performStatusUpdate = async (id, is_active, index) => {
		try {
			setLoading(true);
			const response = await postApi(PROFILE.UPDATE_ACTIVE_INACTIVE, {
				user_id: id,
				status: is_active ? 1 : 0
			});

			if (response.status === 200) {
				setInternalData((prev) => {
					const copyPrev = [...prev];

					copyPrev[index].is_active = is_active;

					return copyPrev;
				});

				toaster.current.success(response.data.message);
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.profile_image_url
										? cell?.original?.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={!cell?.original?.profile_image_url && 'No profile image'}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) => `${row.original.first_name} ${row.original.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'Role',
					minWidth: 170,
					accessor: 'user_role.Role.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'City',
					accessor: 'address[0].city',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Zone',
					accessor: 'address[0].zone.name',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				},
				{
					Header: 'Created On',
					resizable: false,
					maxWidth: 130,
					minWidth: 110,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-center pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				},
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-left pa-20 d-flex align-items-center'
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: true,
									profile_status: PROFILE_STATUS.APPROVED
								})}
							/>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];

	useEffect(() => {
		getInternalManagementData();
	}, [debounceSearch, activePage]);

	return (
		<InternalTeamWrapper>
			<PageTitle
				title="sidebar.verifiedUser"
				className="plr-0"
				search={true}
				searchKey={searchKey}
				setSearchKey={setSearchKey}
			/>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-body">
						<ReactTableFixedColumns
							manual
							sortable={false}
							data={internalData}
							pages={pages}
							columns={columns}
							page={activePage - 1}
							onPageChange={handleChangePage}
							totalCount={count}
							loading={loading}
							pageSize={TABLE.LIMIT}
							minRows={TABLE.MIN_ROW}
							LoadingComponent={Loader}
							PaginationComponent={Pagination}
							style={{ border: 'none', boxShadow: 'none' }}
							className="-striped -highlight custom-react-table-theme-class"
						/>
					</div>
				</div>
			</div>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
				/>
			)}
			<Toaster ref={toaster} />
		</InternalTeamWrapper>
	);
};

export default InternalTeamManagementApproved;
