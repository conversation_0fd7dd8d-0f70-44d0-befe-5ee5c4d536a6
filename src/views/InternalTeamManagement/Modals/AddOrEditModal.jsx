/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	Modal<PERSON>ody,
	Modal<PERSON>ooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	InputGroupText,
	InputGroup,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Dropdown
} from 'reactstrap';
import { Accordion, AccordionBody, AccordionHeader, AccordionItem } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../InternalTeamManagement.style';
import { PROFILE_STATUS, REASON_OTHER } from 'src/helper/constant';
import { Api, getApi } from 'src/helper/api/Api';
import { REASONS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import ImageViewer from 'react-simple-image-viewer';
const AddOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({ reason: '', id: '', type: '' });
	const [formErrors, setFormErrors] = useState({ reason: '' });

	const handleCLoseModal = props.handleChangeAddOrEditModal({ open: false, data: null });
	const [allReasons, setAllReasons] = useState([]);
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const [openAccordion, setOpenAccordion] = useState('user_1');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const getReasons = async () => {
		try {
			const response = await getApi(REASONS.GET_REASONS, {});
			if (response?.status === CODES.SUCCESS) {
				const data = response?.data?.data?.reasons;
				setAllReasons(data);
			}
		} catch (error) {
			setAllReasons([]);
		}
	};

	useEffect(() => {
		if (!props.view) {
			getReasons();
		}
	}, [props.data]);
	const handleChange = ({ target }) => {
		const newReason = target.value;
		const reasonObj = structuredClone(formValues);
		reasonObj.reason = newReason;
		setFormValues(reasonObj);

		const errorMessage = validateReason(newReason);
		setFormErrors({ reason: errorMessage });
	};
	const handleChangeReason = (value) => {
		const formValue = structuredClone(formValues);
		formValue.id = value.id;
		formValue.type = value.type;
		formValue.reason = value.type === REASON_OTHER ? '' : value.reason;
		setFormValues(formValue);
	};

	const validateReason = (reason) => {
		if (!reason.trim()) {
			return 'Reason is required!';
		}
		return ''; // No error
	};

	const handleChangeImageViewer = (open, images, imageIndex) => () => {
		setImageViewer({ open, images });
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessage = validateReason(formValues.reason);
			setFormErrors({ reason: errorMessage });

			if (!errorMessage) {
				handleCLoseModal();

				const obj = {
					user_id: props.data.user_role.user_id,
					role_id: props.data.user_role.Role.id,
					role_name: props.data.user_role.Role.name,
					profile_status: PROFILE_STATUS.REJECTED,
					reason_id: formValues.id
				};
				if (formValues.type === REASON_OTHER) {
					obj.reason = formValues.reason;
				}

				props.handleRejectUser(obj);
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.view ? 'View' : 'Reject'} User Team Managemenet
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{props.data && props.view && (
							<>
								<Accordion open={openAccordion} toggle={toggleAccordion}>
									<AccordionItem>
										<AccordionHeader targetId="user_1">
											User Details
										</AccordionHeader>
										<AccordionBody accordionId="user_1">
											{props.data.profile_image_url && (
												<div className="mb-10 d-flex flex-column">
													<strong className="mb-10">
														Profile image:
													</strong>{' '}
													<img
														className="profile-img-wrapper cursor-pointer"
														src={props.data.profile_image_url}
														alt={props.data.profile_image_key}
														onClick={handleChangeImageViewer(
															true,
															[props.data.profile_image_url],
															0
														)}
													/>
												</div>
											)}
											{props.data.first_name && props.data.last_name && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Name:</strong>{' '}
													{props.data.first_name +
														' ' +
														props.data.last_name}
												</div>
											)}
											{props.data.email && (
												<div className="mb-10 mt-10">
													<strong>Email:</strong> {props.data.email}{' '}
													{props.data.is_email_verified &&
													props.data.is_email_verified ? (
														<span className="badge text-bg-primary fw-normal">
															Verified
														</span>
													) : (
														<span className="badge text-bg-danger fw-normal">
															Not Verified
														</span>
													)}
												</div>
											)}

											{props.data.mobile && (
												<div className="mb-10 mt-10">
													<strong>Mobile:</strong>{' '}
													{props.data?.country_code} {props.data.mobile}{' '}
													{props.data.is_mobile_verified &&
													props.data.is_mobile_verified ? (
														<span className="badge text-bg-primary fw-normal">
															Verified
														</span>
													) : (
														<span className="badge text-bg-danger fw-normal">
															Not Verified
														</span>
													)}
												</div>
											)}

											{props.data.address[0].city && (
												<div className="mb-10 mt-10">
													<strong>City:</strong>{' '}
													{props.data.address[0].city}
												</div>
											)}
											{props.data.address[0].state && (
												<div className="mb-10 mt-10">
													<strong>State:</strong>{' '}
													{props.data.address[0].state}
												</div>
											)}
											{props.data.address?.[0]?.zone && (
												<div>
													<strong>Zone:</strong>{' '}
													{props.data.address[0].zone.name}
												</div>
											)}
											{props.data.user_role.Role.name && (
												<div className="mb-10 mt-10">
													<strong>User Role:</strong>{' '}
													{props.data.user_role.Role.name}
												</div>
											)}
											{props.data.user_skills.length ? (
												<div className="mb-10 mt-10">
													<strong>User Skills:</strong>{' '}
													{props.data?.user_skills.map((items) => (
														<span
															className="badge text-bg-secondary mr-5 fw-normal"
															key={items.skills_data.id}>
															{items.skills_data.title}
														</span>
													))}
												</div>
											) : null}
											{props.data?.requester_user_review_history?.[0] &&
												props.profile_status !==
													PROFILE_STATUS.APPROVED && (
													<div className="mb-10 mt-10">
														<strong>
															{props.profile_status ===
																PROFILE_STATUS.PENDING &&
																'Last'}{' '}
															Reason:
														</strong>{' '}
														{props.data
															?.requester_user_review_history?.[0]
															?.reason
															? props.data
																	?.requester_user_review_history?.[0]
																	?.reason
															: props.data
																	?.requester_user_review_history?.[0]
																	?.profile_reject_reason?.reason}
													</div>
												)}
										</AccordionBody>
									</AccordionItem>
								</Accordion>
							</>
						)}

						{props.data && !props.view && (
							<>
								<FormGroup>
									<Label>Reason</Label>
									<Dropdown
										className="mt-10 mb-4"
										isOpen={dropdownOpen}
										toggle={toggleRoleDropDown}>
										<DropdownToggle caret className="roleBtn text-truncate">
											{formValues.type === REASON_OTHER
												? 'Other'
												: formValues.reason || 'Select Reason'}
										</DropdownToggle>

										<DropdownMenu className="w100" style={{ maxWidth: '50%' }}>
											{allReasons.length ? (
												allReasons.map((role, index) => {
													return (
														<DropdownItem
															key={index}
															onClick={() => {
																handleChangeReason(role);
															}}
															className="text-truncate">
															{role.reason}
														</DropdownItem>
													);
												})
											) : (
												<DropdownItem disabled> No Data </DropdownItem>
											)}
										</DropdownMenu>
									</Dropdown>
									{formValues.type === REASON_OTHER && (
										<>
											<Input
												id="reason"
												value={formValues.reason}
												onChange={handleChange}
												type="textarea"
												placeholder="Type reject reason..."
											/>
										</>
									)}
									<span className="error-msg mt-10">{formErrors.reason}</span>
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							{props.data && !props.view && (
								<Button
									loading={saveLoading}
									type="submit"
									className="btn form-button">
									Reject
								</Button>
							)}
						</div>

						<div>
							<Button
								onClick={handleCLoseModal}
								disabled={saveLoading}
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
			{imageViewer.open && (
				<ImageViewer
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default AddOrEditModal;
