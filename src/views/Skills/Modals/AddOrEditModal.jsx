import { useEffect, useState } from 'react';
import {
	Input,
	<PERSON>dal<PERSON><PERSON>,
	<PERSON>dal<PERSON>ooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	InputGroupText,
	InputGroup
} from 'reactstrap';
import Button from '../../../components/button/Button';
import Dropdown from '../../../components/Dropdown/Dropdown';
import { AddOrEditModalWrapper } from '../Skills.style';

const AddOrEditModal = (props) => {
	const [roles, setRoles] = useState(props.roles.map((value) => ({ ...value, selected: false })));
	const [skillValue, setSkillValue] = useState('');
	const [skills, setSkills] = useState([]);
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		skills: [],
		default: props.data?.default || false,
		skill: props.data?.skill || ''
	});
	const [formErrors, setForm<PERSON>rrors] = useState({ skills: '', roles: '', skill: '' });
	const [selectedRoles, setSelectedRoles] = useState([]);
	const [selectedSkills, setSelectedSkills] = useState([]);

	useEffect(() => {
		setRoles(props.roles.map((value) => ({ ...value, selected: false })));
	}, [props.roles]);

	const handleCLoseModal = props.handleChangeAddOrEditModal({ open: false, data: null });

	const handleChangeRoleDropdown = (ids) => {
		setSelectedRoles(ids);
		setFormErrors((prev) => ({ ...prev, skills: '' }));
	};

	const handleSkillAddPress = () => {
		if (skillValue) {
			setSkills((prev) => {
				const copyPrev = [...prev];

				const found = copyPrev.find((value) => value.name === skillValue);

				if (!found) copyPrev.push({ id: skillValue, name: skillValue });

				return copyPrev;
			});
			setSkillValue('');
		}
	};

	const handleDeleteSkill = (skill) => () =>
		setSkills((prev) => {
			const copyPrev = [...prev];

			const filteredSkills = copyPrev.filter((value) => value.name !== skill);

			return filteredSkills;
		});

	const handleChangeSkillValue = ({ target }) => setSkillValue(target.value);

	const handleChangeSkillDropdown = (ids) => setSelectedSkills(ids);

	const handleSkillInput = ({ target }) =>
		setFormValues((prev) => ({ ...prev, skill: target.value }));

	const handleDefaultInput = ({ target }) =>
		setFormValues((prev) => ({ ...prev, default: target.checked }));

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			let hasError = false;
			const newFormErrors = props.data ? { skill: '' } : { roles: '', skills: '' };

			if (props.data) {
				if (!formValues.skill) newFormErrors.skill = 'Skill is required!';
			} else {
				if (!selectedRoles.length) newFormErrors.roles = 'Roles is required!';
				if (!skills.length) newFormErrors.skills = 'Skills is required!';
			}

			setFormErrors(newFormErrors);

			Object.keys(newFormErrors).forEach((key) => {
				const element = newFormErrors[key];

				if (element) hasError = true;
			});

			if (!hasError) {
				handleCLoseModal();

				if (props.data && props.data.id)
					props.handleEditSkill({
						id: props.data.id,
						skill: formValues.skill,
						default: formValues.default ? 1 : 0
					});
				else
					props.handleAddSkill({
						role_ids: selectedRoles,
						skills: skills.map((skill) => ({
							name: skill.name,
							default: selectedSkills.includes(skill.name) ? 1 : 0
						}))
					});
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	return (
		<AddOrEditModalWrapper
			isOpen={props.open}
			centered
			toggle={handleCLoseModal}
			style={{ width: '50%', maxWidth: '800px' }}
			backdrop={'static'}>
			<ModalHeader toggle={handleCLoseModal}>{props.data ? 'Edit' : 'Add'} Skill</ModalHeader>

			<Form onSubmit={handleSubmit}>
				<ModalBody>
					{props.data && (
						<>
							<FormGroup>
								<Label for="skill">Skill</Label>
								<Input
									id="skill"
									value={formValues.skill}
									onChange={handleSkillInput}
									type="text"
									placeholder="Enter Skill"
								/>
								<span className="error-msg">{formErrors.skill}</span>
							</FormGroup>

							<FormGroup check>
								<Label for="default" check>
									<Input
										id="default"
										checked={formValues.default}
										onChange={handleDefaultInput}
										type="checkbox"
									/>{' '}
									Default
								</Label>
							</FormGroup>
						</>
					)}

					{!props.data && (
						<>
							<FormGroup>
								<Label>Select Roles</Label>
								<Dropdown
									data={roles}
									placeholder="Select Roles"
									handleChange={handleChangeRoleDropdown}
								/>
								<span className="error-msg mt-10">{formErrors.roles}</span>
							</FormGroup>

							<FormGroup>
								<Label for="skills">Enter Skills</Label>
								<InputGroup>
									<Input
										value={skillValue}
										onChange={handleChangeSkillValue}
										type="text"
										placeholder="Type Skill"
									/>
									<InputGroupText
										className="cursor-pointer"
										onClick={handleSkillAddPress}>
										Add
									</InputGroupText>
								</InputGroup>

								<div className="skills-chip-container">
									{skills.map((skill) => (
										<span key={skill.name} className="skills-chip">
											{skill.name}{' '}
											<span
												aria-hidden="true"
												className="close-icon"
												onClick={handleDeleteSkill(skill.name)}>
												×
											</span>
										</span>
									))}
								</div>
								<span className="error-msg mt-10">{formErrors.skills}</span>
							</FormGroup>

							<FormGroup>
								<Label>Default Skills</Label>
								<Dropdown
									data={skills}
									placeholder="Select Default Skills"
									handleChange={handleChangeSkillDropdown}
								/>
							</FormGroup>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button loading={saveLoading} type="submit" className="btn form-button">
							{props.data ? 'Edit' : 'Add'}
						</Button>
					</div>

					<div>
						<Button
							onClick={handleCLoseModal}
							disabled={saveLoading}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</Form>
		</AddOrEditModalWrapper>
	);
};

export default AddOrEditModal;
