import { useEffect, useRef, useState } from 'react';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';
import ReactTable from 'react-table';
import SkillsWrapper from './Skills.style';
import { TABLE } from '../../helper/constant';
import AddOrEditModal from './Modals/AddOrEditModal';
import { getApi, postApi } from '../../helper/api/Api';
import { ROLES, SKILLS } from '../../helper/api/endPoint';
import Pagination from '../../components/Pagination/Pagination';
import Loader from '../../components/common/Loader';
import PageTitle from '../../components/common/PageTitle';
import Toaster from '../../components/common/Toaster';
import Button from '../../components/button/Button';
import Switch from '../../components/Switch/Switch';
// ASSETS
import EditIcon from 'src/assets/images/Edit.svg';
import CODES from 'src/helper/StatusCodes';

const Skills = () => {
	const toaster = useRef();

	const [activeTab, setActiveTab] = useState(undefined);
	const [activePage, setActivePage] = useState(1);
	const [count, setCount] = useState(0);
	const [loading, setLoading] = useState(true);
	const [roles, setRoles] = useState([]);
	const [skills, setSkills] = useState([]);
	const [totalSkills, setTotalSkills] = useState(1);
	const [roleId, setRoleId] = useState(undefined);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });

	useEffect(() => {
		getRolesApi();
	}, []); // eslint-disable-line

	useEffect(() => {
		if (roleId) getSkills(roleId);
	}, [activePage]); // eslint-disable-line

	const getRolesApi = () => {
		getApi(ROLES.GET, { scope: 'APP', type: 'CHILD' }).then((response) => {
			if (response.status === CODES.SUCCESS) {
				const apiData = response?.data?.data.roles;

				if (apiData?.length) {
					setRoles(apiData);
					setActiveTab(apiData?.[0]?.id);
					getSkills(apiData?.[0]?.id);
				}
			} else toaster.current.error(error?.response?.data?.message);
		});
	};

	const getSkills = async (role_id) => {
		try {
			setLoading(true);
			setRoleId(role_id);
			const response = await getApi(SKILLS.GET, {
				role_id,
				page: activePage,
				limit: TABLE.LIMIT
			});

			if (response.status === CODES.SUCCESS) {
				setSkills(response?.data?.data?.skills);
				setTotalSkills(Math.ceil(response?.data?.data?.count / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}

			setLoading(false);
		} catch (error) {
			setTotalSkills(1);
			setCount(0);
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const handleChangeActiveInactive = async (id, status) => {
		const index = skills.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setSkills((prev) => {
					const copyPrev = [...prev];

					copyPrev[index].loading = true;

					return copyPrev;
				});

				const response = await postApi(SKILLS.UPDATE_ACTIVE_INACTIVE, {
					id,
					status: status ? 1 : 0
				});

				if (response.status === 200) {
					setSkills((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].loading = false;
						copyPrev[index].is_active = status;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				setSkills((prev) => {
					const copyPrev = [...prev];

					copyPrev[index].loading = false;

					return copyPrev;
				});

				toaster.current.error(error?.response?.data?.message);
			}
		}
	};

	const handleEditSkill = async (data) => {
		try {
			const response = await postApi(SKILLS.UPDATE, { role_id: roleId, ...data });

			if (response.data.status) {
				setSkills((prev) => {
					const copyPrev = [...prev];

					const index = copyPrev.findIndex((value) => value.id === data.id);

					copyPrev[index].skill = data.skill;
					copyPrev[index].default = data.default ? true : false;

					return copyPrev;
				});

				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
		}
	};

	const handleAddSkill = async (data) => {
		try {
			const response = await postApi(SKILLS.ADD, { ...data });

			if (response.data.status) {
				getSkills(roleId);
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
		}
	};

	/** @type {Array<import("react-table").Column> | undefined} */
	const columns = [
		{
			Header: 'Skill',
			accessor: 'skill',
			headerClassName: 'text-left pa-20',
			className: 'text-left pa-20'
		},
		{
			Header: 'Active',
			Cell: (cell) => (
				<Switch
					checked={cell.original.is_active}
					onChange={() =>
						handleChangeActiveInactive(cell.original.id, !cell.original.is_active)
					}
				/>
			),
			headerClassName: 'text-right pa-20',
			className: 'text-right pa-20'
		},
		{
			Header: 'Action',
			Cell: (cell) => (
				<img
					src={EditIcon}
					alt="Edit"
					title="Edit"
					width={23}
					className="cursor-pointer"
					onClick={handleChangeAddOrEditModal({ open: true, data: cell.original })}
				/>
			),
			headerClassName: 'text-right pa-20',
			className: 'text-right pa-20'
		}
	];

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const toggle = (tab) => {
		if (activeTab !== tab) {
			getSkills(tab);
			setActivePage(1);
			setActiveTab(tab);
		}
	};

	return (
		<>
			<SkillsWrapper>
				<PageTitle title="sidebar.skills" />

				<div className="bg-white">
					<div className="roe-card-style pa-0">
						<div className="btn-container">
							<Button
								className="btn form-button add-skill-btn"
								onClick={handleChangeAddOrEditModal({ open: true, data: null })}>
								Add Skill
							</Button>
						</div>

						<Nav tabs className="mb-10">
							{roles.map((role) => (
								<NavItem key={role.id} className="cursor-pointer">
									<NavLink
										className={`${activeTab === role.id ? 'active' : ''}`}
										onClick={() => toggle(role.id)}>
										{role.name}
									</NavLink>
								</NavItem>
							))}
						</Nav>

						<TabContent activeTab={activeTab}>
							{roles.map((role) => (
								<TabPane key={role.id} tabId={role.id}>
									<ReactTable
										manual
										data={skills}
										sortable={false}
										columns={columns}
										page={activePage - 1}
										onPageChange={handleChangePage}
										totalCount={count}
										loading={loading}
										pages={totalSkills}
										pageSize={TABLE.LIMIT}
										minRows={TABLE.MIN_ROW}
										LoadingComponent={Loader}
										PaginationComponent={Pagination}
										style={{ border: 'none', boxShadow: 'none' }}
										className="-striped -highlight custom-react-table-theme-class"
										defaultFilterMethod={(filter, row) => {
											const id = filter.pivotId || filter.id;
											return row[id] !== undefined
												? String(row[id].toLowerCase()).includes(
														filter.value.toLowerCase()
												  )
												: true;
										}}
									/>
								</TabPane>
							))}
						</TabContent>
					</div>
				</div>
			</SkillsWrapper>

			<Toaster ref={toaster} />

			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					roles={roles}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
					handleEditSkill={handleEditSkill}
					handleAddSkill={handleAddSkill}
				/>
			)}
		</>
	);
};

export default Skills;
