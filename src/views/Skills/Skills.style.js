import { Modal } from 'reactstrap';
import styled from 'styled-components';

const SkillsWrapper = styled.div`
	.card {
		min-height: calc(100vh - 260px);
	}

	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}

	.btn-container {
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.add-skill-btn {
			width: auto;
			border-radius: 5px;
		}
	}
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
`;

export default SkillsWrapper;
