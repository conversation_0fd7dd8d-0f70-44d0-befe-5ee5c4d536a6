/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { ROLE_SCOPE, TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { getApi, postApi } from 'src/helper/api/Api';
import { GROUP, ROLE, USER_CREATION } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { VideoWrapper, StatusWrapper } from './UserCreation.style';
import AddOrEditModal from './Modal/AddOrEditModal';
import Button from '../../components/button/Button';
import EditIcon from 'src/assets/images/Edit.svg';
import DeleteIcon from '../../assets/images/delete.svg';
import Swal from 'sweetalert2';
import { CONFIRM_DELETE } from 'src/components/header/constants';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const UserCreation = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [tags, setTags] = useState([]);
	const [groups, setGroups] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'User',
					resizable: false,
					Cell: (row) => (
						<div className="text-overflow">
							{row?.original?.first_name + ' ' + row?.original?.last_name}
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Email',
					resizable: false,
					Cell: (row) => <div className="text-overflow">{row?.original?.email}</div>,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Group',
					resizable: false,
					Cell: (row) => (
						<div className="text-overflow">
							{row?.original?.user_group?.group_data?.name
								? row?.original?.user_group?.group_data?.name
								: '-'}
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Role',
					resizable: false,
					Cell: (row) => (
						<div className="text-overflow">{row?.original?.user_role?.Role?.name}</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Tags',
					resizable: false,
					Cell: (row) => (
						<div className="d-flex gap-2 flex-wrap">
							{row?.original?.user_tags.length > 0
								? row?.original?.user_tags.map((role) => (
										<StatusWrapper>
											<div className="status module-tag">
												{role.tag_data.name}
											</div>
										</StatusWrapper>
								  ))
								: '-'}
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Status',
					width: 140,
					resizable: false,
					Cell: (row) => (
						<StatusWrapper>
							<div className={`status ${row?.original?.is_active ? 'id-1' : 'id-3'}`}>
								{row?.original?.is_active ? 'Active' : 'Inactive'}
							</div>
						</StatusWrapper>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							{cell?.original?.feedback_status?.id !== 2 ? (
								<img
									src={EditIcon}
									alt="EditIcon"
									title="View"
									width={23}
									className="mr-10 cursor-pointer"
									onClick={handleChangeViewOrEditModal({
										open: true,
										view: false,
										data: cell.original
									})}
								/>
							) : null}
							<img
								src={DeleteIcon}
								alt="ViewIcon"
								title="Delete"
								width={21}
								className="mr-10 cursor-pointer"
								onClick={() => handleChangeDeleteModal(cell.original.id)}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	const getTags = async () => {
		getApi(USER_CREATION.TAG_GET, {})
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.tags;

					if (apiData?.length) {
						setTags(apiData);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				// toaster.current.error(error?.response?.data?.message);
			});
	};

	useEffect(() => {
		getGroups();
		getTags();
	}, []);

	useEffect(() => {
		getUsers();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangeDeleteModal = (id) => {
		const params = {
			id: id
		};
		Swal.fire(CONFIRM_DELETE).then((result) => {
			if (result.isConfirmed) {
				postApi(USER_CREATION.DELETE, { ...params })
					.then((response) => {
						if (response.data.status) {
							getUsers();
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};

	const getUsers = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				source: ROLE_SCOPE.CMS,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(USER_CREATION.GET, { ...dataToSend });
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.users);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const getGroups = async (id) => {
		try {
			setLoading(true);
			const response = await getApi(GROUP.GET, {});
			if (response?.status === CODES.SUCCESS) {
				setGroups(response?.data?.data?.groups);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addTags = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(USER_CREATION.TAG_ADD, data, '', false);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getTags();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const addUser = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(USER_CREATION.ADD, data, '', false);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getUsers();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const editUser = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(USER_CREATION.UPDATE, data, '', false);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getUsers();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	return (
		<>
			<VideoWrapper>
				<PageTitle
					title="sidebar.userCreation"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="btn-container">
							<Button
								className="btn form-button add-skill-btn"
								onClick={handleChangeViewOrEditModal({
									open: true,
									data: null,
									view: false
								})}>
								Add User
							</Button>
						</div>

						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</VideoWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getUsers={getUsers}
					addUser={addUser}
					editUser={editUser}
					addTags={addTags}
					tags={tags}
					groups={groups}
				/>
			)}
		</>
	);
};

export default UserCreation;
