/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup, Input } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../UserCreation.style';
import Loader from 'src/components/common/Loader';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Dropdown from 'src/components/Dropdown/Dropdown';
import AddIcon from '../../../assets/images/add-white.svg';
import ApproveIcon from '../../../assets/images/approve-white.svg';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import { MAX_LENGTH, MIN_LENGTH, REGEX_VALIDATION } from 'src/helper/constant';

const AddOrEditModal = (props) => {
	const [tags, setTags] = useState([]);
	const [roles, setRoles] = useState([]);
	const [groups, setGroups] = useState([]);
	const [saveLoading, setSaveLoading] = useState(false);
	const [selectedRoles, setSelectedRoles] = useState(null);
	const [selectedGroups, setSelectedGroups] = useState(null);
	const [selectedTags, setSelectedTags] = useState(null);
	const [showCustomTag, setShowCustomTag] = useState(false);

	useEffect(() => {
		if (props.data) {
			formik.setFieldValue('name', props.data.name);
			formik.setFieldValue('first_name', props.data.first_name);
			formik.setFieldValue('last_name', props.data.last_name);
			formik.setFieldValue('email', props.data.email);
			formik.setFieldValue('mobile', props.data.mobile);

			const registeredGroupId = props.data.user_group.group_data.id;
			const isIdPresentOfGroup = props.groups.some((item) => item.id === registeredGroupId);

			// check if old groups are deleted
			if (isIdPresentOfGroup) {
				formik.setFieldValue('group_id', props.data.user_group.group_data.id);
				formik.setFieldValue('role_id', props.data.user_role.Role.id);

				setSelectedGroups(props.data.user_group.group_data);
				handleChangeGroupsDropdown(props.data.user_group.group_data);
				setSelectedRoles(props.data.user_role.Role);
			}

			const tagIDs = props.data.user_tags.map((item) => item.tag_data.id);
			formik.setFieldValue('tags', tagIDs);

			const updatedTags = props.tags.map((tag) => {
				if (tagIDs.includes(tag.id)) {
					return {
						...tag,
						selected: true
					};
				}
				return tag;
			});

			setTags(updatedTags);
		} else {
			if (props.tags.length > 0) {
				setTags(
					props.tags.map((value) => ({
						selected: false,
						id: value.id,
						name: value.name,
						is_active: value.is_active,
						is_deleted: value.is_deleted
					}))
				);
			}
		}
	}, [props.data, props.groups, props.tags]);

	useEffect(() => {
		if (props.groups.length > 0) {
			setGroups(
				props.groups.map((value) => ({
					selected: false,
					id: value.id,
					name: value.name,
					is_active: value.is_active,
					is_deleted: value.is_deleted
				}))
			);
		}
	}, [props.groups]);

	const commonValidationSchema = Yup.object().shape({
		name: Yup.string(),
		first_name: Yup.string().required('First Name is required!'),
		last_name: Yup.string().required('Last Name is required!'),
		email: Yup.string()
			.email('Please enter a valid email.')
			.trim()
			.required('Email is required.')
			.nullable(),
		mobile: Yup.string()
			.matches(REGEX_VALIDATION.MOBILE, 'Please enter valid Mobile number.')
			.min(MIN_LENGTH.MOBILE, 'Minimum length should be 10 digits.')
			.max(MAX_LENGTH.MOBILE, 'Maximum length should be 10 digits.')
			.required('Mobile is required.'),
		group_id: Yup.string().required('Group is required!'),
		role_id: Yup.string().required('Role is required!'),
		tags: Yup.array()
	});

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const handleChangeRolesDropdown = (ids) => {
		setSelectedRoles(ids);
		formik.setFieldValue('role_id', ids.id);
	};
	const handleChangeGroupsDropdown = (ids) => {
		setSelectedGroups(ids);
		formik.setFieldValue('group_id', ids.id);

		const selectedGroup = props.groups.find((group) => group.id === ids.id);

		if (selectedGroup) {
			const groupRoles = selectedGroup.group_role_data.map((roleData) => ({
				id: roleData.role_id,
				name: roleData.role_data.name
			}));
			setRoles(groupRoles);
		} else {
			setRoles([]);
		}
	};
	const handleChangeTagsDropdown = (ids) => {
		setSelectedTags(ids);
		formik.setFieldValue('tags', ids);
	};

	const onSubmitHandler = async (values) => {
		try {
			setSaveLoading(true);
			handleCLoseModal();
			const { name, ...dataToSend } = values;
			if (dataToSend.tags.length === 0) {
				delete dataToSend.tags;
			}
			if (props.data) {
				dataToSend.id = props.data.id;
				props.editUser(dataToSend);
			} else {
				props.addUser(dataToSend);
			}
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSaveLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			name: '', // Tags's name creation

			// this are user creation form fields
			first_name: '',
			last_name: '',
			email: '',
			mobile: '',
			group_id: '',
			role_id: '',
			tags: []
		},
		validationSchema: commonValidationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	const addCustomTag = () => {
		if (showCustomTag) {
			try {
				setSaveLoading(true);
				if (!formik.values.name) {
					throw error;
				}
				props.addTags({ name: formik.values.name });

				setShowCustomTag(false);
				formik.setFieldValue('name', '');
			} catch (error) {
				formik.setFieldError('name', 'Please add tag!');
			} finally {
				setSaveLoading(false);
			}
		} else {
			setShowCustomTag(true);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} User
				</ModalHeader>

				<Form onSubmit={formik.handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<div className="row">
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>First name</Label>
											<Input
												id="first_name"
												name="first_name"
												value={formik.values.first_name}
												onChange={formik.handleChange}
												onBlur={formik.handleBlur}
												type="text"
												placeholder="Your first name..."
											/>
											{formik.touched.first_name &&
												formik.errors.first_name && (
													<span className="error-msg mt-0">
														{formik.errors.first_name}
													</span>
												)}
										</FormGroup>
									</div>
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>Last name</Label>
											<Input
												id="last_name"
												name="last_name"
												value={formik.values.last_name}
												onChange={formik.handleChange}
												onBlur={formik.handleBlur}
												type="text"
												placeholder="Your last name..."
											/>
											{formik.touched.last_name &&
												formik.errors.last_name && (
													<span className="error-msg mt-0">
														{formik.errors.last_name}
													</span>
												)}
										</FormGroup>
									</div>
								</div>
								<div className="row">
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>Email</Label>
											<Input
												id="email"
												name="email"
												value={formik.values.email}
												onChange={formik.handleChange}
												onBlur={formik.handleBlur}
												type="text"
												placeholder="Your email..."
											/>
											{formik.touched.email && formik.errors.email && (
												<span className="error-msg mt-0">
													{formik.errors.email}
												</span>
											)}
										</FormGroup>
									</div>
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>Mobile</Label>
											<Input
												id="mobile"
												name="mobile"
												value={formik.values.mobile}
												onChange={formik.handleChange}
												onBlur={formik.handleBlur}
												type="text"
												placeholder=""
											/>
											{formik.touched.mobile && formik.errors.mobile && (
												<span className="error-msg mt-0">
													{formik.errors.mobile}
												</span>
											)}
										</FormGroup>
									</div>
								</div>
								<div className="row">
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>Group</Label>
											<SingleDropdown
												data={groups}
												keyProps={['name']}
												onSelect={handleChangeGroupsDropdown}
												selectedData={selectedGroups}
												className={'w-100 text-left'}
											/>
											{formik.touched.group_id && formik.errors.group_id && (
												<span className="error-msg mt-0">
													{formik.errors.group_id}
												</span>
											)}
										</FormGroup>
									</div>
									<div className="col-md-12 col-lg-6">
										<FormGroup>
											<Label>Role</Label>
											<SingleDropdown
												data={roles}
												keyProps={['name']}
												onSelect={handleChangeRolesDropdown}
												selectedData={selectedRoles}
												className={'w-100 text-left'}
											/>
											{formik.touched.role_id && formik.errors.role_id && (
												<span className="error-msg mt-0">
													{formik.errors.role_id}
												</span>
											)}
										</FormGroup>
									</div>
								</div>
								<FormGroup>
									<Label>Tags</Label>
									<Dropdown
										data={tags}
										placeholder="Select tags"
										handleChange={handleChangeTagsDropdown}
										disabled={false}
									/>
									{formik.touched.tags && formik.errors.tags && (
										<span className="error-msg mt-0">{formik.errors.tags}</span>
									)}
								</FormGroup>
								{showCustomTag ? (
									<FormGroup>
										<Input
											id="name"
											name="name"
											value={formik.values.name}
											onChange={formik.handleChange}
											onBlur={formik.handleBlur}
											className="mt-10"
											type="text"
											placeholder="Your tag..."
										/>
										{formik.errors.name && (
											<span className="error-msg mt-0">
												{formik.errors.name}
											</span>
										)}
									</FormGroup>
								) : null}
								<div
									className="d-flex gap-1 add-btn ml-auto mt-10"
									onClick={addCustomTag}>
									<img
										src={showCustomTag ? ApproveIcon : AddIcon}
										alt="add-icon"
										width={showCustomTag ? '20px' : '18px'}
									/>
									{showCustomTag ? 'Save' : 'Add Tags'}
								</div>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								{props.edit ? 'Save' : 'Add'}
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModal;
