import React, { useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import PageTitle from '../../components/common/PageTitle';
import ChangePasswordForm from './ChangePasswordForm';
import Toaster from '../../components/common/Toaster';
import { postApi } from '../../helper/api/Api';
import ChangePasswordWrapper from './ChangePassword.style';
import { ROUTES } from '../../helper/constant';
import { Form, Input, Label } from 'reactstrap';

import Error from '../../components/common/Error';
import Button from '../../components/button/Button';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { PASSWORD } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';

const ChangePassword = (props) => {
	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [disabled, setDisabled] = useState(false);
	const navigate = useNavigate();

	const themeChanger = useSelector((state) => state.themeChanger);

	const { handleChange, handleSubmit, isValid, values, errors, touched, handleBlur, resetForm } =
		useFormik({
			validationSchema: Yup.object().shape({
				currentPassword: Yup.string().required('This field is required'),
				newPassword: Yup.string().required('This field is required'),
				confirmPassword: Yup.string().required('This field is required')
			}),
			validate: (values) => {
				const errors = {};
				const pattern = new RegExp('.*[@#$%^&+=]');
				const pattern2 = new RegExp('(?=.*[A-Z])');
				const pattern3 = new RegExp('(?=.*[0-9])');

				if (values.newPassword) {
					if (values.newPassword.length < 8) {
						errors.newPassword = 'password must have 8 characters';
					} else if (!pattern.test(values.newPassword)) {
						errors.newPassword = `password must have at-least one special character `;
					} else if (!pattern2.test(values.newPassword)) {
						errors.newPassword = `password must have at-least one Capital Letter `;
					} else if (!pattern3.test(values.newPassword)) {
						errors.newPassword = `password must have at-least one Number `;
					}
				}

				if (values.confirmPassword && values.newPassword !== values.confirmPassword) {
					errors.confirmPassword = 'password not matched';
				}
				return errors;
			},
			initialValues: {
				currentPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			validateOnChange: true,
			onSubmit: (values, { resetForm }) => {
				submitFormHandler();
			},
			displayName: 'CustomValidationForm',
			enableReinitialize: true
		});

	const formAttributes = (fieldName) => ({
		id: fieldName,
		value: values?.[fieldName] || '',
		onChange: handleChange,
		onBlur: handleBlur
	});

	const submitFormHandler = async () => {
		try {
			const params = {
				old_password: values.currentPassword,
				new_password: values.confirmPassword,
				confirm_new_password: values.newPassword
			};
			setDisabled(true);
			setLoading(true);

			const response = await postApi(PASSWORD.CHANGE_PASSWORD, params);

			if (response.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				resetForm();
			}
		} catch (error) {
			toaster.current.error(error.message);
		} finally {
			setDisabled(false);
			setLoading(false);
		}
	};

	return (
		<ChangePasswordWrapper {...props} sidebarTheme={themeChanger.sidebarTheme}>
			<div className="pos-relative">
				<PageTitle title="sidebar.changepassword" className="plr-0" />
				<div
					className="back-icon fs-15 demi-bold-text cursor-pointer"
					onClick={() => navigate('/dashboard')}>
					<i className="fas fa-step-backward"></i> Back
				</div>
			</div>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-header module-header"></div>
					<div className="roe-card-body">
						<form>
							<div className="form-group">
								<Label className="fs-14">Current Password</Label>
								<Input
									type="password"
									onPaste={(e) => {
										e.preventDefault();
									}}
									className="form-control react-form-input"
									placeholder="Current Password"
									{...formAttributes('currentPassword')}
								/>
								<Error
									errors={errors}
									touched={touched}
									fieldName="currentPassword"
								/>
							</div>
							<div className="form-group mt-10">
								<Label className="fs-14">New Password</Label>
								<Input
									type="password"
									onPaste={(e) => {
										e.preventDefault();
									}}
									className="form-control react-form-input"
									placeholder="New Password"
									{...formAttributes('newPassword')}
								/>
								<Error errors={errors} touched={touched} fieldName="newPassword" />
							</div>
							<div className="form-group mt-10">
								<Label className="fs-14 ">Confirm Password</Label>
								<Input
									type="password"
									onPaste={(e) => {
										e.preventDefault();
									}}
									className="form-control react-form-input"
									placeholder="Confirm Password"
									{...formAttributes('confirmPassword')}
								/>
								<Error
									errors={errors}
									touched={touched}
									fieldName="confirmPassword"
								/>
							</div>
							<div className="d-flex justify-content-center">
								<Button
									onClick={handleSubmit}
									className="btn form-button col-1 mt-15"
									style={{ maxWidth: '125px' }}
									disabled={!isValid || disabled}
									loading={loading}>
									Submit
								</Button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
		</ChangePasswordWrapper>
	);
};

export default ChangePassword;
