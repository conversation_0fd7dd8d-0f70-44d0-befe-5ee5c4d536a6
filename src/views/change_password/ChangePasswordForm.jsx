import React from 'react';
import { Input, Label } from 'reactstrap';
import { formAttributes } from '../../helper/functions';

import Error from '../../components/common/Error';
import Button from '../../components/button/Button';

const adminChangePasswordForm = (props) => {
	const { onSubmit, action, loading, disabled, values, isValid } = props;

	const handleSubmit = (e) => {
		e.preventDefault();

		if (isValid) {
			onSubmit(values, action);
		}
	};

	return (
		<div>
			<form>
				<div className="form-group">
					<Label className="fs-16 medium-text">Current Password</Label>
					<Input
						type="password"
						onPaste={(e) => {
							e.preventDefault();
						}}
						className="form-control react-form-input"
						placeholder="Current Password"
						{...formAttributes(props, 'currentPassword')}
					/>
					<Error {...props} fieldName="currentPassword" />
				</div>
				<div className="form-group">
					<Label className="fs-16 medium-text">New Password</Label>
					<Input
						type="password"
						onPaste={(e) => {
							e.preventDefault();
						}}
						className="form-control react-form-input"
						placeholder="New Password"
						{...formAttributes(props, 'newPassword')}
					/>
					<Error {...props} fieldName="newPassword" />
				</div>
				<div className="form-group">
					<Label className="fs-16 medium-text">Confirm Password</Label>
					<Input
						type="password"
						onPaste={(e) => {
							e.preventDefault();
						}}
						className="form-control react-form-input"
						placeholder="Confirm Password"
						{...formAttributes(props, 'confirmPassword')}
					/>
					<Error {...props} fieldName="confirmPassword" />
				</div>
				<div className="d-flex justify-content-center">
					<Button
						onClick={(e) => handleSubmit(e)}
						className="btn form-button col-1"
						style={{ maxWidth: '125px' }}
						disabled={!isValid || disabled}
						loading={loading}
						type="submit">
						Submit
					</Button>
				</div>
			</form>
		</div>
	);
};

export default adminChangePasswordForm;
