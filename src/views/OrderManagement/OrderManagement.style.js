import styled from 'styled-components';
import { Modal } from 'reactstrap';

const OrderManagementWrapper = styled.div`
	.rejected {
		color: #000;
		padding: 5px;
		text-align: center;
		border-radius: 10px;
		background-color: red;
		max-width: 100px;
	}

	.card-img {
		width: 23px;
		height: 23px;
	}
	.text-light-gray {
		color: #868686;
	}

	.card-text {
		width: 100%;
		text-overflow: ellipsis;
		word-wrap: normal;
		overflow: hidden;
		white-space: nowrap;
	}
	.card-content {
		width: calc(100% - 47px);
	}

	.approved {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: green;
		max-width: 100px;
	}

	.pending {
		color: #000;
		padding: 5px 10px;
		text-align: center;
		border-radius: 10px;
		background-color: yellow;
		max-width: 100px;
	}
	.maxWidth {
		max-width: 115px;
	}

	.icon {
		width: 18px;
		height: 18px;
	}

	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups {
		display: none;
	}
	.-padRow.-even {
		display: none;
	}
	.last-column {
		display: flex;
		justify-content: flex-end;
		gap: 5px;
		align-items: center;
	}
	.status {
		background-color: #fff8ec;
		padding: 6px 12px;
		border-radius: 14px;
		font-size: 14px;
	}
	.scope-task-image {
		max-width: 170px;
		max-height: 667px;
		height: 100%;
		border: 1px solid #dee2e6;
	}
	.index-block {
		background: #ecececc4;
		padding: 1px 10px;
		border-radius: 5px;
	}
	.border-main-color {
		border-left: 5px solid #1cb4e3;
	}
	.card-task {
		cursor: pointer;
		transition: 0.5s all;
	}
	.card-task:not(.disabled):hover {
		box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
	}
	.task-container {
		height: calc(100vh - 240px);
	}
	.card-task.disabled {
		cursor: not-allowed;
		opacity: 0.5;
	}
	.module-wise-permissions {
		.title {
			font-size: 12px;
			padding-left: 10px;
		}
		.permission-container {
			padding: 10px 0 10px 25px;
			margin: 0 0 10px 25px;
			border-left: 1px solid #bbbbbb;
			border-radius: 0 0 0px 20px;
		}
	}
`;

export const StatusWrapper = styled.div`
	.status {
		background-color: #fff8ec;
		padding: 6px 12px;
		border-radius: 14px;
		font-size: 14px;
		width: fit-content;
		text-align: center;
		font-weight: 500;
	}
	.module-tag {
		background-color: #fbecff;
		border: 1px solid #f5cfff;
		color: #a01bae;
	}
	.permission-tag {
		background-color: #ccfbf1;
		border: 1px solid #a7f2e2;
		color: #066e68;
	}
	/* New */
	.status.id-1 {
		background-color: #eaf0ff;
		color: #3061de;
	}
	/* In progress */
	.status.id-2 {
		color: #8b610d;
	}
	/* Completed */
	.status.id-4 {
		background-color: #edfef2;
		color: #0e702e;
	}
	/* Overdue */
	.status.id-3 {
		background-color: #ffefd2;
		color: #d39a28;
	}
	.status.id-spaces {
		background-color: #1cb4e3;
		color: #fff;
	}
`;

export const AttachmentStyles = `
  .attachment-modal .modal-content {
    background-color: #f8f9fa;
  }
  .attachment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
  .attachment-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .attachment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }
  .touchpoint {
    font-weight: 600;
    color: #495057;
  }
  .download-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    padding: 0.25rem;
    transition: color 0.2s;
  }
  .download-btn:hover {
    color: #212529;
  }
  .attachment-content {
    padding: 1rem;
  }
  .attachment-media {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
  }
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
	.profile-img-wrapper {
		height: 150px;
		width: fit-content;
	}
	.kyc-doc-container {
		gap: 15px;
		display: flex;
		margin-top: 5px;
		align-items: center;

		.kyc-doc {
			object-fit: contain;
			max-width: calc(50% - 8px);
		}
	}
	.space-container {
		background: #f7f7f7;
		padding: 2px 10px;
		border-radius: 10px;
		hr {
			border-color: #9e9e9e;
		}
	}
	.descriptive-card {
		background: #f0f0f0;
		border-radius: 5px;
		padding: 10px 18px;
	}
	.img-answer {
		width: 30px;
		height: 30px;
		min-width: 30px;
		border-radius: 5px;
		cursor: pointer;
	}
`;

export default OrderManagementWrapper;
