/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import OrderManagementWrapper, { StatusWrapper } from './OrderManagement.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { ROUTES, TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { getApi, postApi } from 'src/helper/api/Api';
import { ORDER_MANAGEMENT, SPACES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import AddOrEditModal from './Modal/ViewModal';
import ViewIcon from '../../assets/images/View.svg';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { useNavigate } from 'react-router-dom';
import { convertTimeToLocal } from 'src/helper/functions';
import Button from 'src/components/button/Button';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const OrderManagement = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'Order',
			fixed: 'left',
			columns: [
				{
					Header: 'Order Name',
					resizable: false,
					Cell: (cell) => (
						<>
							<div className="text-overflow">{cell?.original?.title}</div>
						</>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 180
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Order ID',
					resizable: false,
					Cell: (row) => `${row?.original?.actual_order_id}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					width: 180
				},
				{
					Header: 'Fabricator',
					resizable: false,
					Cell: (cell) => (
						<>
							<div className="text-overflow">
								{cell?.original?.fabricator_data?.first_name}{' '}
								{cell?.original?.fabricator_data?.last_name}
							</div>
						</>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 180
				},
				{
					Header: 'Fabricator Email',
					resizable: false,
					Cell: (cell) => (
						<>
							<div>{cell?.original?.fabricator_email}</div>
						</>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 280
				},
				{
					Header: 'Total Work Orders',
					resizable: false,
					Cell: (row) => `${row?.original?.work_orders_count}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 180
				},
				{
					Header: 'Created On',
					resizable: false,
					minWidth: 150,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-left pa-20',
					className: 'pa-20 text-left d-flex align-items-center'
				},
				{
					Header: 'Status',
					resizable: false,
					Cell: (cell) => (
						<>
							<StatusWrapper>
								<div className={`status id-${cell?.original?.order_status?.id}`}>
									{cell?.original?.order_status?.title}{' '}
								</div>
							</StatusWrapper>
						</>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 120
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					sticky: 'right',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={() =>
									navigate(ROUTES.ORDER_MANAGEMENT.DETAILS, {
										state: cell.original
									})
								}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					minWidth: 80
				}
			]
		}
	];

	useEffect(() => {
		getWorkOrder();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getWorkOrder = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}
			const response = await getApi(
				ORDER_MANAGEMENT.GET,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.orders);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addSpace = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SPACES.ADD, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrder();
			}
		} catch (error) {
			console.log(error);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};
	const editSpace = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SPACES.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrder();
			}
		} catch (error) {
			console.log(error);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const syncDataFromZoho = async () => {
		try {
			setLoading(true);
			const response = await getApi(ORDER_MANAGEMENT.GET_ZOHO, {}, 'order');
			if (response?.status === CODES.SUCCESS) {
				getWorkOrder();
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			toaster.current.error(error?.response?.data?.message);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	return (
		<>
			<OrderManagementWrapper>
				<PageTitle
					title="sidebar.orderManagement"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="justify-content-end">
						<Button
							onClick={syncDataFromZoho}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Sync
						</Button>
					</div>

					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
											filter.value.toLowerCase()
										)
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</OrderManagementWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getWorkOrder={getWorkOrder}
					editSpace={editSpace}
					addSpace={addSpace}
				/>
			)}
		</>
	);
};

export default OrderManagement;
