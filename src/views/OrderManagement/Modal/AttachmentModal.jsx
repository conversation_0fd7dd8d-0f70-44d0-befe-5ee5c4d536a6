import { ChevronLeft, ChevronRight, Download } from 'lucide-react';
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, ModalHeader, ModalBody, Button, Badge } from 'reactstrap';
import { FFmpeg } from '@ffmpeg/ffmpeg';

const AttachmentModal = ({ isOpen, toggle, attachments = [] }) => {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [encodedVideos, setEncodedVideos] = useState({});
	const [imageUrls, setImageUrls] = useState({});
	const [message, setMessage] = useState('');
	const [progress, setProgress] = useState(0);
	const [isFFmpegLoading, setIsFFmpegLoading] = useState(false);
	const ffmpegRef = useRef(null);
	const encodingQueue = useRef([]);
	const currentAttachment = attachments[currentIndex] || {};
	const nextAttachment = attachments[(currentIndex + 1) % attachments.length];

	const initFFmpeg = async () => {
		if (!ffmpegRef.current) {
			const ffmpeg = new FFmpeg();
			await ffmpeg.load({
				corePath: 'https://unpkg.com/@ffmpeg/core-mt@0.12.2/dist/umd/ffmpeg-core.js',
				wasmPath: 'https://unpkg.com/@ffmpeg/core-mt@0.12.2/dist/umd/ffmpeg-core.wasm',
				workerPath:
					'https://unpkg.com/@ffmpeg/core-mt@0.12.2/dist/umd/ffmpeg-core.worker.js'
			});
			ffmpegRef.current = ffmpeg;
		}
		return ffmpegRef.current;
	};

	const loadImage = async (imageUrl) => {
		try {
			if (imageUrls[imageUrl]) {
				return imageUrls[imageUrl];
			}

			const response = await fetch(imageUrl);
			if (!response.ok) {
				throw new Error('Failed to fetch image');
			}
			const blob = await response.blob();
			const blobUrl = URL.createObjectURL(blob);

			setImageUrls((prev) => ({
				...prev,
				[imageUrl]: blobUrl
			}));

			return blobUrl;
		} catch (error) {
			console.error('Failed to load image:', error);
			setMessage('Failed to load image.');
			return null;
		}
	};

	const encodeVideo = async (videoUrl, priority = 'high') => {
		if (encodedVideos[videoUrl]) {
			console.log('Using cached video:', videoUrl);
			return encodedVideos[videoUrl];
		}

		try {
			const ffmpeg = await initFFmpeg();

			if (priority === 'low' && isFFmpegLoading) {
				encodingQueue.current.push(videoUrl);
				return null;
			}

			if (priority === 'high') {
				setMessage('Processing video...');
				setIsFFmpegLoading(true);
			}

			const response = await fetch(videoUrl);
			const videoBlob = await response.blob();
			const videoArrayBuffer = await videoBlob.arrayBuffer();

			const urlParts = videoUrl.split('/');
			const rawFileName = urlParts[urlParts.length - 1];
			const outputFileName = `${rawFileName}.mp4`;

			await ffmpeg.writeFile(rawFileName + '_input.mp4', new Uint8Array(videoArrayBuffer));

			await ffmpeg.exec([
				'-i',
				rawFileName + '_input.mp4',
				'-c:v',
				'libx264',
				'-preset',
				'ultrafast',
				'-crf',
				'28',
				'-c:a',
				'aac',
				'-movflags',
				'+faststart',
				'-tune',
				'fastdecode',
				outputFileName
			]);

			const encodedData = await ffmpeg.readFile(outputFileName);
			const encodedUrl = URL.createObjectURL(
				new Blob([encodedData.buffer], { type: 'video/mp4' })
			);

			console.log('Video encoded successfully:', outputFileName);

			setEncodedVideos((prev) => ({
				...prev,
				[videoUrl]: encodedUrl
			}));

			if (priority === 'high') {
				setMessage('');
				setIsFFmpegLoading(false);
			}

			return encodedUrl;
		} catch (error) {
			console.error('Error during video encoding:', error);
			setMessage(priority === 'high' ? 'Failed to encode video.' : '');
			setIsFFmpegLoading(false);
			return null;
		}
	};

	const handleDownload = async () => {
		try {
			let url = '';
			let filename = '';

			if (currentAttachment.type === 'img') {
				// For images, use the blob URL we created
				url = imageUrls[currentAttachment.image_url];
				if (!url) {
					url = await loadImage(currentAttachment.image_url);
				}
				if (!url) {
					setMessage('Please wait for the image to load before downloading.');
					return;
				}
				filename = currentAttachment.image_url.split('/').pop() || 'image.jpg';
			} else if (currentAttachment.type === 'video') {
				url = encodedVideos[currentAttachment.video_url];
				if (!url) {
					setMessage('Please wait for the video to be processed before downloading.');
					return;
				}
				filename = `${currentAttachment.video_url.split('/').pop() || 'video'}.mp4`;
			}

			const link = document.createElement('a');
			link.href = url;
			link.download = filename;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		} catch (error) {
			console.error('Download failed:', error);
			setMessage('Failed to download file.');
		}
	};

	// Pre-load next image/video
	useEffect(() => {
		if (nextAttachment?.type === 'video' && !encodedVideos[nextAttachment.video_url]) {
			encodeVideo(nextAttachment.video_url, 'low');
		} else if (nextAttachment?.type === 'img' && !imageUrls[nextAttachment.image_url]) {
			loadImage(nextAttachment.image_url);
		}
	}, [currentIndex, nextAttachment, encodedVideos, imageUrls]);

	// Load current attachment
	useEffect(() => {
		if (currentAttachment?.type === 'video' && !encodedVideos[currentAttachment.video_url]) {
			encodeVideo(currentAttachment.video_url, 'high');
		} else if (currentAttachment?.type === 'img' && !imageUrls[currentAttachment.image_url]) {
			loadImage(currentAttachment.image_url);
		}

		// Cleanup function
		return () => {
			// Clean up old cached videos and images (keep only current and next)
			const currentUrl = currentAttachment?.video_url || currentAttachment?.image_url;
			const nextUrl = nextAttachment?.video_url || nextAttachment?.image_url;

			Object.entries(encodedVideos).forEach(([url, encodedUrl]) => {
				if (url !== currentUrl && url !== nextUrl) {
					URL.revokeObjectURL(encodedUrl);
					setEncodedVideos((prev) => {
						const { [url]: _, ...rest } = prev;
						return rest;
					});
				}
			});

			Object.entries(imageUrls).forEach(([url, blobUrl]) => {
				if (url !== currentUrl && url !== nextUrl) {
					URL.revokeObjectURL(blobUrl);
					setImageUrls((prev) => {
						const { [url]: _, ...rest } = prev;
						return rest;
					});
				}
			});
		};
	}, [currentAttachment]);

	const handleNext = () => {
		setCurrentIndex((prev) => (prev + 1) % attachments.length);
	};

	const handlePrevious = () => {
		setCurrentIndex((prev) => (prev - 1 + attachments.length) % attachments.length);
	};

	return (
		<Modal isOpen={isOpen} toggle={toggle} className="modal-dialog-centered modal-lg">
			<ModalHeader toggle={toggle} className="position-relative">
				<div className="d-flex justify-between align-items-center">
					<div>View Attachments</div>
				</div>
			</ModalHeader>
			<ModalBody>
				{currentAttachment.touchpoint && (
					<div className="mb-3">
						<Badge
							color="primary"
							className="mb-2"
							style={{
								fontSize: '0.9rem',
								padding: '0.4em 0.8em',
								borderRadius: '4px'
							}}>
							Touchpoint {currentAttachment.touchpoint}
						</Badge>
					</div>
				)}

				<div className="position-relative">
					{attachments.length > 0 ? (
						<div
							className="d-flex justify-content-center align-items-center"
							style={{ minHeight: '400px' }}>
							{currentAttachment?.type === 'img' ? (
								imageUrls[currentAttachment.image_url] ? (
									<img
										src={imageUrls[currentAttachment.image_url]}
										alt={`Attachment ${currentIndex + 1}`}
										style={{
											maxHeight: '400px',
											maxWidth: '100%',
											objectFit: 'contain'
										}}
									/>
								) : (
									<div className="text-center text-muted">
										<p>{message || 'Loading image...'}</p>
									</div>
								)
							) : currentAttachment?.type === 'video' ? (
								<div
									className="video-container w-100 h-100"
									style={{ maxHeight: '400px' }}>
									{encodedVideos[currentAttachment.video_url] ? (
										<video
											key={encodedVideos[currentAttachment.video_url]}
											controls
											width="100%"
											height="100%"
											preload="metadata"
											playsInline
											style={{
												objectFit: 'contain',
												backgroundColor: '#000',
												maxHeight: '400px'
											}}>
											<source
												src={encodedVideos[currentAttachment.video_url]}
												type="video/mp4"
											/>
											Your browser does not support the video tag.
										</video>
									) : (
										<div className="text-center text-muted">
											<p>{message || 'Encoding video...'}</p>
											{progress > 0 && progress < 100 && (
												<div className="progress" style={{ height: '2px' }}>
													<div
														className="progress-bar"
														role="progressbar"
														style={{ width: `${progress}%` }}
														aria-valuenow={progress}
														aria-valuemin="0"
														aria-valuemax="100"
													/>
												</div>
											)}
										</div>
									)}
								</div>
							) : (
								<div className="text-center text-muted">
									Unsupported attachment type
								</div>
							)}
						</div>
					) : (
						<div className="text-center text-muted">No attachments to display</div>
					)}

					{attachments.length > 1 && (
						<div
							className="d-flex justify-content-between position-absolute"
							style={{
								top: '50%',
								left: 0,
								right: 0,
								transform: 'translateY(-50%)',
								padding: '0 1rem'
							}}>
							<Button
								color="secondary"
								onClick={handlePrevious}
								className="rounded-circle p-2"
								style={{ opacity: 0.7 }}>
								<ChevronLeft size={20} />
							</Button>
							<Button
								color="secondary"
								onClick={handleNext}
								className="rounded-circle p-2"
								style={{ opacity: 0.7 }}>
								<ChevronRight size={20} />
							</Button>
						</div>
					)}
				</div>

				<div className="mt-4">
					{attachments.length > 1 && (
						<div className="text-center mb-3">
							<small className="text-muted">
								{currentIndex + 1} / {attachments.length}
							</small>
						</div>
					)}

					{attachments.length > 0 && (
						<div className="d-flex justify-content-center">
							<Button
								color="primary"
								onClick={handleDownload}
								disabled={
									(currentAttachment?.type === 'video' &&
										!encodedVideos[currentAttachment.video_url]) ||
									(currentAttachment?.type === 'img' &&
										!imageUrls[currentAttachment.image_url])
								}
								className="d-flex align-items-center gap-2 px-4">
								<Download size={18} />
								Download {currentAttachment?.type === 'img' ? 'Image' : 'Video'}
							</Button>
						</div>
					)}
				</div>
			</ModalBody>
		</Modal>
	);
};

export default AttachmentModal;
