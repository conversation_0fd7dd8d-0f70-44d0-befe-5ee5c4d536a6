/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON>dal<PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import approveIcon from 'src/assets/images/approve.svg';
import { AddOrEditModalWrapper, StatusWrapper } from '../OrderManagement.style';
import { Accordion, AccordionBody, AccordionHeader, AccordionItem } from 'reactstrap';
import ImageViewer from 'src/components/ImageTouchPoint/ImageViewer';
import OriginalImageViewer from 'react-simple-image-viewer';

const ViewModalQuestionSet = (props) => {
	const [touchpoints, setTouchpoints] = useState({});
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const [openAccordion, setOpenAccordion] = useState('questionAccordion_0');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const findMatchingTouchPoint = (touchPoint, touchPoints) => {
		const matchingTouchPoint = touchPoints.find((tp) => tp.touch_point === touchPoint);
		return (
			<>
				{matchingTouchPoint ? (
					<div>
						{matchingTouchPoint.title}{' '}
						<strong>(#{matchingTouchPoint.touch_point})</strong>
					</div>
				) : null}
			</>
		);
	};

	const handleChangeImageViewer = (open, images, imageIndex) => () => {
		setImageViewer({ open, images });
	};

	// Getting coordinates on image click
	const handleImageClick = (event) => {};

	useEffect(() => {
		const data = props.taskData.scope_data.scope_image.touch_points;
		if (data) {
			const renamedData = data.map((item) => ({
				...item,
				left: item.x_coordinate,
				top: item.y_coordinate
			}));
			setTouchpoints(renamedData);
		}
	}, [props.taskData.scope_data.scope_image.touch_points]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'View'} Task Summary Details
				</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							<div className="d-flex justify-content-center mb-10">
								<ImageViewer
									imageSrc={props?.taskData?.scope_data?.scope_image?.image_url}
									onImageClick={handleImageClick}
									touchpoints={touchpoints}
									cursor_indication={true}
								/>
							</div>
							<Accordion open={openAccordion} toggle={toggleAccordion}>
								{props.data
									.sort((a, b) => (a.touch_point > b.touch_point ? 1 : -1))
									.map((items, index) => (
										<AccordionItem key={index}>
											<AccordionHeader
												targetId={'questionAccordion_' + index}>
												{findMatchingTouchPoint(
													items.touch_point,
													props.taskData.scope_data.scope_image
														.touch_points
												)}
											</AccordionHeader>
											<AccordionBody
												accordionId={'questionAccordion_' + index}>
												<p className="mb-10">
													<strong>Questions & Answers:</strong>
												</p>
												<ol className="m-0 ques-list">
													{items.question_set_data.map((ques, index) => (
														<li key={index}>
															<div>
																{ques.question_data.title}
																{ques.question_data.question_type
																	.title !== 'Descriptive' && (
																	<span className="badge text-bg-info text-white fw-normal ml-5">
																		{
																			ques.question_data
																				.question_type.title
																		}
																	</span>
																)}
															</div>
															<div className="mb-10">
																{ques.question_data
																	.question_options &&
																ques.question_data.question_options
																	.length > 0 ? (
																	<div className="d-flex justify-content-start flex-wrap align-items-center gap-2 mt-10 mb-10">
																		{ques.question_data.question_options.map(
																			(options, index) => {
																				// Check if the option is available in the answers
																				const isOptionInAnswers =
																					ques.question_data.question_answer.some(
																						(answer) =>
																							answer.answer_data.some(
																								(
																									ans
																								) =>
																									ans.answer ===
																									options.title
																							)
																					);

																				return (
																					<div
																						className="card w-49"
																						key={index}>
																						<div className="card-body">
																							<p className="d-flex align-items-center">
																								<strong className="mr-4">
																									{String.fromCharCode(
																										65 +
																											index
																									) +
																										'. '}
																								</strong>
																								{
																									options.title
																								}
																								{isOptionInAnswers ? (
																									<img
																										className="ml-auto align-bottom"
																										width={
																											23
																										}
																										src={
																											approveIcon
																										}
																										alt="approveIcon"
																									/>
																								) : null}
																							</p>
																						</div>
																					</div>
																				);
																			}
																		)}
																	</div>
																) : null}
																{ques.question_data?.question_type
																	?.title === 'Descriptive'
																	? ques.question_data?.question_answer.map(
																			(answers, index) => (
																				<div key={index}>
																					{answers.answer_data.map(
																						(
																							ans,
																							index
																						) => (
																							<div className="card mt-10 border-none">
																								<div
																									className="descriptive-card card-body"
																									key={
																										index
																									}>
																									<p
																										className={
																											ans
																												.answer
																												.length >
																											0
																												? 'text-normal'
																												: 'text-muted font-12'
																										}>
																										{ans.answer
																											? ans.answer
																											: 'No answer found.'}
																									</p>
																								</div>
																							</div>
																						)
																					)}
																					<div className="mt-10">
																						{answers.image_answer_data.map(
																							(
																								images,
																								index,
																								row
																							) => (
																								<div
																									className={`ml-5 d-flex align-items-center ${
																										index +
																											1 !==
																										row.length
																											? 'mb-10'
																											: ''
																									}`}
																									key={
																										index
																									}>
																									<img
																										className="img-answer"
																										onClick={handleChangeImageViewer(
																											true,
																											[
																												images.image_url
																											],
																											0
																										)}
																										src={
																											images.image_url
																										}
																										alt="img_answer"
																									/>
																									<span className="text-muted font-14 ml-10 text-break">
																										{images.image_key.replace(
																											'task/',
																											''
																										)}
																									</span>
																								</div>
																							)
																						)}
																					</div>
																				</div>
																			)
																	  )
																	: null}
															</div>
														</li>
													))}
												</ol>
											</AccordionBody>
										</AccordionItem>
									))}
							</Accordion>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						{props.data && !props.edit && (
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button"
								style={{ backgroundColor: '#6c757d' }}>
								Cancel
							</Button>
						)}
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
			{imageViewer.open && (
				<OriginalImageViewer
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default ViewModalQuestionSet;
