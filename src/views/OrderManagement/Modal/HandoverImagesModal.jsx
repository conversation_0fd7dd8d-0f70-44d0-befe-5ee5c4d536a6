import { ChevronLeft, ChevronRight, Download } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, ModalHeader, ModalBody, Button } from 'reactstrap';

const HandoverImagesModal = ({ isOpen, toggle, handoverImages = [] }) => {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [imageUrls, setImageUrls] = useState({});
	const [message, setMessage] = useState('');
	const currentAttachment = handoverImages?.[currentIndex] || {};
	const nextAttachment = handoverImages?.[(currentIndex + 1) % handoverImages?.length];

	const loadImage = async (imageUrl) => {
		try {
			if (imageUrls?.[imageUrl]) {
				return imageUrls?.[imageUrl];
			}

			const response = await fetch(imageUrl);
			if (!response.ok) {
				throw new Error('Failed to fetch image');
			}
			const blob = await response.blob();
			const blobUrl = URL.createObjectURL(blob);

			setImageUrls((prev) => ({
				...prev,
				[imageUrl]: blobUrl
			}));

			return blobUrl;
		} catch (error) {
			console.error('Failed to load image:', error);
			setMessage('Failed to load image.');
			return null;
		}
	};

	const handleDownload = async () => {
		try {
			let url = '';
			let filename = '';
			url = imageUrls?.[currentAttachment?.image_url];
			if (!url) {
				url = await loadImage(currentAttachment?.image_url);
			}
			if (!url) {
				setMessage('Please wait for the image to load before downloading.');
				return;
			}
			filename = currentAttachment?.image_url?.split('/')?.pop() || 'image.jpg';
			const link = document.createElement('a');
			link.href = url;
			link.download = filename;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		} catch (error) {
			console.error('Download failed:', error);
			setMessage('Failed to download file.');
		}
	};

	// Pre-load next image/video
	useEffect(() => {
		if (!imageUrls?.[nextAttachment?.image_url]) {
			loadImage(nextAttachment?.image_url);
		}
	}, [currentIndex, nextAttachment, imageUrls]);

	// Load current attachment
	useEffect(() => {
		if (!imageUrls?.[currentAttachment?.image_url]) {
			loadImage(currentAttachment?.image_url);
		}

		// Cleanup function
		return () => {
			// Clean up old cached videos and images (keep only current and next)
			const currentUrl = currentAttachment?.image_url;
			const nextUrl = nextAttachment?.image_url;

			Object.entries(imageUrls).forEach(([url, blobUrl]) => {
				if (url !== currentUrl && url !== nextUrl) {
					URL.revokeObjectURL(blobUrl);
					setImageUrls((prev) => {
						const { [url]: _, ...rest } = prev;
						return rest;
					});
				}
			});
		};
	}, [currentAttachment]);

	const handleNext = () => {
		setCurrentIndex((prev) => (prev + 1) % handoverImages?.length);
	};

	const handlePrevious = () => {
		setCurrentIndex((prev) => (prev - 1 + handoverImages?.length) % handoverImages?.length);
	};

	return (
		<Modal isOpen={isOpen} toggle={toggle} className="modal-dialog-centered modal-lg">
			<ModalHeader toggle={toggle} className="position-relative">
				<div className="d-flex justify-between align-items-center">
					<div>View Handover Images</div>
				</div>
			</ModalHeader>
			<ModalBody>
				<div className="position-relative">
					{handoverImages?.length > 0 ? (
						<div
							className="d-flex justify-content-center align-items-center"
							style={{ minHeight: '400px' }}>
							{imageUrls?.[currentAttachment?.image_url] ? (
								<img
									src={imageUrls?.[currentAttachment?.image_url]}
									alt={`Attachment ${currentIndex + 1}`}
									style={{
										maxHeight: '400px',
										maxWidth: '100%',
										objectFit: 'contain'
									}}
								/>
							) : (
								<div className="text-center text-muted">
									<p>{message || 'Loading image...'}</p>
								</div>
							)}
						</div>
					) : (
						<div className="text-center text-muted">No handover images to display</div>
					)}

					{handoverImages?.length > 1 && (
						<div
							className="d-flex justify-content-between position-absolute"
							style={{
								top: '50%',
								left: 0,
								right: 0,
								transform: 'translateY(-50%)',
								padding: '0 1rem'
							}}>
							<Button
								color="secondary"
								onClick={handlePrevious}
								className="rounded-circle p-2"
								style={{ opacity: 0.7 }}>
								<ChevronLeft size={20} />
							</Button>
							<Button
								color="secondary"
								onClick={handleNext}
								className="rounded-circle p-2"
								style={{ opacity: 0.7 }}>
								<ChevronRight size={20} />
							</Button>
						</div>
					)}
				</div>

				<div className="mt-4">
					{handoverImages?.length > 1 && (
						<div className="text-center mb-3">
							<small className="text-muted">
								{currentIndex + 1} / {handoverImages?.length}
							</small>
						</div>
					)}

					{handoverImages?.length > 0 && (
						<div className="d-flex justify-content-center">
							<Button
								color="primary"
								onClick={handleDownload}
								disabled={!imageUrls?.[currentAttachment?.image_url]}
								className="d-flex align-items-center gap-2 px-4">
								<Download size={18} />
								Download Image
							</Button>
						</div>
					)}
				</div>
			</ModalBody>
		</Modal>
	);
};

export default HandoverImagesModal;
