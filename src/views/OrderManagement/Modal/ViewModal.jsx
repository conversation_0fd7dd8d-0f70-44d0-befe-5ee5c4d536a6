/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useState } from 'react';
import { <PERSON>dalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper, StatusWrapper } from '../OrderManagement.style';
import { Accordion, AccordionBody, AccordionHeader, AccordionItem } from 'reactstrap';

const ViewOrEditModal = (props) => {
	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const [openAccordion, setOpenAccordion] = useState('order_1');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'View'} Order Details
				</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							<Accordion open={openAccordion} toggle={toggleAccordion}>
								<AccordionItem>
									<AccordionHeader targetId="order_1">
										Order Details
									</AccordionHeader>
									<AccordionBody accordionId="order_1">
										{props.data.id && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Order ID:</strong> {props.actual_order_id}
											</div>
										)}
										{props.actual_order_id && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Work Order ID:</strong>{' '}
												{props.data.actual_work_order_id}
											</div>
										)}
										{props.data.work_order_type &&
											props.data.work_order_type.title && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Work order type:</strong>{' '}
													{props.data.work_order_type.title}
												</div>
											)}
										{props.data.assigned_to_user.first_name &&
											props.data.assigned_to_user.last_name && (
												<div className="mb-10 mt-10 text-capitalize">
													<strong>Assigned To:</strong>{' '}
													{props.data?.assigned_to_user?.first_name +
														' ' +
														props.data?.assigned_to_user?.last_name}
												</div>
											)}
										{props.data.intiated && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Initiated By:</strong>{' '}
												{props.data?.intiated}
											</div>
										)}
										{props.data?.work_order_status?.id && (
											<div className="mb-10 mt-10 text-capitalize d-flex align-items-center">
												<strong>Work Order Status:</strong>{' '}
												<StatusWrapper>
													<div
														className={`ml-10 status id-${props?.data?.work_order_status?.id}`}>
														{props?.data?.work_order_status?.title}
													</div>
												</StatusWrapper>
											</div>
										)}

										{props.data.scheduled_start_date &&
											props.data.scheduled_start_time && (
												<div className="mb-10 mt-10 text-capitalize vertical-middle">
													<strong>Scheduled start date & time:</strong>{' '}
													{props.data.scheduled_start_date}{' '}
													<strong>|</strong>{' '}
													{props.data.scheduled_start_time}
												</div>
											)}
										{props.data.scheduled_duration && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Duration:</strong>{' '}
												{props.data.scheduled_duration}
											</div>
										)}
										{props.data.instructions && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Specific Instructions:</strong>{' '}
												{props.data.instructions}
											</div>
										)}
									</AccordionBody>
								</AccordionItem>
								<AccordionItem>
									<AccordionHeader targetId="order_2">
										Space & Scope Details
									</AccordionHeader>
									<AccordionBody accordionId="order_2">
										{props.data.work_order_spaces &&
										props.data.work_order_spaces.length > 0
											? props.data.work_order_spaces.map((items) => (
													<>
														<div className="space-container mb-15">
															<div className="mb-10 mt-10 text-capitalize d-flex flex-column align-items-start gap-2">
																<strong>Space:</strong>
																<hr className="w-100 m-0 mb-2 p-0" />
																<div className="d-flex flex-wrap gap-2">
																	<StatusWrapper>
																		<div className="status id-spaces">
																			{
																				items?.space_data
																					?.title
																			}
																		</div>
																	</StatusWrapper>
																</div>
															</div>
															<div className="mb-10 mt-15 text-capitalize d-flex flex-column align-items-start gap-2">
																<strong>Scope:</strong>
																<hr className="w-100 m-0 mb-2 p-0" />
																<div className="d-flex flex-wrap gap-2">
																	{items?.work_order_scopes.map(
																		(items) => (
																			<StatusWrapper
																				key={items.id}>
																				<div className="status id-spaces">
																					{items
																						?.scope_data
																						?.name +
																						' - ' +
																						items
																							?.scope_data
																							?.model}
																					<strong>
																						{' (' +
																							items?.count +
																							')'}
																					</strong>
																				</div>
																			</StatusWrapper>
																		)
																	)}
																</div>
															</div>
														</div>
													</>
											  ))
											: null}
									</AccordionBody>
								</AccordionItem>
							</Accordion>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						{props.data && !props.edit && (
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button"
								style={{ backgroundColor: '#6c757d' }}>
								Cancel
							</Button>
						)}
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewOrEditModal;
