/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import OrderManagementWrapper, { StatusWrapper, AttachmentStyles } from './OrderManagement.style';
import PageTitle from 'src/components/common/PageTitle';
import { getApi, postApi } from 'src/helper/api/Api';
import { ORDER_MANAGEMENT, SPACES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Toaster from 'src/components/common/Toaster';
import { useLocation, useNavigate } from 'react-router-dom';
import Button from 'src/components/button/Button';
import { Card, CardBody, CardText } from 'reactstrap';
import ViewModalQuestionSet from './Modal/ViewModalQuestionSet';
import Loader from 'src/components/common/Loader';
import AttachmentModal from './Modal/AttachmentModal';
const TaskDetails = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [taskQuestions, setTaskQuestions] = useState([]);
	const [loading, setLoading] = useState(false);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const [attachmentModalOpen, setAttachmentModalOpen] = useState(false);
	const [currentAttachments, setCurrentAttachments] = useState([]);
	const toggleAttachmentModal = () => {
		setAttachmentModalOpen(!attachmentModalOpen);
	};
	const handleViewAttachments = (attachments) => {
		setCurrentAttachments(attachments);
		setAttachmentModalOpen(true);
	};
	const { state } = useLocation();

	useEffect(() => {
		getTaskDetails();
	}, []);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getTaskDetails = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				work_order_id: state.work_order_id,
				que_required: 1
			};

			const response = await getApi(
				ORDER_MANAGEMENT.TASK_DETAILS,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.tasks);
			}
		} catch (error) {
			setData([]);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const showQuestions = async (items) => {
		try {
			setLoading(true);
			const dataToSend = {
				work_order_type_id: items.work_order_data.work_order_type.id,
				scope_id: items.scope_data.id,
				task_id: items.id
			};

			const response = await getApi(
				ORDER_MANAGEMENT.TASK_QUESTIONS,
				{
					...dataToSend
				},
				'order'
			);

			if (response?.status === CODES.SUCCESS) {
				setTaskQuestions(response?.data?.data?.question_sets);
				setAddOrEditModalData({
					open: true,
					data: response?.data?.data?.question_sets,
					view: true,
					taskData: items
				});
			}
		} catch (error) {
			setTaskQuestions([]);
			setLoading(false);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleExtractAttachment = (items) => {
		const attachments_url = [];
		items.question_sets?.forEach((question_set) => {
			question_set?.question_set_data?.forEach((set_data) => {
				set_data?.question_data.question_answer?.forEach((answer) => {
					answer.image_answer_data?.forEach((image_data) => {
						attachments_url.push({
							touchpoint: question_set.touch_point,
							...image_data,
							type: 'img'
						});
					});
					answer.video_answer_data
						? attachments_url.push({
								touchpoint: question_set.touch_point,
								...answer.video_answer_data,
								type: 'video'
						  })
						: '';
				});
			});
		});
		handleViewAttachments(attachments_url);
	};

	return (
		<>
			<OrderManagementWrapper>
				<PageTitle title="sidebar.taskDetails" />
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={() => navigate(-1)}
							type="submit"
							className="btn form-button"
							style={{ width: '80px' }}>
							Back
						</Button>
					</div>

					<div className="roe-card-style pt-3">
						<div className="roe-card-body overflow-auto task-container">
							{data.length > 0 ? (
								data.map((items, index) => (
									<div key={items.id}>
										<Card
											className={`my-2 overflow-hidden card-task ${
												items.status !== 'Completed' ? 'disabled' : null
											}`}
											onClick={() =>{
												items.status === 'Completed' &&
												items.scope_data?.scope_image?.image_url
													? showQuestions(items)
													: null;
											}}>
											<CardBody className="border-main-color">
												<div className="d-flex justify-content-between align-items-center w-100">
													<div className="index-block">{index + 1}</div>
													<div>
														<StatusWrapper>
															<div
																className={`status id-${
																	items.status === 'Completed'
																		? '4'
																		: '1'
																}`}>
																{items.status}
															</div>
														</StatusWrapper>
													</div>
												</div>
												<CardText>
													<div>
														<strong>Space:</strong>{' '}
														{items.space_data?.title}
													</div>
													<div>
														<strong>Window Type:</strong>{' '}
														{items.scope_data?.model}
													</div>
													<div>
														<strong>Work Order Type: </strong>
														{
															items.work_order_data?.work_order_type
																?.title
														}
													</div>
												</CardText>
												<div className="d-flex justify-content-center mt-10">
													{items.scope_data?.scope_image?.image_url ? (
														<div className="scope-task-image">
															<img
																src={
																	items.scope_data?.scope_image
																		?.image_url
																}
																className="mw-100"
																alt="scope_img"
															/>
														</div>
													) : (
														<CardText className="text-center">
															<small className="text-muted">
																No scope image found.
															</small>
														</CardText>
													)}
												</div>
												<div
													className="d-flex justify-content-between align-items-center mt-3"
													onClick={(e) => {
														e.stopPropagation();
														handleExtractAttachment(items);
													}}>
													<Button
														// Assuming attachments are in the items object
														className="btn form-button"
														// disabled={!items.attachments?.length}
													>
														View Attachments
													</Button>
												</div>
											</CardBody>
										</Card>
									</div>
								))
							) : (
								<div className="card h-auto">
									<div className="p-5 card-body">
										<p className="text-center text-muted">No records found.</p>
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
				<Loader loading={loading} />
			</OrderManagementWrapper>
			<AttachmentModal
				isOpen={attachmentModalOpen}
				toggle={toggleAttachmentModal}
				attachments={currentAttachments}
			/>
			{addOrEditModalData.open && (
				<ViewModalQuestionSet
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
				/>
			)}
		</>
	);
};

export default TaskDetails;
