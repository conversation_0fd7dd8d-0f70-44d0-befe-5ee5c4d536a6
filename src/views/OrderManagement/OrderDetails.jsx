/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import OrderManagementWrapper, { StatusWrapper } from './OrderManagement.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { ROUTES, TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import ViewIcon from 'src/assets/images/View.svg';
import TaskIcon from 'src/assets/images/task-details.svg';
import LocationIcon from 'src/assets/images/location.svg';
import NavAddressIcon from 'src/assets/images/navigation-address.svg';
import ContactIcon from 'src/assets/images/contact-card.svg';
import EmailIcon from 'src/assets/images/email-icon.svg';
import PhoneIcon from 'src/assets/images/mobile-icon.svg';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
import { getApi, postApi } from 'src/helper/api/Api';
import { ORDER_MANAGEMENT, SPACES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import AddOrEditModal from './Modal/ViewModal';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { useLocation, useNavigate } from 'react-router-dom';
import { convertTimeToLocal } from 'src/helper/functions';
import Button from 'src/components/button/Button';
import AttachmentModal from './Modal/AttachmentModal';
import HandoverImagesModal from './Modal/HandoverImagesModal';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const OrderDetails = () => {
	const toaster = useRef();
	const navigate = useNavigate();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const [handoverImagesModalOpen, setHandoverImagesModalOpen] = useState(false);
	const toggleHandoverImagesModal = () => {
		setHandoverImagesModalOpen(!handoverImagesModalOpen);
	};
	const { state } = useLocation();
	const customerData = [
		{
			id: 1,
			name: 'Name',
			value: state.customer_data.name || '-',
			icon: ContactIcon
		},
		{
			id: 2,
			name: 'Zone',
			value: state.customer_data.zone || '-',
			icon: LocationIcon
		},
		{
			id: 3,
			name: 'Location',
			value: state.customer_data.location || '-',
			icon: NavAddressIcon
		},
		{
			id: 4,
			name: 'Universe',
			value: state.customer_data.universe || '-',
			icon: LocationIcon
		},
		{
			id: 4,
			name: 'Zoho Order ID',
			value: state.zoho_order_id || '-',
			icon: LocationIcon
		}
	];

	const fabricatorData = [
		{
			id: 1,
			name:
				state?.fabricator_data?.first_name && state?.fabricator_data?.last_name
					? state?.fabricator_data?.first_name + ' ' + state?.fabricator_data?.last_name
					: '-',
			value: state?.fabricator_data?.id
				? 'Fabricator ID: ' + state?.fabricator_data?.id
				: 'Fabricator ID: -',
			icon: state?.fabricator_data?.profile_image_url || dummyProfilePic
		},
		{
			id: 2,
			name: 'Zone',
			value: state?.fabricator_data?.address
				? state?.fabricator_data?.address[0]?.zone.name
				: '-',
			icon: LocationIcon
		},
		{
			id: 3,
			name: 'Email',
			value: state?.fabricator_data?.email ? state?.fabricator_data?.email : '-',
			icon: EmailIcon
		},
		{
			id: 4,
			name: 'Phone',
			value:
				state?.fabricator_data?.country_code && +state?.fabricator_data?.mobile
					? state?.fabricator_data?.country_code + ' ' + state?.fabricator_data?.mobile
					: '-',
			icon: PhoneIcon
		}
	];

	const columns = [
		{
			Header: 'ID',
			fixed: 'left',
			columns: [
				{
					Header: 'Work Order ID',
					resizable: false,
					Cell: (row) => `${row.original.actual_work_order_id}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 150,
					enableFilter: true
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Work Order Type',
					resizable: false,
					Cell: (row) => `${row.original.work_order_type.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 250,
					enableFilter: true
				},
				{
					Header: 'Assigned to',
					resizable: false,
					Cell: (cell) => (
						<div>
							{cell?.original?.assigned_to_user?.first_name}{' '}
							{cell?.original?.assigned_to_user?.last_name}
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 200,
					enableFilter: true
				},
				{
					Header: 'Supervisor Type',
					resizable: false,
					Cell: (row) =>
						`${row.original.assigned_to_user?.user_role?.business_type_category}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 190,
					enableFilter: true
				},
				{
					Header: 'Approvers',
					resizable: false,
					Cell: (row) => (
						<div className="module-wise-permissions">
							<div className="w-100">
								{row?.original?.work_order_review?.length > 0
									? row?.original?.work_order_review.map((role, index) => (
											<div key={index}>
												<p className="title">#{index + 1} Approver</p>
												<StatusWrapper>
													<div className="status module-tag">
														{role?.reviewer_user_details?.first_name +
															' ' +
															role?.reviewer_user_details?.last_name}
													</div>
												</StatusWrapper>
												<div className="permission-container">
													<p className="title pl-5">Roles</p>
													<div className="d-flex gap-2 flex-wrap">
														{role?.reviewer_user_details?.user_role
															.Role && (
															<StatusWrapper>
																<div className="status permission-tag">
																	{
																		role?.reviewer_user_details
																			?.user_role?.Role?.name
																	}
																</div>
															</StatusWrapper>
														)}
													</div>
												</div>
											</div>
									  ))
									: '-'}
							</div>
						</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 250,
					enableFilter: true
				},
				{
					Header: 'Date & Time',
					resizable: false,
					Cell: (row) =>
						`${row.original.scheduled_start_date} | ${row.original.scheduled_start_time}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					width: 220,
					enableFilter: true
				},
				{
					Header: 'Created On',
					resizable: false,
					Cell: (row) => `${convertTimeToLocal(row.original.createdAt)}`,
					headerClassName: 'text-left pa-20',
					width: 130,
					className: 'pa-20 text-left d-flex align-items-center'
				},
				{
					Header: 'Status',
					resizable: false,
					Cell: (cell) => (
						<>
							<StatusWrapper>
								<div
									className={`status id-${cell?.original?.work_order_status?.id}`}>
									{cell?.original?.work_order_status?.title}
								</div>
							</StatusWrapper>
						</>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					minWidth: 150,
					enableFilter: true
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: true,
									edit: false,
									data: cell.original,
									actual_order_id: state.actual_order_id
								})}
							/>
							<img
								src={TaskIcon}
								alt="Task Details"
								title="Task Details"
								width={23}
								className="mr-0 cursor-pointer"
								onClick={() =>
									navigate(ROUTES.ORDER_MANAGEMENT.TASK_DETAILS, {
										state: {
											work_order_id: cell?.original?.id
										}
									})
								}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					minWidth: 100
				}
			]
		}
	];

	useEffect(() => {
		getWorkOrder();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getWorkOrder = async () => {
		try {
			setLoading(true);
			const dataToSend = {
				order_id: state.id,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				dataToSend.page = 1;
				dataToSend.search = debounceSearch;
			}
			const response = await getApi(
				ORDER_MANAGEMENT.DETAILS,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.work_orders);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addSpace = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SPACES.ADD, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrder();
			}
		} catch (error) {
			console.log(error);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};
	const editSpace = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SPACES.UPDATE, data, 'order');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getWorkOrder();
			}
		} catch (error) {
			console.log(error);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	return (
		<>
			<OrderManagementWrapper>
				<PageTitle
					title="sidebar.orderDetails"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={() => navigate(ROUTES.ORDER_MANAGEMENT.BASE)}
							type="submit"
							className="btn form-button"
							style={{ width: '80px' }}>
							Back
						</Button>
					</div>
					<div className="w-100">
						<div className="row">
							<div className="col-6">
								<p className="mb-15">
									<strong>Customer Details</strong>
								</p>
								<div className="row g-4">
									{customerData.map((items, index) => (
										<div className="col-sm-12 col-md-12 col-lg-6" key={index}>
											<div className="card h-100">
												<div class="card-body d-flex align-items-center">
													<div>
														<img
															src={items.icon}
															alt={items.icon}
															className="card-img me-4"
														/>
													</div>
													<div className="card-content">
														<p class="card-title text-light-gray mb-2">
															{items.name}
														</p>
														<p class="card-text text-capitalize text-small fs-12">
															{items.value}
														</p>
													</div>
												</div>
											</div>
										</div>
									))}
								</div>
							</div>
							<div className="col-6">
								<p className="mb-15">
									<strong>Fabricator Details</strong>
								</p>
								<div className="row g-4">
									{fabricatorData.map((items, index) => (
										<div className="col-sm-12 col-md-12 col-lg-6">
											<div class="card h-100">
												<div
													class="card-body d-flex align-items-center"
													key={index}>
													<div>
														<img
															src={items.icon}
															alt="fab-img"
															className={`card-img me-4 ${
																items.id === 1
																	? 'border-radius-50'
																	: ''
															}`}
														/>
													</div>
													<div className="card-content">
														<p class="card-title card-text text-light-gray mb-2">
															{items.name}
														</p>
														<p class="card-text fs-12">{items.value}</p>
													</div>
												</div>
											</div>
										</div>
									))}
								</div>
							</div>
						</div>
					</div>
					<Button
						className="btn form-button mt-15"
						onClick={() => toggleHandoverImagesModal()}>
						View Handover Images
					</Button>
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</OrderManagementWrapper>
			{handoverImagesModalOpen && (
				<HandoverImagesModal
					isOpen={handoverImagesModalOpen}
					toggle={toggleHandoverImagesModal}
					handoverImages={state?.order_images}
				/>
			)}
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getWorkOrder={getWorkOrder}
					editSpace={editSpace}
					addSpace={addSpace}
				/>
			)}
		</>
	);
};

export default OrderDetails;
