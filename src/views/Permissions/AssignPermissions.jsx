/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { getApi, postApi } from 'src/helper/api/Api';
import { GROUP, PERMISSION, ROLE } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { VideoWrapper, StatusWrapper } from './Permissions.style';
import AddOrEditModal from './Modal/AddOrEditModal';
import Button from '../../components/button/Button';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { Form, FormGroup, Label } from 'reactstrap';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import Dropdown from 'src/components/Dropdown/Dropdown';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const AssignPermissions = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [roles, setRoles] = useState([]);
	const [module, setModule] = useState([]);
	const [permissions, setPermissions] = useState([]);
	const [selectedModule, setSelectedModule] = useState(null);
	const [selectedRoles, setSelectedRoles] = useState(null);
	const [selectedPermissions, setSelectedPermissions] = useState([]);
	const [searchKey, setSearchKey] = useState(null);
	const [loading, setLoading] = useState(false);

	const validationSchema = Yup.object().shape({
		role_id: Yup.string().required('Role is required!'),
		module_id: Yup.string().required('Module is required!'),
		permission_id: Yup.array().min(1, 'Please select at least one permission!'),
	});

	const onSubmitHandler = async (values) => {
		try {
			setLoading(true);
			assignPermission(values);
		} catch (error) {
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const formik = useFormik({
		initialValues: {
			role_id: '',
			module_id: '',
			permission_id: [],
		},
		validationSchema,
		validateOnChange: true,
		enableReinitialize: true,
		onSubmit: onSubmitHandler
	});

	const handleChangeRolesDropdown = (ids) => {
		setSelectedRoles(ids);
		formik.setFieldValue('role_id', ids.id);
	};

	const handleChangeModuleDropdown = (ids) => {
		setSelectedModule(ids);
		formik.setFieldValue('module_id', ids.id);
	};

	const handleChangePermissionsDropdown = (ids) => {
		setSelectedPermissions(ids);
		formik.setFieldValue('permission_id', ids);
	};

	const getRoles = async () => {
		try {
			setLoading(true);
			const response = await getApi(ROLE.GET, {});
			if (response?.status === CODES.SUCCESS) {
				setRoles(response?.data?.data?.roles);
			}
		} catch (error) {
			setData([]);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const getModule = async () => {
		try {
			setLoading(true);
			const response = await getApi(PERMISSION.MODULE_GET, {});
			if (response?.status === CODES.SUCCESS) {
				setModule(response?.data?.data?.modules);
			}
		} catch (error) {
			setData([]);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const getPermissions = async () => {
		try {
			setLoading(true);
			const response = await getApi(PERMISSION.GET, {});
			if (response?.status === CODES.SUCCESS) {
				setPermissions(response?.data?.data?.permissions);
			}
		} catch (error) {
			setData([]);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const assignPermission = async (data) => {
		try {
			const dataToSend = {
				role_id: data.role_id,
				permission_data: [
					{
						module_id: data.module_id,
						permissions: [...data.permission_id]
					}
				]
			};

			setLoading(true);
			const response = await postApi(PERMISSION.ASSIGN, dataToSend, '', false);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				setSelectedRoles(null);
				setSelectedModule(null);
				setSelectedPermissions([]);
				formik.resetForm();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		getRoles();
		getModule();
		getPermissions();
	}, []);

	return (
		<>
			<VideoWrapper>
				<PageTitle
					title="sidebar.assign"
					search={false}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<Loader loading={loading} />
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<Form onSubmit={formik.handleSubmit}>
							<div className="roe-card-body p-4">
								<p className="points-text">Assign Permission</p>
								<FormGroup>
									<Label>Role</Label>
									<SingleDropdown
										data={roles}
										keyProps={['name']}
										onSelect={handleChangeRolesDropdown}
										selectedData={selectedRoles}
										className={'w-100 text-left'}
									/>
									{formik.touched.role_id && formik.errors.role_id && (
										<span className="error-msg mt-0">
											{formik.errors.role_id}
										</span>
									)}
								</FormGroup>
								<FormGroup className='mt-10'>
									<Label>Module</Label>
									<SingleDropdown
										data={module}
										keyProps={['title']}
										onSelect={handleChangeModuleDropdown}
										selectedData={selectedModule}
										className={'w-100 text-left'}
									/>
									{formik.touched.module_id && formik.errors.module_id && (
										<span className="error-msg mt-0">
											{formik.errors.module_id}
										</span>
									)}
								</FormGroup>
								<FormGroup className='mt-10'>
									<Label>Permissions</Label>
									<Dropdown
										data={permissions}
										placeholder="Select Permission"
										handleChange={handleChangePermissionsDropdown}
										disabled={false}
									/>
									{formik.touched.permission_id && formik.errors.permission_id && (
										<span className="error-msg mt-0">
											{formik.errors.permission_id}
										</span>
									)}
								</FormGroup>
								<Button type="submit" loading={loading} className="btn form-button w-auto px-5 mt-20">
									Save
								</Button>
							</div>
						</Form>
					</div>
				</div>
				<Toaster ref={toaster} />
			</VideoWrapper>
		</>
	);
};

export default AssignPermissions;
