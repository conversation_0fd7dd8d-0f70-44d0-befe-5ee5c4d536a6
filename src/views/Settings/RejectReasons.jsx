/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import SettingsWrapper from './Settings.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { convertTimeToLocal } from 'src/helper/functions';
import EditIcon from 'src/assets/images/Edit.svg';
import ViewIcon from 'src/assets/images/View.svg';
import { Api, getApi } from 'src/helper/api/Api';
import { REASONS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import AddOrEditModal from './Modal/ViewOrEditModal';
import Button from 'src/components/button/Button';
import Toaster from 'src/components/common/Toaster';
import { useRef } from 'react';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const RejectReasons = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'Scope',
			columns: [
				{
					Header: 'Reason',
					resizable: false,
					Cell: (row) => `${row.original.reason}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 400
				}
			]
		},
		{
			Header: 'info',
			fixed: 'right',
			columns: [
				{
					Header: 'Type',
					resizable: false,
					Cell: (row) => `${row.original.type}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					width: 150
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							<img
								src={EditIcon}
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewOrEditModal({
									open: true,
									view: false,
									edit: true,
									data: cell.original
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	useEffect(() => {
		getReasons();
	}, [activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getReasons = async () => {
		try {
			setLoading(true);
			const response = await getApi(REASONS.GET_REASONS, {});
			setLoading(false);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.reasons);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const modalError = (msg) => {
		toaster.current.error(msg);
	};

	return (
		<>
			<SettingsWrapper>
				<PageTitle title="sidebar.rejectReason" />
				<div className="p-0 bg-white">
					<div className="d-flex justify-content-end">
						<Button
							onClick={handleChangeViewOrEditModal({
								open: true,
								view: false,
								edit: false,
								data: null
							})}
							type="submit"
							className="btn form-button"
							style={{ width: '150px' }}>
							Add
						</Button>
					</div>

					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
			</SettingsWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getReasons={getReasons}
					modalError={modalError}
				/>
			)}
			<Toaster ref={toaster} />
		</>
	);
};

export default RejectReasons;
