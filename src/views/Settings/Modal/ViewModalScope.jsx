import React from 'react';
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	Modal<PERSON>ooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Accordion,
	AccordionItem,
	AccordionHeader,
	AccordionBody
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Settings.style';
import ImageViewer from 'src/components/ImageTouchPoint/ImageViewer';

const ViewModalScope = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [imageSrc, setImageSrc] = useState(null);
	const [touchpoints, setTouchpoints] = useState({});
	const [openAccordion, setOpenAccordion] = useState('viewScope_1');

	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleCLoseModal = props.handleChangeViewModal({
		open: false,
		data: null
	});

	// Image getting from image upload
	const handleImageChange = (event) => {
		const file = event.target.files[0];
		const reader = new FileReader();

		reader.onloadend = () => {
			setImageSrc(reader.result);
		};

		if (file) {
			reader.readAsDataURL(file);

			setFieldValue('file_name', event.currentTarget.files[0]);
		}
	};

	const titleFields = Object.entries(touchpoints).map(([key, point]) => (
		<React.Fragment key={key}>
			<tr>
				<td>{point.title + ' (#' + point.touch_point + ')'}</td>
				<td>{point.left}</td>
				<td>{point.top}</td>
			</tr>
		</React.Fragment>
	));

	// Getting coordinates on image click
	const handleImageClick = (event) => {};

	useEffect(() => {
		const data = props.data?.scope_image?.touch_points;
		if (data) {
			const renamedData = data.map((item) => ({
				...item,
				left: item.x_coordinate,
				top: item.y_coordinate
			}));
			setTouchpoints(renamedData);
		}
	}, [props.data?.scope_image?.touch_points]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Scope</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<Accordion open={openAccordion} toggle={toggleAccordion}>
							<AccordionItem>
								<AccordionHeader targetId="viewScope_1">
									Scope Details
								</AccordionHeader>
								<AccordionBody accordionId="viewScope_1">
									<div>
										{props.data.name && (
											<div className="mb-10 mt-10">
												<strong>Name:</strong> {props.data.name}
											</div>
										)}
										{props.data.model && (
											<div className="mb-10 mt-10">
												<strong>Model:</strong> {props.data.model}
											</div>
										)}
										{props.data.source && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Source:</strong> {props.data.source}
											</div>
										)}
									</div>
								</AccordionBody>
							</AccordionItem>
							{props?.data?.scope_image && (
								<AccordionItem>
									<AccordionHeader targetId="viewScope_2">
										Image Details
									</AccordionHeader>
									<AccordionBody accordionId="viewScope_2">
										<div>
											<div className="mb-0 mt-10 text-capitalize">
												<div className="d-flex justify-content-center">
													<ImageViewer
														imageSrc={
															props?.data?.scope_image?.image_url
														}
														onImageClick={handleImageClick}
														touchpoints={touchpoints}
														cursor_indication={true}
													/>
												</div>
												{Object.keys(touchpoints).length > 0 ? (
													<div className="w-100">
														<div className="mt-20">
															<table className="table table-bordered mb-0">
																<thead>
																	<tr>
																		<th className="width-number">
																			Title
																		</th>
																		<th className="width-common">
																			Left Coordinate
																		</th>
																		<th className="width-common">
																			Top Coordinate
																		</th>
																	</tr>
																</thead>
																<tbody>{titleFields}</tbody>
															</table>
														</div>
													</div>
												) : null}
											</div>
										</div>
									</AccordionBody>
								</AccordionItem>
							)}
						</Accordion>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="button"
							onClick={handleCLoseModal}
							className="btn form-button c-secondary">
							Cancel
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalScope;
