/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import React from 'react';
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Settings.style';
import ImageViewer from 'src/components/ImageTouchPoint/ImageViewer';
import deleteIcon from '../../../assets/images/reject.svg';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { supportedFormats } from 'src/helper/constant';

const AddOrEditModalScope = (props) => {
	const ZOHO = 'ZOHO';
	const [saveLoading, setSaveLoading] = useState(false);
	const [imageSrc, setImageSrc] = useState(null);
	const [touchpoints, setTouchpoints] = useState({});

	const handleCLoseModal = props.handleChangeViewOrEditModal({
		open: false,
		data: null
	});

	// Form initial values
	const initialValues = {
		modal: '',
		name: '',
		file_name: null,
		...Object.keys(touchpoints).reduce((acc, key) => {
			acc[`title_${key}`] = '';
			return acc;
		}, {})
	};

	// Dynamic validation for title
	const titleValidation = (key) => Yup.string().required(`Required`);

	const validationSchema = Yup.object().shape({
		modal: Yup.string().required('Scope Model is required'),
		name: Yup.string().required('Scope Name is required'),
		file_name: Yup.mixed()
			.required('Scope Image is required')
			.test('fileSize', 'File size is too large.! (Max upload size is 5 mb)', (value) => {
				// Max file size in bytes (e.g., 5MB)
				const maxFileSize = 5000000;
				return !value || value.size <= maxFileSize;
			})
			.test('fileFormat', 'Unsupported file format!', (value) => {
				return !value || (value && supportedFormats.includes(value.type));
			}),
		...Object.keys(touchpoints).reduce((acc, key) => {
			acc[`title_${key}`] = titleValidation(key);
			return acc;
		}, {})
	});

	const {
		values,
		errors,
		touched,
		handleChange,
		handleSubmit,
		isValid,
		setFieldValue,
		setFieldError
	} = useFormik({
		initialValues,
		validationSchema,
		validateOnChange: true,
		onSubmit: (values, { resetForm }) => {
			handleScope();
		}
	});

	// Form Submit
	const handleScope = async () => {
		try {
			if (Object.entries(touchpoints).length === 0) {
				setFieldError('file_name', 'Please Select at least one touchpoint!');
				return;
			}
			if (isValid) {
				setSaveLoading(true);
				const newArray = Object.entries(touchpoints).map(([key, value]) => {
					const touch_point = parseInt(key.replace('touch_points_', ''), 10);
					const x_coordinate = value.left;
					const y_coordinate = value.top;
					const title = values[`title_${key}`];

					return {
						touch_point,
						x_coordinate,
						y_coordinate,
						title
					};
				});

				props.addScope({
					...(props?.data?.source === ZOHO && { scope_id: props?.data?.id }),
					model: values.modal,
					name: values.name,
					scope_image: values.file_name,
					touch_points: newArray
				});

				handleCLoseModal();
			}
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSaveLoading(false);
		}
	};

	// Image getting from image upload
	const handleImageChange = (event) => {
		const file = event.target.files[0];
		const reader = new FileReader();

		reader.onloadend = () => {
			setImageSrc(reader.result);
		};

		if (file) {
			reader.readAsDataURL(file);

			setFieldValue('file_name', event.currentTarget.files[0]);
		}
	};

	// Getting coordinates on image click
	const handleImageClick = (event) => {
		const offsetX = event.nativeEvent.offsetX;
		const offsetY = event.nativeEvent.offsetY;

		const key = `touch_points_${Object.keys(touchpoints).length + 1}`;

		setTouchpoints((prev) => ({
			...prev,
			[key]: { left: offsetX, top: offsetY }
		}));
	};

	// Touchpoints table shows with X & Y coordinates and title
	const titleFields = Object.entries(touchpoints).map(([key, point]) => (
		<React.Fragment key={key}>
			<tr>
				<td>
					<FormGroup>
						<Input
							id={`title_${key}`}
							name={`title_${key}`}
							value={values[`title_${key}`]}
							onChange={handleChange}
							type="text"
							placeholder={`Title for Touchpoint ${key.replace('touch_points_', '')}`}
						/>
						{errors[`title_${key}`] && (
							<span className="error-msg my-1 ml-8">{errors[`title_${key}`]}</span>
						)}
					</FormGroup>
				</td>
				<td className="align-middle">{point.left}</td>
				<td className="align-middle">{point.top}</td>
				<td className="text-center align-middle">
					<img
						src={deleteIcon}
						alt="delete-icon"
						title="Delete Touchpoint"
						className="cursor-pointer"
						width="20px"
						onClick={() => removeTouchpoint(key)}
					/>
				</td>
			</tr>
		</React.Fragment>
	));

	const removeTouchpoint = (key) => {
		const newTouchpoints = { ...touchpoints };
		delete newTouchpoints[key];

		let index = 1;
		const reorderedTouchpoints = {};
		for (const [touchpointKey, touchpointValue] of Object.entries(newTouchpoints)) {
			reorderedTouchpoints[`touch_points_${index}`] = touchpointValue;
			index++;
		}

		setTouchpoints(reorderedTouchpoints);
	};

	useEffect(() => {
		if (Object.entries(touchpoints).length > 0) {
			setFieldError('file_name', null);
		}
	}, [touchpoints]);

	useEffect(() => {
		if (props?.data && props?.data?.source === ZOHO) {
			setFieldValue('modal', props?.data?.model);
			setFieldValue('name', props?.data?.name);
		}
	}, [props]);

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Scope
				</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<Form>
							<>
								<FormGroup>
									<Label>Scope Name</Label>
									<>
										<Input
											id="name"
											name="name"
											value={values.name}
											onChange={handleChange}
											disabled={props?.data?.source === ZOHO}
											type="text"
											placeholder="Type Scope name..."
										/>
									</>
									{touched.modal && errors.name && (
										<span className="error-msg my-2">{errors.name}</span>
									)}
								</FormGroup>
								<FormGroup className="mt-10">
									<Label>Scope Model</Label>
									<>
										<Input
											id="modal"
											name="modal"
											value={values.modal}
											onChange={handleChange}
											disabled={props?.data?.source === ZOHO}
											type="text"
											placeholder="Type Scope value..."
										/>
									</>
									{touched.modal && errors.modal && (
										<span className="error-msg my-2">{errors.modal}</span>
									)}
								</FormGroup>
								<FormGroup className="mt-10">
									<Label className="d-flex align-items-center">
										Scope Image Upload{' '}
										<small className="ml-auto">
											(Supported formats: png, jpeg, svg, gif.)
										</small>
									</Label>
									<>
										<Input
											id="file_name"
											className="mb-10"
											name="file_name"
											type="file"
											onChange={handleImageChange}
											placeholder="Type Scope value..."
										/>
									</>
									{touched.file_name && errors.file_name && (
										<span className="error-msg mb-8">{errors.file_name}</span>
									)}

									{/* <p className="note py-1 px-3 mb-10">
										Note: Image size should be less than
										<strong className="ms-1">375px x 667px</strong>
									</p> */}

									<div className="d-flex flex-column align-items-center">
										<ImageViewer
											imageSrc={imageSrc}
											onImageClick={handleImageClick}
											touchpoints={touchpoints}
										/>
										{Object.keys(touchpoints).length > 0 ? (
											<div className="w-100">
												<div className="mt-20">
													<table className="table table-bordered mb-0">
														<thead>
															<tr>
																<th className="width-number">
																	Title.
																</th>
																<th className="width-common">
																	Left Coordinate
																</th>
																<th className="width-common">
																	Top Coordinate
																</th>
																<th
																	className="text-center"
																	width="80px">
																	Action
																</th>
															</tr>
														</thead>
														<tbody>{titleFields}</tbody>
													</table>
												</div>
											</div>
										) : null}
									</div>
								</FormGroup>
							</>
						</Form>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						<Button
							type="submit"
							onClick={handleSubmit}
							loading={saveLoading}
							className="btn form-button">
							Save
						</Button>
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalScope;
