/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Settings.style';
import { regexCharactersNumbers } from 'src/helper/constant';

const AddOrEditModalSpace = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		title: ''
	});
	const [formErrors, setFormErrors] = useState({ title: '' });

	const handleCLoseModal = props.handleChangeViewOrEditModal({
		open: false,
		data: null
	});

	useEffect(() => {
		if (props.data) {
			setFormValues(props.data);
		}
	}, [props.data]);

	const handleChange = ({ target }) => {
		const newTitle = target.value;
		setFormValues((prevFormValues) => ({
			...prevFormValues,
			title: newTitle
		}));

		// Validate input on change and set error message
		const errorMessage = validateSpace({ title: newTitle });
		setFormErrors({ title: errorMessage });
	};

	const validateSpace = (value) => {
		const isValid = regexCharactersNumbers.test(value?.title);
		if (!value?.title.trim()) {
			return 'Space is required!';
		}
		return ''; // No error
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessage = validateSpace(formValues);
			setFormErrors({ title: errorMessage });

			if (!errorMessage) {
				if (props.edit) {
					props.editSpace({
						id: props.data.id,
						title: formValues.title
					});
				} else {
					props.addSpace({
						title: formValues.title
					});
				}

				handleCLoseModal();
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Space
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Space Value</Label>
									<>
										<Input
											id="title"
											value={formValues?.title}
											onChange={handleChange}
											type="text"
											placeholder="Type Space value..."
										/>
									</>
									<span className="error-msg mt-10">{formErrors.title}</span>
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button
								disabled={saveLoading}
								type="submit"
								className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							{props.data && props.edit && (
								<Button
									onClick={handleCLoseModal}
									loading={saveLoading}
									type="submit"
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModalSpace;
