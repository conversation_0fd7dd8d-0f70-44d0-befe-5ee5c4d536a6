/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { Input, ModalBody, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { PROFILE_STATUS } from 'src/helper/constant';
import { AddOrEditModalWrapper } from '../Settings.style';
import { REASONS } from 'src/helper/api/endPoint';
import { Api, postApi } from 'src/helper/api/Api';
import CODES from 'src/helper/StatusCodes';

const AddOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({
		reason: '',
		type: ''
	});
	const [formErrors, setFormErrors] = useState({ reason: '' });

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const [dropdownOpen, setDropdownOpen] = useState(false);

	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	useEffect(() => {
		if (props.data) {
			setFormValues(props.data);
		}
	}, [props.data]);

	const handleChange = ({ target }) => {
		const newReason = target.value;
		const types = structuredClone(formValues);
		types.reason = newReason;
		setFormValues(types);
		const errorMessage = validateReason(types);
		setFormErrors((prevForms) => ({ ...prevForms, reason: errorMessage }));
	};

	const handleChangeType = (value) => {
		const formValue = structuredClone(formValues);
		formValue.type = value;
		setFormValues(formValue);

		const errorMessage = validateReasonType(formValue);
		setFormErrors((prevForms) => ({ ...prevForms, type: errorMessage }));
	};

	const validateReason = (value) => {
		if (!value?.reason.trim()) {
			return 'Reason is required!';
		}
		return ''; // No error
	};

	const validateReasonType = (value) => {
		if (!value?.type.trim()) {
			return 'Reason Type is required!';
		}
		return ''; // No error
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessageReason = validateReason(formValues);
			const errorMessageType = validateReasonType(formValues);

			setFormErrors({ reason: errorMessageReason, type: errorMessageType });

			if (!errorMessageReason && !errorMessageType) {
				const obj = {
					reason: formValues.reason.trim()
				};
				if (props.edit) {
					obj.reason_id = props.data.id;
				} else {
					obj.type = formValues.type;
				}
				let response = '';
				if (props.edit) {
					response = await postApi(REASONS.UPDATE, obj);
				} else {
					response = await postApi(REASONS.CREATE, obj);
				}
				if (response?.status === CODES.SUCCESS) {
					handleCLoseModal();
					props.getReasons();
				}
			}
		} catch (error) {
			handleCLoseModal();
			props.modalError(error.response.data.message);
			setSaveLoading(false);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Reject Reason
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Reason</Label>
									<Input
										id="reason"
										value={formValues?.reason}
										onChange={handleChange}
										type="textarea"
										placeholder="Type reject reason..."
									/>
									{formErrors.reason && (
										<span className="error-msg mt-5">{formErrors.reason}</span>
									)}
									<FormGroup
										tag="fieldset"
										className={`mt-10 ${props.edit ? 'pe-none' : ''}`}>
										<Label>Reason Type</Label>
										<div className="ml-10">
											<FormGroup check>
												<Label check>
													<Input
														type="radio"
														name="radio1"
														value={'Profile'}
														checked={formValues?.type === 'Profile'}
														onChange={() => handleChangeType('Profile')}
													/>{' '}
													Profile
												</Label>
											</FormGroup>
											<FormGroup check>
												<Label check>
													<Input
														type="radio"
														name="radio1"
														value={'Address'}
														checked={formValues?.type === 'Address'}
														onChange={() => handleChangeType('Address')}
													/>{' '}
													Address
												</Label>
											</FormGroup>
										</div>
									</FormGroup>
									{formErrors.type && (
										<span className="error-msg mt-5">{formErrors.type}</span>
									)}
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button
								// onClick={handleCLoseModal}
								disabled={saveLoading}
								type="submit"
								className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							{props.data && props.edit && (
								<Button
									onClick={handleCLoseModal}
									loading={saveLoading}
									type="submit"
									className="btn form-button c-secondary">
									Cancel
								</Button>
							)}
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditModal;
