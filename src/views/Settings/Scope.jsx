/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import SettingsWrapper from './Settings.style';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { SCOPE_TAB, TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import { getApi, postApi } from 'src/helper/api/Api';
import { SCOPES } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Button from 'src/components/button/Button';
import AddOrEditModalScope from './Modal/EditModalScope';
import useDebounce from 'src/util/hooks/useDebounce';
import Switch from 'src/components/Switch/Switch'; // Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import ViewIcon from '../../assets/images/View.svg';
import AddIcon from '../../assets/images/add.svg';
import ViewModalScope from './Modal/ViewModalScope';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const Scope = () => {
	const CMS = 'CMS';
	const ZOHO = 'ZOHO';
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [activeTab, setActiveTab] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'Scope',
			columns: [
				{
					Header: 'Scope Name',
					resizable: false,
					Cell: (row) => `${row.original.name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 250
				},
				{
					Header: 'Scope Model',
					resizable: false,
					Cell: (row) => `${row.original.model}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					minWidth: 350
				}
			]
		},
		{
			Header: 'info',
			fixed: 'right',
			columns: [
				{
					Header: 'Active',
					width: 100,
					Cell: (cell) => (
						<Switch
							checked={cell.original.is_active}
							onChange={() =>
								handleChangeActiveInactive(
									cell.original.id,
									!cell.original.is_active
								)
							}
						/>
					),
					headerClassName: 'text-center pa-20',
					className: 'text-center pa-20 d-flex align-items-center'
				},
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							{activeTab !== 1 ? (
								<img
									src={AddIcon}
									alt="AddIcon"
									title="Add"
									width={18}
									className="mr-10 cursor-pointer"
									onClick={handleChangeViewOrEditModal({
										open: true,
										view: false,
										edit: false,
										data: cell.original
									})}
								/>
							) : null}
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className={`cursor-pointer ${activeTab !== 1 ? 'mr-0' : 'mr-10'}`}
								onClick={handleChangeViewModal({
									open: true,
									data: cell.original,
									view: true
								})}
							/>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 100
				}
			]
		}
	];

	const toggle = (tab) => {
		if (activeTab !== tab) {
			setActivePage(1);
			setActiveTab(tab);
		}
	};

	const handleChangeViewModal = (params) => () => setViewModalData(params);

	const handleChangeActiveInactive = async (id, is_active) => {
		const index = data.findIndex((value) => value.id === id);

		if (index !== -1) {
			try {
				setLoading(true);
				const response = await postApi(
					SCOPES.CHANGE_STATUS,
					{
						id: id,
						status: is_active ? 1 : 0
					},
					'order'
				);

				if (response.status === CODES.SUCCESS) {
					setData((prev) => {
						const copyPrev = [...prev];

						copyPrev[index].is_active = is_active;

						return copyPrev;
					});

					toaster.current.success(response.data.message);
				}
			} catch (error) {
				toaster.current.error(error?.response?.data?.message);
			} finally {
				setLoading(false);
			}
		}
	};

	useEffect(() => {
		getScope(activeTab);
	}, [debounceSearch, activePage, activeTab]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const getScope = async (tabId) => {
		try {
			setLoading(true);
			const dataToSend = {
				source: tabId === 1 ? CMS : ZOHO,
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(
				SCOPES.GET_SCOPES,
				{
					...dataToSend
				},
				'order'
			);
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.scopes);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const addScope = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(SCOPES.ADD, data, 'order', true);
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getScope();
			}
		} catch (error) {
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};
	return (
		<>
			<SettingsWrapper>
				<PageTitle
					title="sidebar.scope"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mt-0 mb-15">
						<div className="roe-card-body">
							<Nav tabs className="mb-10">
								{SCOPE_TAB.map((role) => (
									<NavItem key={role.id} className="cursor-pointer">
										<NavLink
											className={`${activeTab === role.id ? 'active' : ''}`}
											onClick={() => toggle(role.id)}>
											{role.title}
										</NavLink>
									</NavItem>
								))}
							</Nav>
							<TabContent activeTab={activeTab}>
								{SCOPE_TAB.map((role) => (
									<TabPane key={role.id} tabId={role.id}>
										{role.title === CMS ? (
											<div className="d-flex justify-content-end mb-10">
												<Button
													onClick={handleChangeViewOrEditModal({
														open: true,
														view: false,
														edit: false,
														data: null
													})}
													type="submit"
													className="btn form-button"
													style={{ width: '150px' }}>
													Add
												</Button>
											</div>
										) : null}
										<ReactTableFixedColumns
											manual
											data={data}
											pages={pages}
											sortable={false}
											columns={columns}
											page={activePage - 1}
											onPageChange={handleChangePage}
											totalCount={count}
											loading={loading}
											pageSize={TABLE.LIMIT}
											minRows={TABLE.MIN_ROW}
											LoadingComponent={Loader}
											PaginationComponent={Pagination}
											style={{ border: 'none', boxShadow: 'none' }}
											className="-striped -highlight custom-react-table-theme-class"
											defaultFilterMethod={(filter, row) => {
												const id = filter.pivotId || filter.id;
												return row[id] !== undefined
													? String(row[id].toLowerCase()).includes(
															filter.value.toLowerCase()
													  )
													: true;
											}}
										/>
									</TabPane>
								))}
							</TabContent>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</SettingsWrapper>
			{addOrEditModalData.open && (
				<AddOrEditModalScope
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					addScope={addScope}
				/>
			)}
			{viewModalData.open && (
				<ViewModalScope {...viewModalData} handleChangeViewModal={handleChangeViewModal} />
			)}
		</>
	);
};

export default Scope;
