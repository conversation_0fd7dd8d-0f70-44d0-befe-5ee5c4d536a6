//CORE
import React, { useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';

//CUSTOM
import PageTitle from '../../components/common/PageTitle';
import { getApi, postApi } from '../../helper/api/Api';
import { ADDRESS, ROLES, USER } from '../../helper/api/endPoint';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import CODES from '../../helper/StatusCodes';
import Loader from '../../components/common/Loader';
import Pagination from '../../components/Pagination/Pagination';
import AddOrEditModal from './Modals/AddOrEditModal';
import Toaster from 'src/components/common/Toaster';
import ViewIcon from '../../assets/images/View.svg';
import ApproveIcon from '../../assets/images/approve.svg';
import RejectIcon from '../../assets/images/reject.svg';
import Swal from 'sweetalert2';
import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';
import { CONFIRM_APPROVE_POPUP } from 'src/components/header/constants';
import { convertTimeToLocal } from 'src/helper/functions';

// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import { AddressApproveWrapper } from './AddressApprove.style';
import useDebounce from 'src/util/hooks/useDebounce';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const AddressRegistered = () => {
	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [addressData, setAddressData] = useState([]);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [roles, setRoles] = useState([]);
	const [roleId, setRoleId] = useState(undefined);
	const [activeTab, setActiveTab] = useState(undefined);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });

	useEffect(() => {
		getRolesApi();
	}, []); // eslint-disable-line

	useEffect(() => {
		if (roleId) getAddressData(roleId);
	}, [debounceSearch, activePage]);

	const toggle = (tab) => {
		if (activeTab !== tab) {
			getAddressData(tab);
			setActivePage(1);
			setActiveTab(tab);
		}
	};

	const getRolesApi = () => {
		getApi(ROLES.GET, { scope: 'APP', type: 'CHILD' })
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.roles;

					if (apiData?.length) {
						setRoles(apiData);
						setActiveTab(apiData?.[0]?.id);

						getAddressData(apiData?.[0]?.id);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const getAddressData = async (role_id) => {
		try {
			setLoading(true);
			setRoleId(role_id);

			const params = {
				role_id,
				address_status: PROFILE_STATUS.PENDING,
				page: activePage,
				limit: TABLE.LIMIT
			};

			if (debounceSearch.trim()) {
				(params.page = 1), (params.search = debounceSearch);
			}

			const response = await getApi(ADDRESS.GET, params);

			if (response.status === CODES.SUCCESS) {
				setAddressData(response?.data?.data?.address);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setAddressData([]);
			setPages(1);
			setCount(0);
		} finally {
			setLoading(false);
		}
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleApprovedUser = (data) => {
		let params = {
			address_id: data.id,
			user_id: data.address_user.user_role.user_id,
			role_id: data.address_user.user_role.Role.id,
			address_status: PROFILE_STATUS.APPROVED
		};
		Swal.fire(CONFIRM_APPROVE_POPUP).then((result) => {
			if (result.isConfirmed) {
				postApi(ADDRESS.APPROVE, { ...params })
					.then((response) => {
						if (response.data.status) {
							getAddressData(roleId);
							toaster.current.success(response.data.message);
						}
					})
					.catch((error) => {
						toaster.current.error(error);
					});
				return;
			}
		});
	};
	const handleRejectUser = async (data) => {
		try {
			const response = await postApi(ADDRESS.APPROVE, { ...data });
			if (response.data.status) {
				getAddressData(roleId);
				toaster.current.success(response.data.message);
			}
		} catch (error) {
			console.log(error);
			if (error?.response.status !== CODES.NOT_FOUND) {
				toaster.current.error(error.response.data.message);
			}
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'address_user.profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.address_user.profile_image_url
										? cell?.original?.address_user.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={
									!cell?.original?.address_user.profile_image_url &&
									'No profile image'
								}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) =>
						`${row.original.address_user.first_name} ${row.original.address_user.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'address_user.mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: true,
									profile_status: PROFILE_STATUS.PENDING
								})}
							/>
							<img
								src={ApproveIcon}
								alt="ApproveIcon"
								title="Approve"
								className="mr-10 cursor-pointer"
								width={22}
								onClick={() => handleApprovedUser(cell.original)}
							/>

							<img
								src={RejectIcon}
								alt="RejectIcon"
								title="Reject"
								className="cursor-pointer"
								width={20}
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: false
								})}
							/>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];

	return (
		<AddressApproveWrapper>
			<PageTitle
				title="sidebar.registerUser"
				className="plr-0"
				search={true}
				searchKey={searchKey}
				setSearchKey={setSearchKey}
			/>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-body">
						<Loader loading={!roles.length > 0} />
						<Nav tabs className="mb-10">
							{roles.map((role) => (
								<NavItem key={role.id} className="cursor-pointer">
									<NavLink
										className={`${activeTab === role.id ? 'active' : ''}`}
										onClick={() => toggle(role.id)}>
										{role.name}
									</NavLink>
								</NavItem>
							))}
						</Nav>
						<TabContent activeTab={activeTab}>
							{roles.map((role) => (
								<TabPane key={role.id} tabId={role.id}>
									<ReactTableFixedColumns
										manual
										sortable={false}
										data={addressData}
										pages={pages}
										columns={columns}
										page={activePage - 1}
										onPageChange={handleChangePage}
										totalCount={count}
										loading={loading}
										pageSize={TABLE.LIMIT}
										minRows={TABLE.MIN_ROW}
										LoadingComponent={Loader}
										PaginationComponent={Pagination}
										style={{ border: 'none', boxShadow: 'none' }}
										className="-striped -highlight custom-react-table-theme-class"
									/>
								</TabPane>
							))}
						</TabContent>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
					handleRejectUser={handleRejectUser}
				/>
			)}
		</AddressApproveWrapper>
	);
};

export default AddressRegistered;
