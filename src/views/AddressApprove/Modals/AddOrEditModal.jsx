/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	DropdownItem,
	DropdownMenu,
	DropdownToggle,
	Dropdown
} from 'reactstrap';
import Button from '../../../components/button/Button';
import { PROFILE_STATUS, REASON_OTHER, TABLE } from 'src/helper/constant';
import { getApi } from 'src/helper/api/Api';
import { ADDRESS, REASONS } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import ImageViewer from 'react-simple-image-viewer';
import { AddOrEditModalWrapper } from '../AddressApprove.style';
const AddOrEditModal = (props) => {
	const [saveLoading, setSaveLoading] = useState(false);
	const [formValues, setFormValues] = useState({ reason: '', id: '', type: '' });
	const [formErrors, setFormErrors] = useState({ reason: '' });

	const handleCLoseModal = props.handleChangeAddOrEditModal({ open: false, data: null });
	const [allReasons, setAllReasons] = useState([]);
	const [imageViewer, setImageViewer] = useState({ open: false, images: [] });
	const [dropdownOpen, setDropdownOpen] = useState(false);
	const [oldAddress, setOldAddress] = useState([]);
	const toggleRoleDropDown = () => setDropdownOpen((prev) => !prev);

	const getReasons = async () => {
		try {
			const response = await getApi(REASONS.GET_REASONS, { type: 'Address' });
			if (response?.status === CODES.SUCCESS) {
				const data = response?.data?.data?.reasons;
				setAllReasons(data);
			}
		} catch (error) {
			setAllReasons([]);
		}
	};

	useEffect(() => {
		if (!props.view) {
			getReasons();
		} else {
			if (props.profile_status !== PROFILE_STATUS.APPROVED) {
				getOldAddressData();
			}
		}
	}, [props.data]);

	const handleChange = ({ target }) => {
		const newReason = target.value;
		const reasonObj = structuredClone(formValues);
		reasonObj.reason = newReason;
		setFormValues(reasonObj);

		const errorMessage = validateReason(newReason);
		setFormErrors({ reason: errorMessage });
	};
	const handleChangeReason = (value) => {
		const formValue = structuredClone(formValues);
		formValue.id = value.id;
		formValue.type = value.type;
		formValue.reason = value.type === REASON_OTHER ? '' : value.reason;
		setFormValues(formValue);
	};

	const validateReason = (reason) => {
		if (!reason.trim()) {
			return 'Reason is required!';
		}
		return ''; // No error
	};

	const handleChangeImageViewer = (open, images, imageIndex) => () => {
		setImageViewer({ open, images });
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();

			const errorMessage = validateReason(formValues.reason);
			setFormErrors({ reason: errorMessage });

			if (!errorMessage) {
				handleCLoseModal();

				const obj = {
					address_id: props.data.id,
					user_id: props.data.address_user.user_role.user_id,
					role_id: props.data.address_user.user_role.Role.id,
					address_status: PROFILE_STATUS.REJECTED,
					reason_id: formValues.id
				};

				if (formValues.type === REASON_OTHER) {
					obj.reason = formValues.reason;
				}

				props.handleRejectUser(obj);
			}
		} catch (error) {
			setSaveLoading(false);
		}
	};

	const getOldAddressData = async () => {
		try {
			const params = {
				address_status: PROFILE_STATUS.APPROVED,
				user_id: props.data.user_id,
				page: 1,
				limit: TABLE.LIMIT
			};

			const response = await getApi(ADDRESS.GET, { ...params });

			if (response.status === CODES.SUCCESS) {
				setOldAddress(response.data?.data?.address[0]);
			}
		} catch (error) {
			console.log(error);
			setOldAddress([]);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>
					{props.view ? 'View' : 'Reject'} User Address Approval
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{props.data && props.view && (
							<>
								<div className="px-2">
									{props.data.address_user.profile_image_url && (
										<div className="mb-10 d-flex flex-column">
											<strong className="mb-10">Profile image:</strong>{' '}
											<img
												className="profile-img-wrapper cursor-pointer"
												src={props.data.address_user.profile_image_url}
												alt={props.data.address_user.profile_image_key}
												onClick={handleChangeImageViewer(
													true,
													[props.data.address_user.profile_image_url],
													0
												)}
											/>
										</div>
									)}
									{props.data.address_user.first_name &&
										props.data.address_user.last_name && (
											<div className="mb-10 mt-10 text-capitalize">
												<strong>Name:</strong>{' '}
												{props.data.address_user.first_name +
													' ' +
													props.data.address_user.last_name}
											</div>
										)}
									{props.data.address_user.email && (
										<div className="mb-10 mt-10">
											<strong>Email:</strong> {props.data.address_user.email}
										</div>
									)}

									{props.data.address_user.mobile && (
										<div className="mb-10 mt-10">
											<strong>Mobile:</strong>{' '}
											{props.data?.address_user.country_code}{' '}
											{props.data.address_user.mobile}
										</div>
									)}

									{props.data.reason && (
										<div className="mb-10 mt-10">
											<strong>Reason:</strong> {props.data.reason}
										</div>
									)}

									{!props.data.reason && props.data.address_reject_reason && (
										<div className="mb-10 mt-10">
											<strong>Reason:</strong>{' '}
											{props.data.address_reject_reason.reason}
										</div>
									)}
								</div>
								{Object.keys(oldAddress).length ? (
									<>
										<hr className="mt-12 mb-4" />
										<p>
											<strong>Current Address</strong>
										</p>
										<hr className="mt-4 mb-12" />
									</>
								) : null}
								<div className="px-2">
									{oldAddress.city && (
										<div className="mb-10 mt-10">
											<strong>City:</strong> {oldAddress.city}
										</div>
									)}
									{oldAddress.state && (
										<div className="mb-10 mt-10">
											<strong>State:</strong> {oldAddress.state}
										</div>
									)}
									{oldAddress.zone && (
										<div className="mb-10 mt-10">
											<strong>Zone:</strong> {oldAddress.zone.name}
										</div>
									)}
									{oldAddress.zipcode && (
										<div>
											<strong>Pin Code:</strong> {oldAddress.zipcode}
										</div>
									)}
								</div>
								<hr className="mt-12 mb-4" />
								{(() => {
									switch (String(props.profile_status)) {
										case '0':
											return (
												<p>
													<strong>Rejected Address</strong>
												</p>
											);

										case '2':
											return (
												<p>
													<strong>Requested Address</strong>
												</p>
											);

										default:
											return (
												<p>
													<strong>Address</strong>
												</p>
											);
									}
								})()}

								<hr className="mt-4 mb-12" />
								<div className="px-2">
									{props.data.city && (
										<div className="mb-10 mt-10">
											<strong>City:</strong> {props.data.city}
										</div>
									)}
									{props.data.state && (
										<div className="mb-10 mt-10">
											<strong>State:</strong> {props.data.state}
										</div>
									)}
									{props.data.zone && (
										<div className="mb-10 mt-10">
											<strong>Zone:</strong> {props.data.zone.name}
										</div>
									)}
									{props.data.zipcode && (
										<div>
											<strong>Pin Code:</strong> {props.data.zipcode}
										</div>
									)}
								</div>
							</>
						)}

						{props.data && !props.view && (
							<>
								<FormGroup>
									<Label>Reason</Label>
									<Dropdown
										className="mt-10 mb-4"
										isOpen={dropdownOpen}
										toggle={toggleRoleDropDown}>
										<DropdownToggle caret className="roleBtn text-truncate">
											{formValues.type === REASON_OTHER
												? 'Other'
												: formValues.reason || 'Select Reason'}
										</DropdownToggle>

										<DropdownMenu className="w100" style={{ maxWidth: '50%' }}>
											{allReasons.length ? (
												allReasons.map((role, index) => {
													return (
														<DropdownItem
															key={index}
															onClick={() => {
																handleChangeReason(role);
															}}
															className="text-truncate">
															{role.reason}
														</DropdownItem>
													);
												})
											) : (
												<DropdownItem disabled> No Data </DropdownItem>
											)}
										</DropdownMenu>
									</Dropdown>
									{formValues.type === REASON_OTHER && (
										<>
											<Input
												id="reason"
												value={formValues.reason}
												onChange={handleChange}
												type="textarea"
												placeholder="Type reject reason..."
											/>
										</>
									)}
									<span className="error-msg mt-10">{formErrors.reason}</span>
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							{props.data && !props.view && (
								<Button
									loading={saveLoading}
									type="submit"
									className="btn form-button">
									Reject
								</Button>
							)}
						</div>

						<div>
							<Button
								onClick={handleCLoseModal}
								disabled={saveLoading}
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
			{imageViewer.open && (
				<ImageViewer
					disableScroll={false}
					src={imageViewer.images}
					closeOnClickOutside={true}
					onClose={handleChangeImageViewer(false, [], 0)}
					backgroundStyle={{ backgroundColor: 'rgba(0,0,0,0.9)', zIndex: 9999 }}
				/>
			)}
		</>
	);
};

export default AddOrEditModal;
