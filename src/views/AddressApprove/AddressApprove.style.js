import { Modal } from 'reactstrap';
import styled from 'styled-components';

export const AddressApproveWrapper = styled.div`
	.action-btn {
		/* width: 100px; */
		display: flex;
		gap: 5px;
		.btn {
			min-width: fit-content;
			min-height: auto;
		}
	}

	.card {
		min-height: calc(100vh - 260px);
	}

	.Table__pagination {
		display: flex;
		justify-content: flex-end;
		padding: 20px 10px 0px;
	}

	.Table__pageButton {
		font-size: 18px;
		outline: none;
		border: none;
		background-color: transparent;
		cursor: pointer;
		color: #757575 !important;
		margin: 0 5px;
	}

	.Table__pageButton:disabled {
		cursor: not-allowed;
		color: gray;
	}

	.Table__pageButton--active {
		font-weight: bold;
		width: 30px;
		height: 30px;
		border-radius: 6px;
	}
	.rt-thead,
	.rt-th {
		background-color: #1cb4e3 !important;
		color: #fff;
	}
	.rt-thead.-headerGroups {
		display: none;
	}
	.-padRow.-even {
		display: none;
	}
`;

export const AddOrEditModalWrapper = styled(Modal)`
	.skills-chip-container {
		gap: 15px;
		display: flex;
		flex-wrap: wrap;
		margin-top: 15px;
		align-items: center;

		.skills-chip {
			padding: 5px 10px;
			border-radius: 10px;
			border: 0.5px solid #000;

			.close-icon {
				cursor: pointer;
				margin-left: 5px;
			}
		}
	}
	.note {
		padding: 10px 15px;
		border-radius: 5px;
		font-size: 14px;
		background-color: #fff2f0;
		border: 1px solid #ffccc7;
	}
	.profile-img-wrapper {
		height: 150px;
		width: fit-content;
	}
	.roleBtn {
		display: flex;
		box-shadow: none !important;
		align-items: center;
		border-radius: 6px;
		width: 50%;
		background: #8dc968 !important;
		border: #8dc968 !important;
		height: calc(1.5em + 1rem);
		justify-content: space-between;
	}
`;
