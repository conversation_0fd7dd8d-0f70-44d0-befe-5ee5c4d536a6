//CORE
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactTable from 'react-table';
import { Nav, NavItem, NavLink, TabContent, TabPane } from 'reactstrap';

//CUSTOM
import { AddressApproveWrapper } from './AddressApprove.style';
import PageTitle from '../../components/common/PageTitle';
import { Api, getApi, postApi } from '../../helper/api/Api';
import { ADDRESS, PROFILE, ROLES, USER } from '../../helper/api/endPoint';
import { PROFILE_STATUS, TABLE } from '../../helper/constant';
import CODES from '../../helper/StatusCodes';
import Loader from '../../components/common/Loader';
import Pagination from '../../components/Pagination/Pagination';
import ViewIcon from '../../assets/images/View.svg';
import AddOrEditModal from './Modals/AddOrEditModal';
import useDebounce from 'src/util/hooks/useDebounce';
import Toaster from 'src/components/common/Toaster';

import dummyProfilePic from '../../assets/images/dummy-profile-pic.png';

// Import React Table HOC Fixed columns
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import { convertTimeToLocal } from 'src/helper/functions';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const AddressApproved = () => {
	const toaster = useRef();
	const [loading, setLoading] = useState(false);
	const [addressData, setAddressData] = useState([]);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [roles, setRoles] = useState([]);
	const [roleId, setRoleId] = useState(undefined);
	const [activePage, setActivePage] = useState(1);
	const [activeTab, setActiveTab] = useState(undefined);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({ open: false, data: null });

	useEffect(() => {
		getRolesApi();
	}, []); // eslint-disable-line

	useEffect(() => {
		if (roleId) getAddressData(roleId);
	}, [debounceSearch, activePage]);

	const toggle = (tab) => {
		if (activeTab !== tab) {
			getAddressData(tab);
			setActivePage(1);
			setActiveTab(tab);
		}
	};

	const getRolesApi = () => {
		getApi(ROLES.GET, { scope: 'APP', type: 'CHILD' })
			.then((response) => {
				if (response.status === CODES.SUCCESS) {
					const apiData = response.data?.data?.roles;

					if (apiData?.length) {
						setRoles(apiData);
						setActiveTab(apiData?.[0]?.id);

						getAddressData(apiData?.[0]?.id);
					}
				}
			})
			.catch((error) => {
				console.log(error);
				toaster.current.error(error?.response?.data?.message);
			});
	};

	const getAddressData = async (role_id) => {
		try {
			setLoading(true);
			setRoleId(role_id);

			const params = {
				role_id,
				address_status: PROFILE_STATUS.APPROVED,
				page: activePage,
				limit: TABLE.LIMIT
			};

			if (debounceSearch.trim()) {
				(params.page = 1), (params.search = debounceSearch);
			}

			const response = await getApi(ADDRESS.GET, params);

			if (response.status === CODES.SUCCESS) {
				setAddressData(response?.data?.data?.address);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setAddressData([]);
			setPages(1);
			setCount(0);
		} finally {
			setLoading(false);
		}
	};

	const handleChangeAddOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	const columns = [
		{
			Header: 'Profile Image',
			fixed: 'left',
			columns: [
				{
					Header: 'Profile Image',
					accessor: 'address_user.profile_image_url',
					width: 140,
					headerClassName: 'pa-20 text-center',
					className: 'pa-10 text-center',
					Cell: (cell) => (
						<>
							<img
								src={
									cell?.original?.address_user.profile_image_url
										? cell?.original?.address_user.profile_image_url
										: dummyProfilePic
								}
								className="avatar avatar-lg"
								style={{ height: '60px', width: '60px', borderRadius: '50%' }}
								title={
									!cell?.original?.address_user.profile_image_url &&
									'No profile image'
								}
							/>
						</>
					)
				}
			]
		},
		{
			Header: 'info',
			columns: [
				{
					Header: 'Full Name',
					minWidth: 150,
					resizable: false,
					Cell: (row) =>
						`${row.original.address_user.first_name} ${row.original.address_user.last_name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center text-capitalize'
				},
				{
					Header: 'Mobile Number',
					minWidth: 160,
					accessor: 'address_user.mobile',
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					Cell: ({ value }) => (value ? value : '-')
				}
			]
		},
		{
			Header: 'action',
			fixed: 'right',
			columns: [
				{
					Header: 'Action',
					Cell: (cell) => (
						<>
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeAddOrEditModal({
									open: true,
									data: cell.original,
									view: true,
									profile_status: PROFILE_STATUS.APPROVED
								})}
							/>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					maxWidth: 400,
					minWidth: 125,
					width: 125
				}
			]
		}
	];

	return (
		<AddressApproveWrapper>
			<PageTitle
				title="sidebar.verifiedUser"
				className="plr-0"
				search={true}
				searchKey={searchKey}
				setSearchKey={setSearchKey}
			/>
			<div className="plr-0">
				<div className="roe-card-style mtb-15">
					<div className="roe-card-body">
						<Loader loading={!roles.length > 0} />
						<Nav tabs className="mb-10">
							{roles.map((role) => (
								<NavItem key={role.id} className="cursor-pointer">
									<NavLink
										className={`${activeTab === role.id ? 'active' : ''}`}
										onClick={() => toggle(role.id)}>
										{role.name}
									</NavLink>
								</NavItem>
							))}
						</Nav>
						<TabContent activeTab={activeTab}>
							{roles.map((role) => (
								<TabPane key={role.id} tabId={role.id}>
									<ReactTableFixedColumns
										manual
										sortable={false}
										data={addressData}
										pages={pages}
										columns={columns}
										page={activePage - 1}
										onPageChange={handleChangePage}
										totalCount={count}
										loading={loading}
										pageSize={TABLE.LIMIT}
										minRows={TABLE.MIN_ROW}
										LoadingComponent={Loader}
										PaginationComponent={Pagination}
										style={{ border: 'none', boxShadow: 'none' }}
										className="-striped -highlight custom-react-table-theme-class"
									/>
								</TabPane>
							))}
						</TabContent>
					</div>
				</div>
			</div>
			{addOrEditModalData.open && (
				<AddOrEditModal
					{...addOrEditModalData}
					handleChangeAddOrEditModal={handleChangeAddOrEditModal}
				/>
			)}
			<Toaster ref={toaster} />
		</AddressApproveWrapper>
	);
};

export default AddressApproved;
