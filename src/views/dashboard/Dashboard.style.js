import styled from 'styled-components';

const DashboardWrapper = styled.div`
	.card {
		width: 230px;
		min-height: 200px;
		padding: 24px;
		background-color: #fff;
		box-shadow: 0px 4px 18px 0px #4b465c1a;
		border: unset;
		border-radius: 10px;
		transition: 0.5 all;
		.name {
			font-size: 18px;
			font-weight: 500;
			line-height: 24px;
			letter-spacing: 0px;
			text-align: left;
			width: fit-content;
			color: #4b465c;
			vertical-align: middle;
		}
		.count {
			font-size: 50px;
			letter-spacing: 0px;
			padding: 10px;
			width: 150px;
			min-width: 150px;
			height: 100px;
			background: #1cb4e329;
			border-radius: 12px;
			margin: auto;
			font-weight: bold;
			line-height: normal;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.total-count {
			font-size: 13px;
			font-weight: 400;
			line-height: 20px;
			letter-spacing: 0px;
			text-align: left;
			margin-bottom: 15px;
		}
		.image-card {
			img {
				width: 30px;
				height: 30px;
				margin-right: 10px;
			}
		}
		&.bg-1 {
			background-color: #f6d78b;
		}
		&.bg-2 {
			background-color: #eebfac;
		}
		&.bg-3 {
			background-color: #ffa2df;
		}
		&.bg-4 {
			background-color: #06e2ff;
		}
		&.bg-5 {
			background-color: #a7fac2;
		}
		.zone {
			padding: 3px 5px;
			background-color: #fff9e4;
			color: #ff8463;
			font-size: 9px;
			border-radius: 6px;
			line-height: normal;
		}
	}
	.card-text {
		font-size: 14px;
		font-weight: 500;
		color: #737373;
	}
	.card-count {
		font-size: 28px;
		font-weight: 600;
		color: #000;
	}
	.border-bottom-1 {
		padding-bottom: 3px;
		border-bottom: 1px solid #efefef;
	}
	.main-heading {
		margin-left: 30px;
		padding-top: 4px;
		font-size: 16px;
		font-weight: 600;
		position: relative;
		padding-bottom: 5px;
		border-bottom: 2px solid #efefef;
		&::before {
			content: '';
			position: absolute;
			left: -30px;
			top: 0;
			width: 15px;
			height: 30px;
			border-radius: 5px;
			background-color: #cabdff;
		}
	}
	.pills {
		background: #efefef;
		padding: 9px 10px;
		border-radius: 10px;
		display: flex;
		width: fit-content;
		gap: 0px;
		position: relative;
		max-width: 100%;
		span {
			padding: 4px 15px;
			border-radius: 8px;
			font-size: 14px;
			font-weight: 600;
			cursor: pointer;
			transition: 0.3s all;
			position: relative;
			z-index: 1;
			&.active {
				color: #fff;
				transition: 0.3s all;
			}
		}
		.active-slider {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 6px;
			margin: auto;
			height: calc(100% - 10px);
			width: calc(100% / 4 - 5px);
			padding: 4px 15px;
			border-radius: 8px;
			background-color: #2bafe5;
			transition: 0.3s all;
			&.East {
				left: 6px;
			}
			&.West {
				left: 69px;
			}
			&.South {
				left: 136px;
			}
			&.North {
				left: 206px;
			}
		}
	}
`;

export default DashboardWrapper;
