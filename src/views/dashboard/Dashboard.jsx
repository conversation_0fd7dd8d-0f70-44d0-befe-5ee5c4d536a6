import React from 'react';
import PageTitle from '../../components/common/PageTitle';
import DashboardWrapper from './Dashboard.style';
import head from 'src/assets/images/head.svg';
import engineer from 'src/assets/images/engineer.svg';
import fabricator from 'src/assets/images/fabricator.svg';
import existing from 'src/assets/images/existing.svg';
import freelancer from 'src/assets/images/freelancer.svg';
import north from 'src/assets/images/north-arrow.svg';
import south from 'src/assets/images/south-arrow.svg';
import west from 'src/assets/images/west-arrow.svg';
import east from 'src/assets/images/east-arrow.svg';
import { useEffect } from 'react';
import { useState } from 'react';
import { getApi } from 'src/helper/api/Api';
import { DASHBOARD } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import Loader from 'src/components/common/Loader';
import CountUp from 'react-countup';
import Toaster from 'src/components/common/Toaster';
import { useRef } from 'react';

const Dashboard = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [pills, setPills] = useState('East');
	const [loading, setLoading] = useState(false);

	const getDashboard = async () => {
		try {
			setLoading(true);
			const response = await getApi(DASHBOARD.GET, {});
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data);
			}
		} catch (error) {
			setData([]);
			setLoading(false);
			toaster.current.error(error.message);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		getDashboard();
	}, []);

	const DataDisplay = ({ data }) => {
		// Organize data by zone_name
		const organizedData = { East: {}, North: {}, South: {}, West: {} };

		// Process data and populate organizedData
		for (const key in data) {
			if (Array.isArray(data[key])) {
				data[key].forEach((item) => {
					const { zone_name, user_count } = item;
					organizedData[zone_name][key] = user_count;
				});
			} else if (typeof data[key] === 'object') {
				for (const subKey in data[key]) {
					data[key][subKey].forEach((item) => {
						const { zone_name, user_count } = item;
						if (!organizedData[zone_name][subKey]) {
							organizedData[zone_name][subKey] = {};
						}
						organizedData[zone_name][subKey][key] = user_count;
					});
				}
			}
		}
		const selectedData = organizedData[pills] || {};
		// Render the organized data
		return (
			<div className="mt-25">
				<div className="d-flex gap-3 flex-wrap">
					<div>
						<p className="mb-15 mt-0 main-heading">Internal Team</p>
						<div className="d-flex gap-3">
							<div className={`card`}>
								<div className="d-flex align-items-center justify-content-between h-100 w-100">
									<div className=" h-100 w-100">
										<p className="name">Service Head</p>
										<p className="mt-auto total-count">
											Total count for active users
										</p>
										<div className="d-flex align-items-center mx-auto">
											<p className="count">
												<CountUp
													duration={2}
													className="counter"
													end={
														selectedData.service_head
															? selectedData.service_head
															: 0
													}
												/>
											</p>
										</div>
									</div>
								</div>
							</div>
							<div className={`card`}>
								<div className="d-flex align-items-center justify-content-between h-100 w-100">
									<div className=" h-100 w-100">
										<p className="name">Service Engineer</p>
										<p className="mt-auto total-count">
											Total count for active users
										</p>
										<div className="d-flex align-items-center mx-auto">
											<p className="count">
												<CountUp
													duration={2}
													className="counter"
													end={
														selectedData.service_engineer
															? selectedData.service_engineer
															: 0
													}
												/>
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<p className="mb-15 mt-0 main-heading">Fabricators</p>
						<div className="d-flex gap-3">
							<div className={`card`}>
								<div className="d-flex align-items-center justify-content-between h-100 w-100">
									<div className=" h-100 w-100">
										<p className="name">Fabricators</p>
										<p className="mt-auto total-count">
											Total count for active users
										</p>
										<div className="d-flex align-items-center mx-auto">
											<p className="count">
												<CountUp
													duration={2}
													className="counter"
													end={
														selectedData.fabricators
															? selectedData.fabricators
															: 0
													}
												/>
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>{' '}
					<div>
						<p className="mb-15 mt-0 main-heading">Supervisor</p>
						<div className="d-flex gap-3">
							<div className={`card`}>
								<div className="d-flex align-items-center justify-content-between h-100 w-100">
									<div className="h-100 w-100">
										<p className="name">Existing</p>
										<p className="mt-auto total-count">
											Total count for active users
										</p>
										<div className="d-flex align-items-center mx-auto">
											<p className="count">
												<CountUp
													duration={2}
													className="counter"
													end={
														selectedData?.existing?.supervisors
															? selectedData?.existing?.supervisors
															: 0
													}
												/>
											</p>
										</div>
									</div>
								</div>
							</div>
							<div className={`card`}>
								<div className="d-flex align-items-center justify-content-between h-100 w-100">
									<div className="h-100 w-100">
										<p className="name">Freelancer</p>
										<p className="mt-auto total-count">
											Total count for active users
										</p>
										<div className="d-flex align-items-center mx-auto">
											<p className="count">
												<CountUp
													duration={2}
													className="counter"
													end={
														selectedData?.existing?.freelancer
															? selectedData?.existing?.freelancer
															: 0
													}
												/>
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	};

	return (
		<DashboardWrapper>
			<PageTitle title="sidebar.reports" className="plr-0" />

			<div className="plr-0">
				<Loader loading={loading} />
				<div className="row">
					<div className="col-md-12">
						<div className="d-flex align-items-center justify-content-between">
							{/* <p className="mb-0 mt-0 main-heading">User roles</p> */}
							<div className="ms-auto pills mt-0">
								<div className={`active-slider ${pills}`}></div>
								<span
									className={`${pills === 'East' ? 'active' : ''}`}
									onClick={() => setPills('East')}>
									East
								</span>
								<span
									className={`${pills === 'West' ? 'active' : ''}`}
									onClick={() => setPills('West')}>
									West
								</span>
								<span
									className={`${pills === 'South' ? 'active' : ''}`}
									onClick={() => setPills('South')}>
									South
								</span>
								<span
									className={`${pills === 'North' ? 'active' : ''}`}
									onClick={() => setPills('North')}>
									North
								</span>
							</div>
						</div>
						<DataDisplay data={data} />
						<div className="row g-3 mt-0">
							{data[pills] && data[pills].length > 0
								? data[pills].map((items, index) => (
										<div className="col-md-3" key={index}>
											<div className={`card bg-${index}`}>
												<div className="d-flex align-items-center justify-content-between">
													<div>
														<p className="d-flex align-items-center name">
															{items.zone_name}{' '}
															<span className="zone">
																Zone wise count
															</span>
														</p>
														<p className="count">
															<CountUp
																duration={2}
																className="counter"
																end={items.user_count}
															/>
														</p>
													</div>
													<div className="image-card">
														{items.zone_name === 'North' ? (
															<img
																src={north}
																alt={items.zone_name}
															/>
														) : null}
														{items.zone_name === 'South' ? (
															<img
																src={south}
																alt={items.zone_name}
															/>
														) : null}
														{items.zone_name === 'West' ? (
															<img src={west} alt={items.zone_name} />
														) : null}
														{items.zone_name === 'East' ? (
															<img src={east} alt={items.zone_name} />
														) : null}
													</div>
												</div>
											</div>
										</div>
								  ))
								: null}
						</div>
					</div>
				</div>
			</div>
			<Toaster ref={toaster} />
		</DashboardWrapper>
	);
};

export default Dashboard;
