/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import {
	Input,
	ModalBody,
	ModalFooter,
	ModalHeader,
	Label,
	Form,
	FormGroup,
	Accordion,
	AccordionItem,
	AccordionBody,
	AccordionHeader
} from 'reactstrap';
import Button from 'src/components/button/Button';
import ReactQuill from 'react-quill';
import { AddOrEditModalWrapper, StatusWrapper } from '../Feedback.style';

const ViewModalFeedback = (props) => {
	const [openAccordion, setOpenAccordion] = useState('video_1');
	const toggleAccordion = (id) => {
		if (openAccordion === id) {
			setOpenAccordion();
		} else {
			setOpenAccordion(id);
		}
	};

	const handleCLoseModal = props.handleChangeViewModal({ open: false, data: null });

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<ModalHeader toggle={handleCLoseModal}>View Feedback</ModalHeader>

				<ModalBody>
					{(props.data || !props?.edit) && (
						<>
							<Accordion open={openAccordion} toggle={toggleAccordion}>
								<AccordionItem>
									<AccordionHeader targetId="video_1">
										Feedback Details
									</AccordionHeader>
									<AccordionBody accordionId="video_1">
										{props.data.user_data && (
											<div className="mb-10 text-capitalize">
												<strong>Name:</strong>{' '}
												{props.data.user_data.first_name}{' '}
												{props.data.user_data.last_name}
											</div>
										)}
										{props.data.user_data.mobile && (
											<div className="mb-10 text-capitalize">
												<strong>Mobile:</strong>{' '}
												{props.data.user_data.country_code}{' '}
												{props.data.user_data.mobile}
											</div>
										)}
										{props.data.user_data.email && (
											<div className="mb-10">
												<strong>Email:</strong> {props.data.user_data.email}
											</div>
										)}
										{props.data.user_data.user_role.Role.name && (
											<div className="mb-10 text-capitalize">
												<strong>Role:</strong>{' '}
												{props.data.user_data.user_role.Role.name}
											</div>
										)}
										{props.data.module_data.title && (
											<div className="mb-10 text-capitalize">
												<strong>Module:</strong>{' '}
												{props.data.module_data.title}
											</div>
										)}
										{props.data.feedback_status.title && (
											<div className="mb-10 text-capitalize d-flex align-items-center gap-2">
												<strong>Status:</strong>{' '}
												<StatusWrapper>
													<div
														className={`status id-${props.data?.feedback_status?.id}`}>
														{props.data?.feedback_status?.title}
													</div>
												</StatusWrapper>
											</div>
										)}
										{props.data.description && (
											<div className="mb-10 mt-10">
												<strong>Feedback:</strong>{' '}
												<div className="mt-10">
													<Input
														type="textarea"
														placeholder=""
														value={props.data.description}
														readOnly={true}
														rows={10}
														className="form-control react-form-input"
													/>
												</div>
											</div>
										)}
									</AccordionBody>
								</AccordionItem>
							</Accordion>
						</>
					)}
				</ModalBody>

				<ModalFooter>
					<div>
						{props.data && !props.edit && (
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button"
								style={{ backgroundColor: '#6c757d' }}>
								Cancel
							</Button>
						)}
					</div>
				</ModalFooter>
			</AddOrEditModalWrapper>
		</>
	);
};

export default ViewModalFeedback;
