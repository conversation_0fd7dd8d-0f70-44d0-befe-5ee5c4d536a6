/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader, Label, Form, FormGroup } from 'reactstrap';
import Button from '../../../components/button/Button';
import { AddOrEditModalWrapper } from '../Feedback.style';
import SingleDropdown from 'src/components/SingleDropdown/SingleDropdown';
import Loader from 'src/components/common/Loader';
import Swal from 'sweetalert2';
import { CONFIRM_CHANGE_STATUS } from 'src/components/header/constants';

const AddOrEditVideoTraining = (props) => {
	const [status, setStatus] = useState([
		{
			id: 1,
			name: 'Open',
			is_active: true,
			is_deleted: false
		},
		{
			id: 2,
			name: 'Closed',
			is_active: true,
			is_deleted: false
		},
		{
			id: 3,
			name: 'Hold',
			is_active: true,
			is_deleted: false
		}
	]);
	const [selectedStatus, setSelectedStatus] = useState(null);
	const [saveLoading, setSaveLoading] = useState(false);

	useEffect(() => {
		if (props.data) {
			const statusData = {
				...props.data.feedback_status,
				name: props.data.feedback_status.title
			};

			handleChangeStatusDropdown(statusData);
		}
	}, [props.data]);

	const handleCLoseModal = props.handleChangeViewOrEditModal({ open: false, data: null });

	const handleChangeStatusDropdown = (items) => {
		setSelectedStatus(items);
	};

	const handleSubmit = async (event) => {
		try {
			event.preventDefault();
			setSaveLoading(true);
			if (selectedStatus.name.length > 0) {
				Swal.fire(CONFIRM_CHANGE_STATUS).then((result) => {
					if (result.isConfirmed) {
						handleCLoseModal();

						const obj = {
							feedback_id: props.data.id,
							feedback_status_id: selectedStatus.id
						};

						props.editFeedback(obj);
						return;
					}
				});
			}
		} catch (error) {
			setSaveLoading(false);
		} finally {
			setSaveLoading(false);
		}
	};

	return (
		<>
			<AddOrEditModalWrapper
				isOpen={props.open}
				centered
				toggle={handleCLoseModal}
				style={{ width: '100%', maxWidth: '650px' }}
				backdrop={'static'}>
				<Loader loading={saveLoading} />
				<ModalHeader toggle={handleCLoseModal}>
					{props.edit ? 'Edit' : 'Add'} Feedback Status
				</ModalHeader>

				<Form onSubmit={handleSubmit}>
					<ModalBody>
						{(props.data || !props?.edit) && (
							<>
								<FormGroup>
									<Label>Status</Label>
									<SingleDropdown
										data={status}
										keyProps={['name']}
										onSelect={handleChangeStatusDropdown}
										selectedData={selectedStatus}
									/>
								</FormGroup>
							</>
						)}
					</ModalBody>

					<ModalFooter>
						<div>
							<Button type="submit" loading={saveLoading} className="btn form-button">
								Save
							</Button>
						</div>
						<div>
							<Button
								onClick={handleCLoseModal}
								type="submit"
								className="btn form-button c-secondary">
								Cancel
							</Button>
						</div>
					</ModalFooter>
				</Form>
			</AddOrEditModalWrapper>
		</>
	);
};

export default AddOrEditVideoTraining;
