/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import PageTitle from 'src/components/common/PageTitle';
import ReactTable from 'react-table';
import { TABLE } from 'src/helper/constant';
import Loader from 'src/components/common/Loader';
import Pagination from 'src/components/Pagination/Pagination';
import EditIcon from 'src/assets/images/Edit.svg';
import { getApi, postApi } from 'src/helper/api/Api';
import { FEEDBACK } from 'src/helper/api/endPoint';
import CODES from 'src/helper/StatusCodes';
import useDebounce from 'src/util/hooks/useDebounce';
import withFixedColumns from 'react-table-hoc-fixed-columns';
import 'react-table-hoc-fixed-columns/lib/styles.css';
import Toaster from 'src/components/common/Toaster';
import { VideoWrapper, StatusWrapper } from './Feedback.style';
import AddOrEditVideoTraining from './Modal/AddOrEditModal';
import ViewIcon from '../../assets/images/View.svg';
import ViewModalFeedback from './Modal/ViewModal';

const ReactTableFixedColumns = withFixedColumns(ReactTable);

const Feedback = () => {
	const toaster = useRef();
	const [data, setData] = useState([]);
	const [loading, setLoading] = useState(false);
	const [activePage, setActivePage] = useState(1);
	const [pages, setPages] = useState(1);
	const [count, setCount] = useState(0);
	const [searchKey, setSearchKey] = useState('');
	const debounceSearch = useDebounce(searchKey, 300);
	const [addOrEditModalData, setAddOrEditModalData] = useState({
		open: false,
		data: null,
		view: true
	});
	const [viewModalData, setViewModalData] = useState({
		open: false,
		data: null,
		view: true
	});

	const columns = [
		{
			Header: 'info',
			columns: [
				{
					Header: 'Feedback',
					resizable: false,
					Cell: (row) => (
						<div className="text-overflow">{row?.original?.description}</div>
					),
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true
				},
				{
					Header: 'Module Title',
					resizable: false,
					Cell: (row) => `${row.original.module_data.title}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					width: 230
				},
				{
					Header: 'User Name',
					resizable: false,
					Cell: (row) =>
						`${
							row.original.user_data.first_name +
							' ' +
							row.original.user_data.last_name
						}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					width: 230
				},
				{
					Header: 'User Role',
					resizable: false,
					Cell: (row) => `${row.original.user_data.user_role.Role.name}`,
					headerClassName: 'text-left pa-20',
					className: 'text-left pa-20 d-flex align-items-center',
					enableFilter: true,
					width: 230
				}
			]
		},
		{
			Header: 'Action',
			fixed: 'right',
			columns: [
				{
					Header: 'Status',
					resizable: false,

					Cell: (cell) => (
						<>
							<StatusWrapper>
								<div className={`status id-${cell?.original?.feedback_status?.id}`}>
									{cell?.original?.feedback_status?.title}
								</div>
							</StatusWrapper>
						</>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				},
				{
					Header: 'Action',
					resizable: false,
					Cell: (cell) => (
						<div className="">
							{cell?.original?.feedback_status?.id !== 2 ? (
								<img
									src={EditIcon}
									alt="EditIcon"
									title="View"
									width={23}
									className="mr-10 cursor-pointer"
									onClick={handleChangeViewOrEditModal({
										open: true,
										view: false,
										edit: true,
										data: cell.original
									})}
								/>
							) : null}
							<img
								src={ViewIcon}
								alt="ViewIcon"
								title="View"
								width={23}
								className="mr-10 cursor-pointer"
								onClick={handleChangeViewModal({
									open: true,
									data: cell.original,
									view: true
								})}
							/>
						</div>
					),
					headerClassName: 'text-right pa-20',
					className: 'text-right pa-20 d-flex align-items-center justify-content-end',
					width: 120
				}
			]
		}
	];

	useEffect(() => {
		getFeedback();
	}, [debounceSearch, activePage]);

	const handleChangeViewOrEditModal = (params) => () => setAddOrEditModalData(params);

	const handleChangeViewModal = (params) => () => setViewModalData(params);

	const getFeedback = async (id) => {
		try {
			setLoading(true);
			const dataToSend = {
				page: activePage,
				limit: TABLE.LIMIT
			};
			if (debounceSearch.trim()) {
				(dataToSend.page = 1), (dataToSend.search = debounceSearch);
			}
			const response = await getApi(FEEDBACK.GET, {
				...dataToSend
			});
			if (response?.status === CODES.SUCCESS) {
				setData(response?.data?.data?.feedbacks);
				setPages(Math.ceil(response?.data?.data?.totalCount / TABLE.LIMIT));
				setCount(response?.data?.data?.totalCount);
			}
		} catch (error) {
			setData([]);
			setPages(1);
			setCount(0);
			setLoading(false);
		} finally {
			setLoading(false);
		}
	};

	const editFeedback = async (data) => {
		try {
			setLoading(true);
			const response = await postApi(FEEDBACK.UPDATE, data, '');
			if (response?.status === CODES.SUCCESS) {
				toaster.current.success(response.data.message);
				getFeedback();
			}
		} catch (error) {
			console.log(error);
			toaster.current.error(error.response.data.message);
		} finally {
			setLoading(false);
		}
	};

	const handleChangePage = (page) => {
		setActivePage(page + 1);
	};

	return (
		<>
			<VideoWrapper>
				<PageTitle
					title="sidebar.feedback"
					search={true}
					searchKey={searchKey}
					setSearchKey={setSearchKey}
				/>
				<div className="p-0 bg-white">
					<div className="roe-card-style mtb-15">
						<div className="roe-card-body">
							<ReactTableFixedColumns
								manual
								data={data}
								pages={pages}
								sortable={false}
								columns={columns}
								page={activePage - 1}
								onPageChange={handleChangePage}
								totalCount={count}
								loading={loading}
								pageSize={TABLE.LIMIT}
								minRows={TABLE.MIN_ROW}
								LoadingComponent={Loader}
								PaginationComponent={Pagination}
								style={{ border: 'none', boxShadow: 'none' }}
								className="-striped -highlight custom-react-table-theme-class"
								defaultFilterMethod={(filter, row) => {
									const id = filter.pivotId || filter.id;
									return row[id] !== undefined
										? String(row[id].toLowerCase()).includes(
												filter.value.toLowerCase()
										  )
										: true;
								}}
							/>
						</div>
					</div>
				</div>
				<Toaster ref={toaster} />
			</VideoWrapper>
			{addOrEditModalData.open && (
				<AddOrEditVideoTraining
					{...addOrEditModalData}
					handleChangeViewOrEditModal={handleChangeViewOrEditModal}
					getFeedback={getFeedback}
					editFeedback={editFeedback}
				/>
			)}
			{viewModalData.open && (
				<ViewModalFeedback
					{...viewModalData}
					handleChangeViewModal={handleChangeViewModal}
				/>
			)}
		</>
	);
};

export default Feedback;
