/* eslint-disable react-refresh/only-export-components */
import { lazy, Suspense } from 'react';
import { Navigate } from 'react-router-dom';
// HELPERS
import { ROUTES } from '../helper/constant';
import EditProfile from 'src/views/edit_profile/EditProfile';
import ChangePassword from 'src/views/change_password/ChangePassword';
import AssignPointsDetails from 'src/views/Points/AssignPointsDetails';
import Reports from 'src/views/Reports/Reports';
import WalletLogs from 'src/views/Supervisor/WalletLogs';
import ZoneWiseHierarchy from 'src/views/MasterData/ZoneWiseHierarchy';
// PAGES
const Layout = lazy(() => import('src/components/Layout/Layout'));
// --PUBLIC
const Login = lazy(() => import('src/views/login/Login'));
const ForgotPasswordLink = lazy(() => import('src/views/forgot_password/ForgotPasswordLink'));
const OtpVerify = lazy(() => import('src/views/forgot_password/OtpVerify'));
const ResetPassword = lazy(() => import('src/views/reset_password/ResetPassword'));
// --PRIVATE
const Dashboard = lazy(() => import('src/views/dashboard/Dashboard'));
const StaticPages = lazy(() => import('src/views/StaticPages/StaticPages'));
const Skills = lazy(() => import('src/views/Skills/Skills'));
const WorkOrderType = lazy(() => import('src/views/WorkOrderType/WorkOrderType'));
const OrderManagement = lazy(() => import('src/views/OrderManagement/OrderManagement'));
const OrderDetails = lazy(() => import('src/views/OrderManagement/OrderDetails'));
const TaskDetails = lazy(() => import('src/views/OrderManagement/TaskDetails'));
const SupervisorRegisteredUser = lazy(() => import('src/views/Supervisor/RegisteredUser'));
const SupervisorVerifiedUser = lazy(() => import('src/views/Supervisor/VerifiedUser'));
const SupervisorRejectedUser = lazy(() => import('src/views/Supervisor/RejectedUser'));
const SupervisorAssignFabricator = lazy(() => import('src/views/Supervisor/AssignFabricator'));
const FabricatorRegisteredUser = lazy(() => import('src/views/Fabricator/RegisteredUser'));
const FabricatorRejectedUser = lazy(() => import('src/views/Fabricator/RejectedUser'));
const FabricatorVerifiedUser = lazy(() => import('src/views/Fabricator/VerifiedUser'));
const AddressApproved = lazy(() => import('src/views/AddressApprove/AddressApproved'));
const AddressRegistered = lazy(() => import('src/views/AddressApprove/AddressRegistered'));
const AddressRejected = lazy(() => import('src/views/AddressApprove/AddressRejected'));
const InternalTeamManagementRegistered = lazy(() =>
	import('src/views/InternalTeamManagement/InternalTeamManagementRegistered')
);
const InternalTeamManagementApproved = lazy(() =>
	import('src/views/InternalTeamManagement/InternalTeamManagementApproved')
);
const InternalTeamManagementRejected = lazy(() =>
	import('src/views/InternalTeamManagement/InternalTeamManagementRejected')
);
const SettingsRejectedReason = lazy(() => import('src/views/Settings/RejectReasons'));

const SettingsSpace = lazy(() => import('src/views/Settings/Space'));
const SettingsScope = lazy(() => import('src/views/Settings/Scope'));
const HandoverQuestions = lazy(() => import('src/views/HandoverQuestions/HandoverQuestions'));
const QuestionTags = lazy(() => import('src/views/Questions/QuestionTags'));
const InstallationSteps = lazy(() => import('src/views/Installation/InstallationSteps'));
const QuestionBank = lazy(() => import('src/views/Questions/QuestionBank'));
const QuestionMapping = lazy(() => import('src/views/Questions/QuestionMapping'));
const AddQuestionMapping = lazy(() => import('src/views/Questions/Add/AddQuestionMapping'));
const EditQuestionMapping = lazy(() => import('src/views/Questions/Edit/EditQuestionMapping'));
const VideoTraining = lazy(() => import('src/views/VideoTraining/VideoTraining'));
const Feedback = lazy(() => import('src/views/Feedback/Feedback'));
const Email = lazy(() => import('src/views/Templates/Email'));
const Notifications = lazy(() => import('src/views/Templates/Notifications'));
const DefineRate = lazy(() => import('src/views/Points/DefineRate'));
const AssignPoints = lazy(() => import('src/views/Points/AssignPoints'));
const Role = lazy(() => import('src/views/Role/Role'));
const Group = lazy(() => import('src/views/Group/Group'));
const UserCreation = lazy(() => import('src/views/UserCreation/UserCreation'));
const Permissions = lazy(() => import('src/views/Permissions/Permissions'));
const AssignPermissions = lazy(() => import('src/views/Permissions/AssignPermissions'));
const TAT = lazy(() => import('src/views/MasterData/TAT'));

/** Add your public routes here */
const publicRoutes = [
	{
		path: '/',
		element: (
			<Suspense fallback={<></>}>
				<Login />
			</Suspense>
		)
	},
	{
		path: ROUTES.LOGIN,
		element: (
			<Suspense fallback={<></>}>
				<Login />
			</Suspense>
		)
	},
	{
		path: ROUTES.PASSWORD.FORGOT_PASSWORD,
		element: (
			<Suspense fallback={<></>}>
				<ForgotPasswordLink />
			</Suspense>
		)
	},
	{
		path: ROUTES.PASSWORD.OTP_VERIFY,
		element: (
			<Suspense fallback={<></>}>
				<OtpVerify />
			</Suspense>
		)
	},
	{
		path: ROUTES.PASSWORD.RESET,
		element: (
			<Suspense fallback={<></>}>
				<ResetPassword />
			</Suspense>
		)
	},
	{ path: '*', element: <Navigate to="/" replace /> }
];

/** Add your private routes here */
const privateRoutes = [
	{
		path: '/',
		element: (
			<Suspense fallback={<></>}>
				<Layout />
			</Suspense>
		),
		children: [
			{
				path: '/',
				element: <Navigate to="/dashboard" replace />
			},
			{
				path: ROUTES.DASHBOARD.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Dashboard />
					</Suspense>
				)
			},
			{
				path: ROUTES.STATIC_PAGES.BASE,
				element: (
					<Suspense fallback={<></>}>
						<StaticPages />
					</Suspense>
				)
			},
			{
				path: ROUTES.SKILLS.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Skills />
					</Suspense>
				)
			},
			{
				path: ROUTES.SETTINGS.WORK_ORDER_TYPE,
				element: (
					<Suspense fallback={<></>}>
						<WorkOrderType />
					</Suspense>
				)
			},
			{
				path: ROUTES.SUPERVISOR.REGISTERED,
				element: (
					<Suspense fallback={<></>}>
						<SupervisorRegisteredUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.SUPERVISOR.APPROVED,
				element: (
					<Suspense fallback={<></>}>
						<SupervisorVerifiedUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.SUPERVISOR.REJECTED,
				element: (
					<Suspense fallback={<></>}>
						<SupervisorRejectedUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.SUPERVISOR.ASSIGN_FABRICATOR,
				element: (
					<Suspense fallback={<></>}>
						<SupervisorAssignFabricator />
					</Suspense>
				)
			},
			{
				path: ROUTES.FABRICATOR.REGISTERED,
				element: (
					<Suspense fallback={<></>}>
						<FabricatorRegisteredUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.FABRICATOR.APPROVED,
				element: (
					<Suspense fallback={<></>}>
						<FabricatorVerifiedUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.FABRICATOR.REJECTED,
				element: (
					<Suspense fallback={<></>}>
						<FabricatorRejectedUser />
					</Suspense>
				)
			},
			{
				path: ROUTES.PROFILE.EDIT,
				element: (
					<Suspense fallback={<></>}>
						<EditProfile />
					</Suspense>
				)
			},
			{
				path: ROUTES.PASSWORD.CHANGE,
				element: (
					<Suspense fallback={<></>}>
						<ChangePassword />
					</Suspense>
				)
			},
			{
				path: ROUTES.INTERNAL_TEAM_MANAGEMENT.REGISTERED,
				element: (
					<Suspense fallback={<></>}>
						<InternalTeamManagementRegistered />
					</Suspense>
				)
			},
			{
				path: ROUTES.INTERNAL_TEAM_MANAGEMENT.APPROVED,
				element: (
					<Suspense fallback={<></>}>
						<InternalTeamManagementApproved />
					</Suspense>
				)
			},
			{
				path: ROUTES.INTERNAL_TEAM_MANAGEMENT.REJECTED,
				element: (
					<Suspense fallback={<></>}>
						<InternalTeamManagementRejected />
					</Suspense>
				)
			},
			{
				path: ROUTES.ADDRESS_APPROVE.REGISTERED,
				element: (
					<Suspense fallback={<></>}>
						<AddressRegistered />
					</Suspense>
				)
			},
			{
				path: ROUTES.ADDRESS_APPROVE.APPROVED,
				element: (
					<Suspense fallback={<></>}>
						<AddressApproved />
					</Suspense>
				)
			},
			{
				path: ROUTES.ADDRESS_APPROVE.REJECTED,
				element: (
					<Suspense fallback={<></>}>
						<AddressRejected />
					</Suspense>
				)
			},
			{
				path: ROUTES.SETTINGS.REJECTED_REASON,
				element: (
					<Suspense fallback={<></>}>
						<SettingsRejectedReason />
					</Suspense>
				)
			},
			{
				path: ROUTES.SETTINGS.SCOPE,
				element: (
					<Suspense fallback={<></>}>
						<SettingsScope />
					</Suspense>
				)
			},
			{
				path: ROUTES.SETTINGS.SPACE,
				element: (
					<Suspense fallback={<></>}>
						<SettingsSpace />
					</Suspense>
				)
			},
			{
				path: ROUTES.ORDER_MANAGEMENT.BASE,
				element: (
					<Suspense fallback={<></>}>
						<OrderManagement />
					</Suspense>
				)
			},
			{
				path: ROUTES.ORDER_MANAGEMENT.DETAILS,
				element: (
					<Suspense fallback={<></>}>
						<OrderDetails />
					</Suspense>
				)
			},
			{
				path: ROUTES.ORDER_MANAGEMENT.TASK_DETAILS,
				element: (
					<Suspense fallback={<></>}>
						<TaskDetails />
					</Suspense>
				)
			},
			{
				path: ROUTES.HANDOVER_QUESTIONS.BASE,
				element: (
					<Suspense fallback={<></>}>
						<HandoverQuestions />
					</Suspense>
				)
			},
			{
				path: ROUTES.HANDOVER_QUESTIONS.ADD,
				element: (
					<Suspense fallback={<></>}>
						<QuestionTags />
					</Suspense>
				)
			},
			{
				path: ROUTES.QUESTION_BANK.TAGS,
				element: (
					<Suspense fallback={<></>}>
						<QuestionTags />
					</Suspense>
				)
			},
			{
				path: ROUTES.INSTALLATION_STEPS.BASE,
				element: (
					<Suspense fallback={<></>}>
						<InstallationSteps />
					</Suspense>
				)
			},
			{
				path: ROUTES.QUESTION_BANK.BASE,
				element: (
					<Suspense fallback={<></>}>
						<QuestionBank />
					</Suspense>
				)
			},
			{
				path: ROUTES.QUESTION_BANK.MAPPING,
				element: (
					<Suspense fallback={<></>}>
						<QuestionMapping />
					</Suspense>
				)
			},
			{
				path: ROUTES.QUESTION_BANK.ADD,
				element: (
					<Suspense fallback={<></>}>
						<AddQuestionMapping />
					</Suspense>
				)
			},
			{
				path: ROUTES.QUESTION_BANK.EDIT,
				element: (
					<Suspense fallback={<></>}>
						<EditQuestionMapping />
					</Suspense>
				)
			},
			{
				path: ROUTES.VIDEO_TRAINING.BASE,
				element: (
					<Suspense fallback={<></>}>
						<VideoTraining />
					</Suspense>
				)
			},
			{
				path: ROUTES.FEEDBACK.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Feedback />
					</Suspense>
				)
			},
			{
				path: ROUTES.TEMPLATES.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Email />
					</Suspense>
				)
			},
			{
				path: ROUTES.TEMPLATES.NOTIFICATIONS,
				element: (
					<Suspense fallback={<></>}>
						<Notifications />
					</Suspense>
				)
			},
			{
				path: ROUTES.POINTS.DEFINE,
				element: (
					<Suspense fallback={<></>}>
						<DefineRate />
					</Suspense>
				)
			},
			{
				path: ROUTES.POINTS.ASSIGN,
				element: (
					<Suspense fallback={<></>}>
						<AssignPoints />
					</Suspense>
				)
			},
			{
				path: ROUTES.POINTS.DETAILS,
				element: (
					<Suspense fallback={<></>}>
						<AssignPointsDetails />
					</Suspense>
				)
			},
			{
				path: ROUTES.REPORTS.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Reports />
					</Suspense>
				)
			},
			{
				path: ROUTES.SUPERVISOR.WALLET_LOGS,
				element: (
					<Suspense fallback={<></>}>
						<WalletLogs />
					</Suspense>
				)
			},
			{
				path: ROUTES.ROLE.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Role />
					</Suspense>
				)
			},
			{
				path: ROUTES.GROUP.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Group />
					</Suspense>
				)
			},
			{
				path: ROUTES.USER_CREATION.BASE,
				element: (
					<Suspense fallback={<></>}>
						<UserCreation />
					</Suspense>
				)
			},
			{
				path: ROUTES.PERMISSION.BASE,
				element: (
					<Suspense fallback={<></>}>
						<Permissions />
					</Suspense>
				)
			},
			{
				path: ROUTES.PERMISSION.ASSIGN,
				element: (
					<Suspense fallback={<></>}>
						<AssignPermissions />
					</Suspense>
				)
			},
			{
				path: ROUTES.MASTER_DATA.TAT,
				
				element: (
					<Suspense fallback={<></>}>
						<TAT />
					</Suspense>
				)
			},
			{
				path: ROUTES.MASTER_DATA.ZONE_WISE_HIERARCHY,
				element: (
					<Suspense fallback={<></>}>
						<ZoneWiseHierarchy />
					</Suspense>
				)
			},
			{ path: '*', element: <Navigate to="/" replace /> }
		]
	}
];

export { privateRoutes, publicRoutes };
