import { Provider } from 'react-redux';
import { createRoot } from 'react-dom/client';
import { ThemeProvider } from 'styled-components';
import { PersistGate } from 'redux-persist/integration/react';
// STYLES
// import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-table/react-table.css';
// import 'react-quill/dist/quill.snow.css';
import './assets/css/custom-quill.snow.css';
import './assets/scss/app.scss';
import './index.css';
// SETTINGS
import themes from 'src/settings/themes';
import { themeConfig } from 'src/settings';
// REDUX
import { persistor, store } from 'src/redux/store';
// COMPONENTS
import App from 'src/App';

if (import.meta.env.PROD) {
	console.log = () => {};
	console.error = () => {};
	console.debug = () => {};
}

createRoot(document.getElementById('root')).render(
	<Provider store={store}>
		<PersistGate persistor={persistor}>
			<ThemeProvider theme={themes[themeConfig.theme]}>
				<App />
			</ThemeProvider>
		</PersistGate>
	</Provider>
);
