import { createBrowserHistory } from 'history';
import { <PERSON><PERSON>erRout<PERSON> } from 'react-router-dom';
import { IntlProvider } from 'react-intl';
// SETTINGS
import AppLocale from './languageProvider';
import config, { getCurrentLanguage } from './settings/languageConfig';
// ROUTER
import Router from 'src/Routes/Router';

const currentAppLocale = AppLocale[getCurrentLanguage(config.defaultLanguage || 'english').locale];

const history = createBrowserHistory({ basename: import.meta.env.VITE_REACT_APP_BASE_NAME });

function App() {
	return (
		<IntlProvider locale={currentAppLocale.locale} messages={currentAppLocale.messages}>
			<BrowserRouter history={history}>
				<Router />
			</BrowserRouter>
		</IntlProvider>
	);
}

export default App;
