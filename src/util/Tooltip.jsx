import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

const TooltipContainer = styled.div`
	display: inline-block;
	/* width: 100%; */
	position: relative;
	cursor: pointer;
`;

const TooltipText = styled.div`
	position: fixed !important;
	padding: 10px;
	background-color: #333;
	color: #fff;
	border-radius: 10px;
	width: auto;
	max-width: 100%;
	opacity: 1;
	transition: opacity 1s ease;
	z-index: 9999;
	top: 0px;
	left: ${(props) => props.tooltipLeft}px;
`;

const Tooltip = ({ text, position, children }) => {
	const [isHovered, setIsHovered] = useState(false);

	const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

	const handleMouseEnter = (e) => {
		setIsHovered(true);
	};
	const handleMouseLeave = () => {
		setIsHovered(false);
	};

	const handleMouseMove = (e) => {
		const { clientX, clientY } = e;
		setTooltipPosition({ top: clientY, left: clientX });
	};

	useEffect(() => {
		if (isHovered) {
			window.addEventListener('mousemove', handleMouseMove);
		} else {
			window.removeEventListener('mousemove', handleMouseMove);
		}

		return () => {
			window.removeEventListener('mousemove', handleMouseMove);
		};
	}, [isHovered]);

	return (
		<TooltipContainer onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
			{children}
			{isHovered && (
				<TooltipText tooltipTop={tooltipPosition.top} tooltipLeft={tooltipPosition.left}>
					{text}
				</TooltipText>
			)}
		</TooltipContainer>
	);
};

export default Tooltip;
