/* export function getColumnSearchProps(dataIndex) {
	let searchInput = null;

	return {
		filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
			<div style={{ padding: 8 }}>
				<Input
					ref={(node) => {
						searchInput = node;
					}}
					placeholder={`Search`}
					value={selectedKeys[0]}
					onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
					onPressEnter={() => {
						confirm();
					}}
					style={{ width: 188, marginBottom: 8, display: 'block' }}
				/>
				<div>
					<Button
						type="primary"
						onClick={() => {
							confirm({ closeDropdown: true });
						}}
						// icon={<SearchOutlinen />}
						size="small"
						style={{ width: 90 }}>
						Search
					</Button>
					<Button
						onClick={() => clearFilters && handleReset(clearFilters, confirm)}
						size="small"
						style={{ width: 90 }}>
						Reset
					</Button>
				</div>
			</div>
		),
		filterIcon: (filtered) => (
			// <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
		),
		onFilter: (value, record) =>
			record[dataIndex]
				? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
				: '',
		onFilterDropdownVisibleChange: (visible) => {
			if (visible) {
				setTimeout(() => searchInput.select(), 100);
			}
		},
		render: (text) => text
	};
} */

// remove this

// export const ColumnFilter = ({ column }) => {
//   const { setFilter } = column;
//   return (
//     <input
//       type="text"
//       placeholder={`Search ${column.Header}`}
//       onChange={(e) => setFilter(e.target.value)}
//     />
//   );
// };
