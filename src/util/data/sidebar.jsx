import { ROUTES } from '../../helper/constant';
import DashboardIcon from 'src/assets/images/Sidebar/Dashboard.svg';
import PagesIcon from 'src/assets/images/Sidebar/Pages.svg';
import SkillsIcon from 'src/assets/images/Sidebar/Skills.svg';
import PeopleIcon from 'src/assets/images/Sidebar/People.svg';
import TeamIcon from 'src/assets/images/Sidebar/Team-group.svg';
import settingIcon from 'src/assets/images/Sidebar/settings.svg';
import SupervisorIcon from 'src/assets/images/Sidebar/supervision.svg';
import AddressApprove from 'src/assets/images/Sidebar/address-approve.svg';
import WorkOrder from 'src/assets/images/Sidebar/WorkOrder.svg';
import QuestionBank from 'src/assets/images/Sidebar/Question.svg';
import VideoIcon from 'src/assets/images/Sidebar/Video.svg';
import VerifiedUser from 'src/assets/images/Sidebar/verified-user.svg';
import RejectedUser from 'src/assets/images/Sidebar/rejected-user.svg';
import RegisteredUser from 'src/assets/images/Sidebar/registered-user.svg';
import QuestionSet from 'src/assets/images/Sidebar/questions-set.svg';
import QuestionMapping from 'src/assets/images/Sidebar/question-mapping.svg';
import RejectReason from 'src/assets/images/Sidebar/rejected-reason.svg';
import SpaceIcon from 'src/assets/images/Sidebar/spaces2.svg';
import ScopeIcon from 'src/assets/images/Sidebar/scope.svg';
import WorkOrderType from 'src/assets/images/Sidebar/work-order-type.svg';
import feedbackIcon from 'src/assets/images/Sidebar/feedback.svg';
import userRoleIcon from 'src/assets/images/Sidebar/user-role.svg';
import groupIcon from 'src/assets/images/Sidebar/group-icon.svg';
import userCreationIcon from 'src/assets/images/Sidebar/user-creation.svg';
import permissionIcon from 'src/assets/images/Sidebar/permission.svg';
import createPermissionIcon from 'src/assets/images/Sidebar/create-permission.svg';
import assignPermissionIcon from 'src/assets/images/Sidebar/assign-permission.svg';
import templatesIcon from 'src/assets/images/Sidebar/document.svg';
import emailIcon from 'src/assets/images/Sidebar/email.svg';
import notificationsIcon from 'src/assets/images/Sidebar/notifications.svg';
import pointsIcon from 'src/assets/images/Sidebar/points.svg';
import definePointsIcon from 'src/assets/images/Sidebar/define-points.svg';
import assignPointsIcon from 'src/assets/images/Sidebar/assign-points.svg';
import ReportsIcon from 'src/assets/images/Sidebar/reports.svg';
import installationSteps from 'src/assets/images/Sidebar/reports.svg';
import MasterDataIcon from 'src/assets/images/Sidebar/master-data.svg';
import TATIcon from 'src/assets/images/Sidebar/tat.svg';
import ZoneHierarchyIcon from 'src/assets/images/Sidebar/zone-hierarchy.svg';

export const sidebarData = [
	{
		name: 'sidebar.dashboard',
		icon: DashboardIcon,
		routepath: ROUTES.REPORTS.BASE
	},
	{
		name: 'sidebar.reports',
		icon: ReportsIcon,
		routepath: ROUTES.DASHBOARD.BASE
	},
	{
		name: 'sidebar.skills',
		icon: SkillsIcon,
		routepath: ROUTES.SKILLS.BASE
	},
	{
		name: 'sidebar.internalTeam',
		icon: TeamIcon,
		child: [
			{
				listname: 'sidebar.registerUser',
				routepath: ROUTES.INTERNAL_TEAM_MANAGEMENT.REGISTERED,
				shortname: '',
				icon: RegisteredUser
			},
			{
				listname: 'sidebar.verifiedUser',
				routepath: ROUTES.INTERNAL_TEAM_MANAGEMENT.APPROVED,
				shortname: '',
				icon: VerifiedUser
			},
			{
				listname: 'sidebar.rejectedUser',
				routepath: ROUTES.INTERNAL_TEAM_MANAGEMENT.REJECTED,
				shortname: '',
				icon: RejectedUser
			}
		]
	},
	{
		name: 'sidebar.fabricator',
		icon: PeopleIcon,
		child: [
			{
				listname: 'sidebar.registerUser',
				routepath: ROUTES.FABRICATOR.REGISTERED,
				shortname: '',
				icon: RegisteredUser
			},
			{
				listname: 'sidebar.verifiedUser',
				routepath: ROUTES.FABRICATOR.APPROVED,
				shortname: '',
				icon: VerifiedUser
			},
			{
				listname: 'sidebar.rejectedUser',
				routepath: ROUTES.FABRICATOR.REJECTED,
				shortname: '',
				icon: RejectedUser
			}
		]
	},
	{
		name: 'sidebar.supervisor',
		icon: SupervisorIcon,
		child: [
			{
				listname: 'sidebar.registerUser',
				routepath: '/supervisor/registeredUser',
				shortname: '',
				icon: RegisteredUser
			},
			{
				listname: 'sidebar.verifiedUser',
				routepath: '/supervisor/verifiedUser',
				shortname: '',
				icon: VerifiedUser
			},
			{
				listname: 'sidebar.rejectedUser',
				routepath: '/supervisor/rejectedUser',
				shortname: '',
				icon: RejectedUser
			}
		]
	},
	{
		name: 'sidebar.addressApprove',
		icon: AddressApprove,
		child: [
			{
				listname: 'sidebar.registerUser',
				routepath: ROUTES.ADDRESS_APPROVE.REGISTERED,
				shortname: '',
				icon: RegisteredUser
			},
			{
				listname: 'sidebar.verifiedUser',
				routepath: ROUTES.ADDRESS_APPROVE.APPROVED,
				shortname: '',
				icon: VerifiedUser
			},
			{
				listname: 'sidebar.rejectedUser',
				routepath: ROUTES.ADDRESS_APPROVE.REJECTED,
				shortname: '',
				icon: RejectedUser
			}
		]
	},
	{
		name: 'sidebar.orderManagement',
		icon: WorkOrder,
		routepath: ROUTES.ORDER_MANAGEMENT.BASE
	},
	{
		name: 'sidebar.staticpages',
		icon: PagesIcon,
		routepath: ROUTES.STATIC_PAGES.BASE
	},
	{
		name: 'sidebar.handoverQuestions',
		icon: QuestionBank,
		routepath: ROUTES.HANDOVER_QUESTIONS.BASE
	},
	{
		name: 'sidebar.installationSteps',
		icon: installationSteps,
		routepath: ROUTES.INSTALLATION_STEPS.BASE
	},
	{
		name: 'sidebar.questions',
		icon: QuestionBank,
		child: [
			{
				listname: 'sidebar.questionTags',
				routepath: ROUTES.QUESTION_BANK.TAGS,
				shortname: '',
				icon: QuestionSet
			},
			{
				listname: 'sidebar.questionBank',
				routepath: ROUTES.QUESTION_BANK.BASE,
				shortname: '',
				icon: QuestionSet
			},
			{
				listname: 'sidebar.questionMapping',
				routepath: ROUTES.QUESTION_BANK.MAPPING,
				shortname: '',
				icon: QuestionMapping
			}
		]
	},
	{
		name: 'sidebar.videoTraining',
		icon: VideoIcon,
		routepath: ROUTES.VIDEO_TRAINING.BASE
	},
	{
		name: 'sidebar.feedback',
		icon: feedbackIcon,
		routepath: ROUTES.FEEDBACK.BASE
	},
	{
		name: 'sidebar.role',
		icon: userRoleIcon,
		routepath: ROUTES.ROLE.BASE
	},
	{
		name: 'sidebar.group',
		icon: groupIcon,
		routepath: ROUTES.GROUP.BASE
	},
	{
		name: 'sidebar.userCreation',
		icon: userCreationIcon,
		routepath: ROUTES.USER_CREATION.BASE
	},
	{
		name: 'sidebar.permissions',
		icon: permissionIcon,
		child: [
			{
				listname: 'sidebar.create',
				routepath: ROUTES.PERMISSION.BASE,
				shortname: '',
				icon: createPermissionIcon
			},
			{
				listname: 'sidebar.assign',
				routepath: ROUTES.PERMISSION.ASSIGN,
				shortname: '',
				icon: assignPermissionIcon
			}
		]
	},
	{
		name: 'sidebar.templates',
		icon: templatesIcon,
		child: [
			{
				listname: 'sidebar.email',
				routepath: ROUTES.TEMPLATES.BASE,
				shortname: '',
				icon: emailIcon
			},
			{
				listname: 'sidebar.notifications',
				routepath: ROUTES.TEMPLATES.NOTIFICATIONS,
				shortname: '',
				icon: notificationsIcon
			}
		]
	},
	{
		name: 'sidebar.points',
		icon: pointsIcon,
		child: [
			{
				listname: 'sidebar.defineRate',
				routepath: ROUTES.POINTS.DEFINE,
				shortname: '',
				icon: definePointsIcon
			},
			{
				listname: 'sidebar.assignPoints',
				routepath: ROUTES.POINTS.ASSIGN,
				shortname: '',
				icon: assignPointsIcon
			}
		]
	},
	{
		name: 'sidebar.masterData',
		icon: MasterDataIcon,
		child: [
			{
				listname: 'sidebar.tat',
				routepath: ROUTES.MASTER_DATA.TAT,
				shortname: '',
				icon: TATIcon
			},
			{
				listname: 'sidebar.zoneWiseHierarchy',
				routepath: ROUTES.MASTER_DATA.ZONE_WISE_HIERARCHY,
				shortname: '',
				icon: ZoneHierarchyIcon
			}
		]
	},
	{
		name: 'sidebar.settings',
		icon: settingIcon,
		child: [
			{
				listname: 'sidebar.rejectReason',
				routepath: ROUTES.SETTINGS.REJECTED_REASON,
				shortname: '',
				icon: RejectReason
			},
			{
				listname: 'sidebar.space',
				routepath: ROUTES.SETTINGS.SPACE,
				shortname: '',
				icon: SpaceIcon
			},
			{
				listname: 'sidebar.scope',
				routepath: ROUTES.SETTINGS.SCOPE,
				shortname: '',
				icon: ScopeIcon
			},
			{
				listname: 'sidebar.workOrderType',
				routepath: ROUTES.SETTINGS.WORK_ORDER_TYPE,
				shortname: '',
				icon: WorkOrderType
			}
		]
	}
];

// verified and registered
// Comments:::::::

//  If you want one level child then look below example

/*
  {
	name: 'sidebar.forms',
	iconClass: 'fab fa-wpforms',
	child: [
	  {
		listname: 'sidebar.regularforms',
		routepath: '/regularform',
		shortname: 'RF'
	  }
	]
  }
*/

//  If you want Second level child then look below example

/*
   {
	  name: 'sidebar.pages',
	  iconClass: 'fas fa-print',
	  child: [
		{
		  listname: 'sidebar.authentication',
		  iconClass: 'fas fa-user',
		  child: [
			{
			  listname: 'sidebar.login',
			  routepath: '/login',
			  shortname: 'L'
			},
		  ]
		}
	  ]
	}
*/
