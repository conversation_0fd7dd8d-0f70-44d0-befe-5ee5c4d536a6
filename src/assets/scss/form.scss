.form-container {
    box-shadow: 0 6px 6px -3px rgba(0, 0, 0, .2), 0 10px 14px 1px rgba(0, 0, 0, .14), 0 4px 18px 3px rgba(0, 0, 0, .12) !important;
    border-radius: 12px;
    background-color: white;
    width: 320px;
    margin: 6% auto;

    .login-title {
        font-size: 24px;
        line-height: 32px;
        font-weight: 400;
        letter-spacing: normal !important;
        text-align: center;
    }

    .link-label {
        font-weight: bold;
        font-size: 14px;
        margin-top: 10px;
        cursor: pointer;
        color: #76B828;

        &:hover {
            text-decoration: underline
        }
    }

    .login-icon {
        padding: 16px 0;
        text-align: center;
    }

    .form-button {
        font-size: 18px;
        text-transform: capitalize;
        box-shadow: none !important;
        width: 100%;
        border-radius: 2px;
        font-weight: 500;
        background: #00c486;
        color: white
    }

    .btn:hover {
        color: white
    }

    .form-info-text {
        color: #677484;
        font-size: 15px;
    }

    .register-privacy-text {
        font-size: 14px;
    }

    .lock-screen-profile {
        padding-bottom: 0px;

        img {
            border-radius: 50%;
        }
    }
}

.react-form-input {
    border-radius: 6px;
    font-size: 14px !important;
    border: 1px solid #ddd !important;

    &:focus {
        border: 1px solid #9a9a9a !important;
        box-shadow: none !important;
    }
}

.react-form-search-input {
    font-size: 14px !important;
    border: 1px solid #ddd !important;

    &:focus {
        border: 1px solid #9a9a9a !important;
        box-shadow: none !important;
    }
}

.static-control-input {
    border-radius: 6px !important;
    font-size: 14px !important;

    // border: 1px solid #ddd !important;
    &:focus {
        outline: 0;
        box-shadow: none !important;
    }
}

.form-button {
    font-size: 18px;
    text-transform: capitalize;
    box-shadow: none !important;
    width: 100%;
    border-radius: 2px;
    font-weight: 500;
    background: #00c486;
    color: white
}

.error-msg {
    height: 16px;
    display: block;
    font-size: 12px;
    color: red;
    font-weight: 600;
}

.page-home-button {
    color: #fff;
    cursor: pointer;
    padding: 20px 25px;
    border-radius: 2px;
    font-size: 14px;
    border: 0;
    float: right;
    background-color: transparent;

    &:hover {
        background-color: rgba(0, 0, 0, .12);
    }
}
